<script lang="ts">
	import InputDocument from '$lib/coms-form/InputDocument.svelte';
	import type { TAddress, TDocumentSetting, TFields } from '$lib/type';
	import KhDateTime from '$lib/coms-document/KhDateInput.svelte';
	import { khmerDate } from '$lib/helper';
	import Header from '$lib/coms-document/Header.svelte';
	interface Prop {
		p_name: string;
		p_nation: string;
		p_address?: TAddress;
		p_date_checkup: string;
		fields: TFields[];
		title_khm: string;
		title_eng: string;
		logo: string;
		get_document_setting?: TDocumentSetting;
	}
	let {
		p_name,
		p_nation,
		p_address: address,
		get_document_setting,
		fields,
		logo,
		p_date_checkup,
		title_eng,
		title_khm
	}: Prop = $props();
	let p_address = $derived(
		`${address?.village?.type ?? ''} ${address?.village?.name_khmer ?? ''} ${address?.commune?.type ?? ''} ${address?.commune?.name_khmer ?? ''} ${address?.district?.type ?? ''} ${address?.district?.name_khmer ?? ''} ${address?.provice?.type ?? ''} ${address?.provice?.name_khmer ?? ''}`
	);
	let blood_pressure = $derived(fields.find((e) => e.name === 'blood_pressure')?.result ?? '');
	let heart_rate = $derived(fields.find((e) => e.name === 'heart_rate')?.result ?? '');
	let respiratory_rate = $derived(fields.find((e) => e.name === 'respiratory_rate')?.result ?? '');
	let temperature = $derived(fields.find((e) => e.name === 'temperature')?.result ?? '');
	let spo2 = $derived(fields.find((e) => e.name === 'spo2')?.result ?? '');
	let reason_for_admission = $derived(
		fields.find((e) => e.name === 'reason_for_admission')?.result ?? ''
	);
	let relevant_medical_history = $derived(
		fields.find((e) => e.name === 'relevant_medical_history')?.result ?? ''
	);
	let clinical_examination = $derived(
		fields.find((e) => e.name === 'clinical_examination')?.result ?? ''
	);
	let para_clinical_examination = $derived(
		fields.find((e) => e.name === 'para_clinical_examination')?.result ?? ''
	);
	let primary_diagnosis = $derived(
		fields.find((e) => e.name === 'primary_diagnosis')?.result ?? ''
	);
	let treatment = $derived(fields.find((e) => e.name === 'treatment')?.result ?? '');
	let reason_of_transfer = $derived(
		fields.find((e) => e.name === 'reason_of_transfer')?.result ?? ''
	);
	let comment = $derived(fields.find((e) => e.name === 'comment')?.result ?? '');
	let n = $derived(fields.find((e) => e.name === 'n')?.result ?? '');
	let date_1 = $derived(fields.find((e) => e.name === 'date_1')?.result ?? '');
	let date_2 = $derived(fields.find((e) => e.name === 'date_2')?.result ?? '');
</script>

<input type="hidden" name="title" value="patient_transfer" />
<main style="max-width: 1200px;">
	<Header {get_document_setting} {logo} {n} {title_eng} {title_khm} />
	<div class="text-center">
		<h4
			style="color: {get_document_setting?.title_color}"
			class="kh_font_muol_light text-decoration-underline"
		>
			លិខិតបញ្ជូនអ្នកជំងឺ
		</h4>
	</div>
	<br />
	<div class="section fs-5">
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			អ្នកជំងឺឈ្មោះ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				readonly
				value={p_name}
				width="760px"
				type="text"
			/>
			សញ្ជាតិ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				readonly
				value={p_nation}
				width="100px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			អាសយដ្ឋាន <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				readonly
				value={p_address}
				width="940px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			ចូលសម្រាកព្យាបាលនៅថ្ងៃទី <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={khmerDate(p_date_checkup, 'datetime')}
				readonly
				width="810px"
				type="text"
			/>
		</div>
		<div class="mb-2">
			<div class="table">
				<table class="table table-bordered">
					<thead>
						<tr class="text-center">
							<td style="color: {get_document_setting?.text_body_color}">
								<div>សម្ពាធឈាម</div>
								<div>Blood Pressure</div>
							</td>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>ចង្វាក់បេះដូង</div>
								<div>Heart Rate</div>
							</td>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>ចង្វាក់ដង្ហើម</div>
								<div>Respiratory Rate</div>
							</td>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>កម្ដៅ</div>
								<div>Temperature</div>
							</td>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>កំហាប់អុកសុីសែន</div>
								<div>SpO <sub>2</sub></div>
							</td>
						</tr>
					</thead>
					<tbody>
						<tr class="text-center">
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										value={blood_pressure}
										name="blood_pressure"
										width="100%"
										type="text"
										class="border-0"
									/>
									<b>mmHg</b>
								</div>
							</td>
							<td>
								<div>
									<InputDocument
										class="border-0"
										value={heart_rate}
										name="heart_rate"
										width="100%"
										type="text"
									/>
									<b>bpm</b>
								</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										class="border-0"
										value={respiratory_rate}
										name="respiratory_rate"
										width="100%"
										type="text"
									/>
									<b>bpm</b>
								</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										class="border-0"
										value={temperature}
										name="temperature"
										width="100%"
										type="text"
									/>
									<b> °C</b>
								</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										class="border-0"
										value={spo2}
										name="spo2"
										width="100%"
										type="text"
									/>
									<b>%</b>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
		<div class="mb-2">
			<div class="table">
				<table class="table table-bordered">
					<tbody>
						<tr>
							<td style="width: 300px;">
								<div style="color: {get_document_setting?.text_body_color}">មូលហេតុចូល</div>
								<div style="color: {get_document_setting?.text_body_color}">
									Reason for admission
								</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										value={reason_for_admission}
										name="reason_for_admission"
										class="border-0"
										width="100%"
										type="text"
									/>
								</div>
							</td>
						</tr>
						<tr>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>ប្រវត្តជំងឺ</div>
								<div>Relevant medical history</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										class="border-0"
										value={relevant_medical_history}
										name="relevant_medical_history"
										width="100%"
										type="text"
									/>
								</div>
							</td>
						</tr>
						<tr>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>លទ្ធផលពិនិត្យរាងកាយខុសប្រក្រតី</div>
								<div>Clinical Examination</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										class="border-0"
										value={clinical_examination}
										name="clinical_examination"
										width="100%"
										type="text"
									/>
								</div>
							</td>
						</tr>
						<tr>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>លទ្ធផលពិនិត្យអមវេជ្ជសាស្ត្រ</div>
								<div>Para-clinical Examination</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										class="border-0"
										value={para_clinical_examination}
										name="para_clinical_examination"
										width="100%"
										type="text"
									/>
								</div>
							</td>
						</tr>
						<tr>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>រោគវិនិច្ឆ័យបឋម</div>
								<div>Primary Diagnosis</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										class="border-0"
										value={primary_diagnosis}
										name="primary_diagnosis"
										width="100%"
										type="text"
									/>
								</div>
							</td>
						</tr>
						<tr>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>ឱសថដែលបានផ្តល់អោយ</div>
								<div>Treatment</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										value={treatment}
										class="border-0"
										name="treatment"
										width="100%"
										type="text"
									/>
								</div>
							</td>
						</tr>
						<tr>
							<td style="color: {get_document_setting?.text_body_color}">
								<div>មូលហេតុបញ្ជូន</div>
								<div>Reason of Transfer</div>
							</td>
							<td>
								<div>
									<InputDocument
										style="color: {get_document_setting?.text_input_color}"
										class="border-0"
										value={reason_of_transfer}
										name="reason_of_transfer"
										width="100%"
										type="text"
									/>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
		<div class="mb-2">
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				class="text-start"
				value={comment}
				name="comment"
				width="100%"
				type="text"
			/>
		</div>
	</div>

	<div style="color: {get_document_setting?.text_body_color}" class="fs-5 mb-2 text-center">
		សូមលោកប្រធានមន្ទីរពេទ្យ លោកគ្រូអ្នកគ្រូជួយទទួល និង
		បន្តការពិនិត្យព្យាបាលអ្នកជំងឺនេះដោយក្តីអនុគ្រោះ ។
	</div>
	<br />
	<div class="text-end fs-5" style="margin-right: 5em;">
		<KhDateTime date={date_1} name="date_1" />
		<div style="padding-right: 55px;font-weight: bold;">គ្រូពេទ្យព្យាបាល</div>
	</div>
	<div style="margin-left: 5em;" class="fs-5">
		<KhDateTime date={date_2} name="date_2" />
		<div style="padding-left: 45px;">បានឃើញ និងឯកភាព</div>
		<div style="padding-left: 20px;font-weight: bold;">ប្រធានគ្លីនិក/ប្រធានមន្ទីរពេទ្យ</div>
	</div>
</main>
