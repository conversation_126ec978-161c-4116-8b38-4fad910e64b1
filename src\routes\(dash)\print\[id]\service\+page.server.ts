import { db } from '$lib/server/db';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { service } from '$lib/server/schemas';
export const load = (async ({ params }) => {
	const { id } = params;
	const get_service = await db.query.service.findFirst({
		where: eq(service.id, +id),
		with: {
			visit: {
				with: {
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					staff: true,
					department: true,
					accessment: true
				}
			},
			operationProtocol: true,
			progressNote: {
				with: {
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					staff: true,
					department: true,
					accessment: true
				}
			},
			product: true
		}
	});

	// const map_examination = new Map()
	return { get_service };
}) satisfies PageServerLoad;
