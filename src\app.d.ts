// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		// interface Locals {}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
		interface Locals {
			user: import('$lib/server/auth').SessionValidationResult['user'];
			session: import('$lib/server/auth').SessionValidationResult['session'];
			roles: import('$lib/server/auth').SessionValidationResult['roles'];
			departments: import('$lib/server/auth').SessionValidationResult['departments'];
		}
	}
	interface ViewTransition {
		updateCallbackDone: Promise<void>;
		ready: Promise<void>;
		finished: Promise<void>;
		skipTransition: () => void;
	}
	interface Document {
		startViewTransition(updateCallback: () => Promise<void>): ViewTransition;
	}
	// declare function $effect(fn: () => void | (() => void) | Promise<void>): void;
	interface Param {
		body?: string;
		animation?: boolean;
		autohide?: boolean;
		btnClose?: boolean;
		btnCloseWhite?: boolean;
		className?: string;
		delay?: number;
		gap?: number;
		header?: string;
		margin?: string;
		placement?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
	}
	declare const bs5: {
		Toast: {
			new (param: Param): {
				element: HTMLDivElement;
				bootstrapToast: {
					show(): void;
					hide(): void;
				};

				show(): void;
				hide(): void;
			};
		};
	};
}
export {};
