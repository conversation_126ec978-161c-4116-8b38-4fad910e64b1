import type { Actions, PageServerLoad } from './$types';
import path from 'path';
import * as fs from 'fs/promises';
import { env } from '$env/dynamic/private';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { fail } from '@sveltejs/kit';
import child from 'child_process';
import util from 'util';
import { backupAll } from '$lib/server/utils/backupAll';
const exec = util.promisify(child.exec);

export const load: PageServerLoad = async () => {
	const list_backups = path.join(process.cwd(), 'backups');
	const list_files = (await fs.readdir(list_backups)).map((e) => {
		const name_as_datetime = e.replace('.zip', '').replace('backup-', '').replace('.sql', '');
		return {
			name: e,
			date: name_as_datetime
		};
	});
	return {
		list_files: list_files.sort((a, b) => {
			return new Date(b.date).getTime() - new Date(a.date).getTime();
		})
	};
};

export const actions: Actions = {
	backup: async () => {
		const date = YYYYMMDD_Format.datetime(new Date());
		const name_backup = date.split(' ').join('_').split(':').join('.').concat('.sql');
		const filePath = path.join(process.cwd(), 'backups');
		// const dir = path.join(process.cwd());
		try {
			await exec(
				`mysqldump --host=${env.DB_HOST} --user=${env.DB_USER} --password=${env.DB_PASSWORD} -P ${env.DB_PORT} ${env.DB_NAME} > ${filePath}/${name_backup}`
			);
		} catch (e) {
			return fail(500, { serverError: e });
		}
	},
	restore_from_dir: async ({ request, url }) => {
		const body = await request.formData();
		const file = body.get('sql') as File;
		const filePath = path.join(process.cwd(), 'uploads');
		if (file.size) {
			try {
				await exec(
					`mysql --host=${env.DB_HOST} --user=${env.DB_USER} --password=${env.DB_PASSWORD} -P ${env.DB_PORT} ${env.DB_NAME} < ${filePath}/${file.name}`
				);
			} catch (e) {
				return fail(500, { serverError: e });
			}

			await fs.unlink(`${filePath}/${file.name}`);
		}
	},
	restore: async ({ request, url }) => {
		const body = await request.formData();
		const { name_backup } = Object.fromEntries(body) as Record<string, string>;
		const filePath = path.join(process.cwd(), 'backups');
		try {
			await exec(
				`mysql --host=${env.DB_HOST} --user=${env.DB_USER} --password=${env.DB_PASSWORD} -P ${env.DB_PORT} ${env.DB_NAME} < ${filePath}/${name_backup}`
			);
		} catch (e) {
			return fail(500, { serverError: e });
		}
	},
	delete: async ({ request, url }) => {
		const body = await request.formData();
		const { slug } = Object.fromEntries(body) as Record<string, string>;
		const filePath = path.join(process.cwd(), 'backups');
		try {
			await fs.unlink(`${filePath}/${slug ?? ''}`);
		} catch (e) {
			return fail(500, { serverError: e });
		}
	},
	backup_all: async () => {
		// backup_all().catch(console.error);
		await backupAll().catch((e) => {
			console.error(e);
		});
	}
};
