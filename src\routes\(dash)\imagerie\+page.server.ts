import { db } from '$lib/server/db';
import { imagerieRequest, patient } from '$lib/server/schemas';
import { betweenHelper, pagination } from '$lib/server/utils';
import type { PageServerLoad } from './$types';
import { desc, eq, like, or, and } from 'drizzle-orm';
export const load = (async ({ parent, url }) => {
	await parent();
	const q = url.searchParams.get('q') ?? '';
	const patient_id = Number(url.searchParams.get('patient_id'));
	const status = url.searchParams.get('status') || '';
	const get_imagires = await db.query.imagerieRequest.findMany({
		where: and(
			betweenHelper(url, imagerieRequest.request_datetime),
			patient_id ? eq(imagerieRequest.patient_id, patient_id) : undefined,
			status ? eq(imagerieRequest.status, Boolean(JSON.parse(status))) : undefined
		),
		with: {
			product: true,
			resultImagerie: true,
			visit: {
				with: {
					patient: true,
					staff: true
				}
			}
		},
		orderBy: desc(imagerieRequest.id),
		...pagination(url)
	});
	const count = await db.$count(
		imagerieRequest,
		and(
			betweenHelper(url, imagerieRequest.request_datetime),
			patient_id ? eq(imagerieRequest.patient_id, patient_id) : undefined,
			status ? eq(imagerieRequest.status, Boolean(JSON.parse(status))) : undefined
		)
	);
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`)
		),
		limit: 200
	});
	return {
		get_patients,
		items: count,
		get_imagires
	};
}) satisfies PageServerLoad;
