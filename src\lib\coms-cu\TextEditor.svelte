<script lang="ts">
	import 'suneditor/dist/css/suneditor.min.css';
	interface Props {
		name?: string;
		height?: number;
		setValue?: string;
		getValue?: string;
	}
	let {
		name = '',
		height = 400,
		setValue = $bindable(''),
		getValue = $bindable('')
	}: Props = $props();
	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
	// let ida = $derived(document.getElementById('a'));
	let destoryEditor: any = $state();
	$effect(() => {
		(async () => {
			const element = document.getElementById(id);
			const suneditor = (await import('suneditor')).default as any;
			const plugins = await import('suneditor/src/plugins');
			const editor = suneditor.create(element!, {
				font: ['KhmerOSBattambang', 'KhmerOSMuolLight', 'KhmerOSMuol', 'TimesNewRoman'],
				defaultStyle: 'font-size:18px;font-family:KhmerOSBattambang',
				plugins: plugins,
				height: height,
				buttonList: [
					[
						'undo',
						'redo',
						'font',
						'fontSize',
						'formatBlock',
						// 'paragraphStyle',
						// 'blockquote',
						'bold',
						'underline',
						'italic',
						'strike',
						'subscript',
						'superscript',
						'fontColor',
						'hiliteColor',
						// 'textStyle',
						'removeFormat',
						'outdent',
						'indent',
						'align',
						'horizontalRule',
						'list',
						'lineHeight',
						'table',
						// 'link',
						// 'image',
						// 'video',
						// 'audio' /** 'math', */, // You must add the 'katex' library at options to use the 'math' plugin.
						/** 'imageGallery', */ // You must add the "imageGalleryUrl".
						'fullScreen',
						'showBlocks',
						'codeView'
						// 'preview',
						// 'print',
						// 'save'

						/** 'dir', 'dir_ltr', 'dir_rtl' */ // "dir": Toggle text direction, "dir_ltr": Right to Left, "dir_rtl": Left to Right
					]
				]
				// The value of the option argument put in the "create" function call takes precedence
			});
			destoryEditor = editor;
			editor.onChange = function (contents: any, core: any) {
				getValue = contents ? contents : setValue;
			};
		})();
		return () => {
			destoryEditor.destroy();
		};
	});

	$effect(() => {
		destoryEditor?.setContents(setValue);
	});
</script>

<div>
	{#if name}
		<input type="hidden" {name} value={getValue} />
	{/if}
	<textarea class="form-control" {id}> </textarea>
</div>
