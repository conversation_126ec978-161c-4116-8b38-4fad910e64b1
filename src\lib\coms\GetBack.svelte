<script lang="ts">
	import { browser } from '$app/environment';
	import { locale } from '$lib/translations/locales.svelte';
	let { href }: { href: string } = $props();
	let from_url = $derived.by(() => {
		const url = browser ? sessionStorage?.getItem('from_url') : '';
		if (url) {
			return url;
		} else {
			return href;
		}
	});

	function onclick() {
		setTimeout(() => {
			if (browser) {
				sessionStorage?.removeItem('from_url');
			}
		}, 300);
	}
</script>

<a aria-label={href} {onclick} href={from_url} class="btn btn-link p-0"
	><i class="fa-solid fa-rotate-left"></i>
	{locale.T('back')}
</a>
