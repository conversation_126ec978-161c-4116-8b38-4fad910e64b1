import { db } from '$lib/server/db';
import { and, eq, isNull } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { billing } from '$lib/server/schemas';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { redirect } from '@sveltejs/kit';
import { BillingPOS } from '$lib/server/models';

export const load: PageServerLoad = async ({ parent, locals }) => {
	await parent();
	const { user } = locals;
	const old_billing = await db.query.billing.findFirst({
		where: and(
			isNull(billing.visit_id),
			isNull(billing.progress_note_id),
			eq(billing.status, 'paying'),
			eq(billing.billing_type, 'POS'),
			eq(billing.hold, false),
			eq(billing.staff_id, Number(user?.staff_id))
		)
	});
	if (old_billing) {
		await db
			.update(billing)
			.set({
				created_at: YYYYMMDD_Format.datetime(new Date()),
				staff_id: user?.staff_id
			})
			.where(eq(billing.id, old_billing.id));
		return redirect(303, `/billing/pos/${old_billing.id}`);
	} else {
		const billing_id = await BillingPOS(Number(user?.staff_id));
		if (billing_id) {
			return redirect(303, `/billing/pos/${billing_id}`);
		} else {
			return redirect(303, `/`);
		}
	}
};
