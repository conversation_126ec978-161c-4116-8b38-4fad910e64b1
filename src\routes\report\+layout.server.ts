import { db } from '$lib/server/db';
import { uploads } from '$lib/server/schemas';
import { eq } from 'drizzle-orm';
import type { LayoutServerLoad } from './$types';
export const load: LayoutServerLoad = async () => {
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_uploads = await db.query.uploads.findMany({
		where: eq(uploads.related_type, 'clinicinfo')
	});
	return {
		get_clinic_info: {
			...get_clinic_info,
			uploads: get_uploads
		}
	};
};
