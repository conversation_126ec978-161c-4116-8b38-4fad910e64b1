import { db } from '$lib/server/db';
import { words } from '$lib/server/schemas';
import logError from '$lib/server/utils/logError';
import { eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load = (async () => {
	return {};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_words: async ({ request, url }) => {
		const body = await request.formData();
		const { word, type, category } = Object.fromEntries(body) as Record<string, string>;
		await db
			.insert(words)
			.values({
				text: word,
				type: type,
				category: category
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_words: async ({ request, url }) => {
		const body = await request.formData();
		const { word, type, id, category } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(words)
			.set({
				text: word,
				type: type,
				category: category
			})
			.where(eq(words.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_words: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(words)
			.where(eq(words.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
