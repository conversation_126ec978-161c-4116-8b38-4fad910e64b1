import { int, mysqlTable, text, varchar } from 'drizzle-orm/mysql-core';
import { product } from './product';
import { relations } from 'drizzle-orm';
import { progressNote } from './visit';

export const ward = mysqlTable('ward', {
	id: int().primaryKey().autoincrement(),
	ward: varchar({ length: 100 }),
	description: text()
});

export const room = mysqlTable('room', {
	id: int().primaryKey().autoincrement(),
	room: varchar({ length: 100 }),
	product_id: int().references(() => product.id, {
		onDelete: 'set null',
		onUpdate: 'set null'
	}),
	department_id: int().references(() => product.id, {
		onDelete: 'set null',
		onUpdate: 'set null'
	}),
	ward_id: int().references(() => ward.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
	description: text()
});

export const bed = mysqlTable('bed', {
	id: int().primaryKey().autoincrement(),
	bed: varchar({ length: 100 }),
	room_id: int().references(() => room.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
	ward_id: int().references(() => ward.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
	description: text()
});

export const wardRelations = relations(ward, ({ many }) => ({
	room: many(room),
	bed: many(bed)
}));
export const roomRelations = relations(room, ({ one, many }) => ({
	ward: one(ward, {
		references: [ward.id],
		fields: [room.ward_id]
	}),
	department: one(product, {
		references: [product.id],
		fields: [room.department_id]
	}),
	product: one(product, {
		references: [product.id],
		fields: [room.product_id]
	}),
	bed: many(bed),
	progressNote: many(progressNote)
}));

export const bedRelations = relations(bed, ({ one }) => ({
	room: one(room, {
		references: [room.id],
		fields: [bed.room_id]
	}),
	ward: one(ward, {
		references: [ward.id],
		fields: [bed.ward_id]
	})
}));
