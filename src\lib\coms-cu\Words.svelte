<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	interface Words {
		id: number;
		text: string;
		type: string;
	}
	let q: string = $state('');
	let loading = $state(false);
	function handleText(text: string) {
		if (value?.includes(' '.concat(text).concat(','))) {
			value = value?.replace(' '.concat(text.concat(',')), '');
		} else {
			value = value?.concat(' '.concat(text)).concat(',');
		}
	}
	let isEdit = $state(false);
	let words_id: number = $state(0);
	let words_ = $state('');
	interface Props {
		words: Words[];
		modal_name: string;
		value?: string | undefined;
		name?: string;
		category: string;
	}

	let { words, modal_name, value = $bindable(undefined), name = '', category }: Props = $props();
	let find_words = $derived(
		words.filter((el: Words) => el?.text.toLowerCase().includes(q.toLowerCase()))
	);
	$effect(() => {
		if (isEdit === false) {
			words_ = '';
		}
	});
	let world_type_remove_space = $derived(
		modal_name.replaceAll(' ', '_').replaceAll('/', '_').replaceAll("'", '_')
	);
	let valueText = $state('');
	let isFoundText = $derived(words.some((e) => e.text === valueText));
</script>

<div class="modal fade" id={modal_name}>
	<div class="modal-dialog modal-dialog-scrollabl modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('words')} {name}</h4>
				<button
					id="close_words"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<Form
					fnSuccess={() => {
						words_id = 0;
						valueText = '';
						isEdit = false;
					}}
					bind:loading
					class="alert alert-secondary"
					reset={true}
					action={words_id > 0 && isEdit
						? '/opd/words/?/update_words'
						: '/opd/words/?/create_words'}
					method="post"
				>
					<input type="hidden" name="id" value={words_id ?? ''} />

					<div class="row">
						<input type="hidden" name="type" value={world_type_remove_space} />
						<input type="hidden" name="category" value={category} />
						<div class="col">
							<input
								oninput={(e) => (valueText = e.currentTarget.value)}
								value={words_}
								required
								name="word"
								type="text"
								placeholder={locale.T('new_world')}
								class="form-control"
							/>
						</div>
						<div class="col-auto">
							<fieldset disabled={isFoundText}>
								{#if words_id > 0 && isEdit}
									<SubmitButton {loading} name={locale.T('update')} />
								{:else}
									<SubmitButton {loading} name={locale.T('add')} />
								{/if}
							</fieldset>
						</div>
					</div>
				</Form>
				<div class="card">
					<div class="card-header">
						<input
							bind:value={q}
							type="search"
							placeholder={locale.T('search')}
							class="form-control"
						/>
					</div>
					<div style="height: 500px;" class="card-body overflow-auto">
						<div class=" row">
							{#each find_words as item}
								{#if item.text}
									<div class="col-6 p-2">
										<Form
											bind:loading
											fnSuccess={() => {
												words_id = 0;
												valueText = '';
												isEdit = false;
											}}
											action="/opd/words/?/delete_words"
											method="post"
										>
											<input type="hidden" name="id" value={item.id} />

											<input
												onclick={() => {
													handleText(item.text);
												}}
												checked={value?.includes(' '.concat(item.text).concat(','))}
												class="form-check-input"
												type="checkbox"
												id={item.id.toString()}
												value={item.text}
											/>
											<label
												class:text-danger={valueText === item.text}
												class:fs-1={valueText === item.text}
												for={item.id.toString()}
												class="custom-control-label">{item.text}</label
											>
											<button
												aria-label="submit"
												type="button"
												class={words_id === item.id && isEdit
													? 'btn btn-link m-0 p-0'
													: 'btn btn-link text-secondary m-0 p-0'}
												onclick={() => {
													words_id = 0;
													words_id = item.id;
													isEdit = !isEdit;
													words_ = '';
													words_ = item.text;
													valueText = '';
												}}><i class="fa-solid fa-file-pen"></i></button
											>
											{#if words_id === item.id && isEdit}
												<button
													onclick={(e) =>
														confirm(locale.T('confirm_delete')) &&
														e.currentTarget.form?.requestSubmit()}
													aria-label="submit"
													class="btn btn-link text-danger m-0 p-0"
													type="button"><i class="fa-solid fa-x"></i></button
												>
											{/if}
										</Form>
									</div>
								{/if}
							{/each}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
