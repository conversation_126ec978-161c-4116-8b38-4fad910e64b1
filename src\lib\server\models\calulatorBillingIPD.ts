import { eq } from 'drizzle-orm';
import { db } from '../db';
import { billing, progressNote } from '../schemas';

export async function totalIPD(progress_note_id: number | null) {
	if (progress_note_id) {
		const get_progress_note = await db.query.progressNote.findFirst({
			where: eq(progressNote.id, progress_note_id),
			with: {
				billing: {
					with: {
						charge: true
					}
				},
				visit: {
					with: {
						billing: true
					}
				}
			}
		});
		const opd_paid = get_progress_note?.visit.reduce((s, e) => s + Number(e.billing?.paid), 0) || 0;
		const opd_total =
			get_progress_note?.visit.reduce((s, e) => s + Number(e.billing?.total), 0) || 0;
		const opd_balance = opd_total - opd_paid;
		// const ipd_total = get_progress_note?.billing?.amount || 0
		const ipd_total =
			get_progress_note?.billing?.charge.reduce((s, e) => s + Number(e.price), 0) || 0;
		const result = {
			paid: Number(get_progress_note?.billing?.paid) + opd_paid,
			total: Number(get_progress_note?.billing?.total) + opd_paid,
			balance: get_progress_note?.billing?.balance || 0
		};
		return {
			opd_paid: opd_paid,
			opd_total: opd_total,
			opd_balance: opd_balance,
			ipd_total: ipd_total,
			amount_ipd_opd: ipd_total + opd_total,
			total_ipd_opd: ipd_total + (opd_total - opd_paid),
			result
		};
	}
}

export async function pushAmountOPDToIPD(progress_note_id: number) {
	if (progress_note_id) {
		const all_money = await totalIPD(progress_note_id);
		if (all_money) {
			await db
				.update(billing)
				.set({
					amount: all_money?.total_ipd_opd
				})
				.where(eq(billing.progress_note_id, progress_note_id));
		}
	}
}
