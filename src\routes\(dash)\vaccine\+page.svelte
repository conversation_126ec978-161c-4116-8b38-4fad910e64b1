<script lang="ts">
	import type { PageServerData } from './$types';
	import { enhance } from '$app/forms';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { store } from '$lib/store/store.svelte';
	import ConfirmeModal from '$lib/coms-form/ConfirmeModal.svelte';
	import Renderhtml from '$lib/coms/Renderhtml.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import { dobToAge } from '$lib/helper';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let vaccin_id = $state<number>();
	let loading = $state(false);
	let { get_injection } = $derived(data);
	let find_injection = $derived(get_injection.find((e) => e.id === vaccin_id));
	let appointment_injection_id = $state<number>();
</script>

<!-- svelte-ignore a11y_missing_attribute -->

<DeleteModal action="?/delete_appionment_inject" id={appointment_injection_id} />
<ConfirmeModal action="?/update_appointment_inject" id={appointment_injection_id} />
<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('vaccine')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/vaccine" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-syringe nav-icon"></i>
					{locale.T('vaccine')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col">
				<input
					type="text"
					name="table_search"
					class="form-control float-right"
					placeholder="Search"
				/>
			</div>
		</div>
	</div>
	<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-hover text-nowrap table-light">
			<thead class="table-active table-light sticky-top">
				<tr>
					<th class="text-center" style="width: 5%;">{locale.T('id')}</th>
					<th class="text-center">{locale.T('date')}</th>
					<th>{locale.T('id')} {locale.T('patient')}</th>
					<th>{locale.T('patient')}</th>
					<th class="text-center">{locale.T('gender')}</th>
					<th class="text-center">{locale.T('age')}</th>
					<th>{locale.T('doctor')}</th>
					<!-- <th>Visit Type</th> -->
					<th>{locale.T('vaccine')}</th>
					<th class="text-center">{locale.T('schedule')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody>
				{#each get_injection as item}
					<tr>
						<td class="text-center">{item.id}</td>
						<td class="text-center">
							<DDMMYYYYFormat style="date" date={item.datetime} />
							<br />
							<DDMMYYYYFormat style="time" date={item.datetime} />
						</td>
						<td class="text-center">
							{item?.patient?.id}
						</td>
						<td>
							{item?.patient?.name_khmer} <br />
							{item?.patient?.name_latin}
						</td>
						<td class="text-center">{item?.patient?.gender}</td>
						<td class="text-center">{dobToAge(item?.patient?.dob, item.datetime)}</td>
						<!-- <td>{item?.staff?.name}</td> -->
						<!-- <td>{item?.checkin_type}</td> -->
						<td></td>
						<td>
							<div>
								<span class=" badge text-bg-info text-start">{item.unit?.unit ?? ''}</span>
								<br />
								{#each item.vaccine as iitem, index (iitem.id)}
									<span class=" badge text-bg-success text-start"
										>{'លើកទី 0'.concat(String(index + 1))} {iitem.product?.products ?? ''}</span
									> <br />
								{/each}
							</div>
						</td>
						<td>
							<button
								onclick={() => {
									vaccin_id = 0;
									vaccin_id = item.id;
								}}
								data-bs-toggle="modal"
								data-bs-target="#create_injection"
								type="button"
								class="btn btn-sm btn-info"
								>{locale.T('schedule')}
							</button>
							<div>
								{#each item.appointmentInjection as iitem}
									{#if iitem.times < 10}
										<span class="badge text-bg-warning">
											{'លើកទី 0'.concat(String(iitem?.times))}
											<DDMMYYYYFormat style="date" date={iitem.appointment} />
										</span>
										{#if iitem.status}
											<button aria-label="injected" type="button" class="btn btn-link btn-lg"
												><i class="fa-solid fa-square-check"></i></button
											>
											{'បានចាក់​'}
											<DDMMYYYYFormat date={iitem.datetime_inject} />
										{:else}
											<button aria-label="notinjected" type="button" class="btn btn-link btn-lg"
												><i class="fa-regular fa-square"></i></button
											>
										{/if}

										<br />
									{:else}
										<span class="badge text-bg-warning">
											{'លើកទី '.concat(String(iitem?.times))}
										</span>
										<br />
									{/if}
								{/each}
							</div>
						</td>
						<td>
							<div>
								<button style="background-color: deeppink;" type="button" class="btn btn-sm"
									>{locale.T('invoice')}
								</button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>

<!-- @Create Ijection -->
<div class="modal fade" id="create_injection" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('schedule')} {locale.T('injection')}</h4>
				<button
					aria-label="close"
					data-bs-toggle="modal"
					data-bs-target="#create_injection"
					id="nest_create_injection"
					type="button"
					class="d-none"
				>
				</button>
				<button
					id="close_create_injection"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div>
					<Form bind:loading action="?/create_appointment_inject" method="post">
						<input type="hidden" name="injection_id" value={find_injection?.id ?? ''} />
						<div class=" mb-3">
							<label for="times">{locale.T('times')}</label>
							<select class="form-control" name="times" id="times">
								<option value="1">{locale.T('time_1')}</option>
								<option value="2">{locale.T('time_2')}</option>
								<option value="3">{locale.T('time_3')}</option>
								<option value="4">{locale.T('time_4')}</option>
								<option value="5">{locale.T('time_5')}</option>
								<option value="6">{locale.T('time_6')}</option>
								<option value="7">{locale.T('time_7')}</option>
								<option value="8">{locale.T('time_8')}</option>
								<option value="9">{locale.T('time_9')}</option>
								<option value="10">{locale.T('time_10')}</option>
							</select>
						</div>
						<div class=" mb-3">
							<label for="appointment">{locale.T('appintment')}</label>
							<input class="form-control" type="date" name="appointment" id="appointment" />
						</div>
						<div>
							<label for="discription">{locale.T('description')}</label>
							<textarea rows="3" class="form-control" name="discription" id="discription"
							></textarea>
						</div>
						<div class=" col pt-3 justify-content-end text-end">
							<SubmitButton {loading} />
						</div>
					</Form>
				</div>

				<hr />
				<div class="card-body table-responsive p-0">
					{#if find_injection?.unit?.vaccine_dose}
						<Renderhtml value={find_injection?.unit?.vaccine_dose ?? ''} />
					{/if}
					<table class="table table-sm text-nowrap">
						<thead>
							<tr>
								<th colspan="5">
									{find_injection?.unit?.unit ?? ''}
								</th>
							</tr>
						</thead>
						<tbody>
							{#each find_injection?.appointmentInjection || [] as item, index (item.id)}
								<tr>
									<td>
										<button
											aria-label="delete_modal"
											onclick={() => {
												appointment_injection_id = 0;
												appointment_injection_id = item.id;
											}}
											type="button"
											class="btn btn-danger"
											data-bs-toggle="modal"
											data-bs-target="#delete_modal"
											><i class="fa-solid fa-trash-can"></i>
										</button>
									</td>
									<td>
										{#if item.times < 10}
											{'លើកទី 0'.concat(String(item?.times))}
										{:else}
											{'លើកទី '.concat(String(item?.times))}
										{/if}
									</td>

									<td>
										{#if item.status}
											<button class="btn btn-primary">
												<DDMMYYYYFormat style="date" date={item.appointment} />
											</button>
										{:else}
											<button class="btn btn-danger">
												<DDMMYYYYFormat style="date" date={item.appointment} />
											</button>
										{/if}
									</td>
									<td>
										{#if item.status}
											<button
												aria-label="confirme_modal"
												onclick={() => {
													appointment_injection_id = 0;
													appointment_injection_id = item.id;
												}}
												data-bs-toggle="modal"
												data-bs-target="#confirme_modal"
												id="confirme_modal"
												type="button"
												class="btn btn-link btn-lg"><i class="fa-solid fa-square-check"></i></button
											>
										{:else}
											<button
												aria-label="confirme_modal"
												onclick={() => {
													appointment_injection_id = 0;
													appointment_injection_id = item.id;
												}}
												disabled={index + 1 > Number(find_injection?.vaccine.length)}
												data-bs-toggle="modal"
												data-bs-target="#confirme_modal"
												id="confirme_modal"
												type="button"
												class="btn btn-link btn-lg"><i class="fa-regular fa-square"></i></button
											>
										{/if}
									</td>
									{#if item.status}
										<td>
											<DDMMYYYYFormat date={item.datetime_inject} />

											<!-- Vaccine {find_injection?.vaccine[index].product?.products ?? ''} -->
										</td>
									{:else}
										<td></td>
									{/if}
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>
