<script lang="ts">
	import Words from '$lib/coms-cu/Words.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_nursing_process, progress_note_id, get_words } = $derived(data);
	let loading = $state(false);
	let datetime = $state('');
	let accessment = $state('');
	let health_problems = $state('');
	let actions = $state('');
	let evolution = $state('');
	$effect(() => {
		if (get_nursing_process) {
			datetime = get_nursing_process?.datetime ?? '';
			accessment = get_nursing_process?.accessment ?? '';
			health_problems = get_nursing_process?.health_problems ?? '';
			actions = get_nursing_process?.actions ?? '';
			evolution = get_nursing_process?.evolution ?? '';
		} else {
			datetime = '';
			accessment = '';
			health_problems = '';
			actions = '';
			evolution = '';
		}
	});
</script>

<Words
	name="Accessment"
	bind:value={accessment}
	words={get_words.filter((e) => e.type === 'accessment')}
	modal_name="accessment"
	category="objective"
/>
<Words
	name="Health Problems"
	bind:value={health_problems}
	words={get_words.filter((e) => e.type === 'health_problems')}
	modal_name="health_problems"
	category="objective"
/>
<Words
	name="Actions"
	bind:value={actions}
	words={get_words.filter((e) => e.type === 'actions')}
	modal_name="actions"
	category="objective"
/>
<Words
	name="Evolution"
	bind:value={evolution}
	words={get_words.filter((e) => e.type === 'evolution')}
	modal_name="evolution"
	category="objective"
/>
<Form
	action="/ipd/{progress_note_id}/nursing-process/?/create_nursing_process"
	method="post"
	reset={false}
	bind:loading
>
	<div class="card bg-light">
		<input type="hidden" value={get_nursing_process?.id || ''} name="get_nursing_process.id" />
		<div class="card-header fs-5">
			<div class="row">
				<div class="col">
					<a
						aria-label="nersing_process"
						href="/ipd/{progress_note_id}/nursing-process"
						class="btn btn-link m-0 p-0"
						><i class="fa-solid fa-rotate-left"></i> {locale.T('back')}
					</a>
					<!-- <span># {locale.T('nursing_process')}</span> -->
				</div>
				<div class="col-auto"></div>
			</div>
		</div>
		<div class="modal-body">
			<div class="card-body">
				{#if get_nursing_process?.id}
					<input value={datetime || ''} type="hidden" name="date" />
					<input value={get_nursing_process?.id} type="hidden" name="nursing_process_id" />
					<div class=" row pb-3">
						<div class="col-sm-3">
							<button type="button" class="btn btn-outline-primary btn-sm"
								>{locale.T('time')}</button
							>
						</div>

						<div class="col-sm-9">
							<div class="input-group">
								<input
									value={datetime?.slice(11, 16)}
									id="time"
									name="time"
									type="time"
									class="form-control"
								/>
							</div>
						</div>
					</div>
				{/if}
				<div class=" row pb-2">
					<div class="col-sm-3">
						<button
							data-bs-toggle="modal"
							data-bs-target="#accessment"
							type="button"
							class="btn btn-outline-primary btn-sm">{'ការប៉ាន់ប្រមាណ'}</button
						>
					</div>
					<div class="col-sm-9">
						<textarea
							bind:value={accessment}
							id="accessment"
							name="accessment"
							rows="4"
							class="form-control"
						></textarea>
					</div>
				</div>

				<div class=" row pb-3">
					<div class="col-sm-3">
						<button
							data-bs-toggle="modal"
							data-bs-target="#health_problems"
							type="button"
							class="btn btn-outline-primary btn-sm">{'បញ្ហាសុខភាពដែលត្រូវថែទាំ'}</button
						>
					</div>
					<div class="col-sm-9">
						<div class="input-group">
							<textarea
								bind:value={health_problems}
								id="health_problems"
								name="health_problems"
								rows="4"
								class="form-control"
							></textarea>
						</div>
					</div>
				</div>
				<div class=" row pb-3">
					<div class="col-sm-3">
						<button
							data-bs-toggle="modal"
							data-bs-target="#actions"
							type="button"
							class="btn btn-outline-primary btn-sm">{'សកម្មភាពដែលបានអនុវត្តន៍'}</button
						>
					</div>
					<div class="col-sm-9">
						<div class="input-group">
							<textarea
								bind:value={actions}
								id="actions"
								name="actions"
								rows="4"
								class="form-control"
							></textarea>
						</div>
					</div>
				</div>

				<div class=" row pb-3">
					<div class="col-sm-3">
						<button
							data-bs-toggle="modal"
							data-bs-target="#evolution"
							type="button"
							class="btn btn-outline-primary btn-sm">{'ការវាយតម្លៃ'}</button
						>
					</div>
					<div class="col-sm-9">
						<div class="input-group">
							<textarea
								value={evolution}
								id="evolution"
								name="evolution"
								rows="4"
								class="form-control"
							></textarea>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="card-footer float-end text-end">
			<SubmitButton {loading} />
		</div>
	</div>
</Form>
