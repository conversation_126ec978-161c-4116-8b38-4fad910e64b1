<script lang="ts">
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { uuid, related_id, related_type, old_filename } = $derived(data);
	let loading = $state(false);
</script>

<Form showToast={false} bind:loading action="?/upload" method="post" enctype="multipart/form-data">
	<div class="container">
		<div class="row justify-content-center">
			<div class="col-sm-6">
				{#if uuid}
					<div class="mb-3 mt-3">
						<input type="hidden" name="uuid" value={uuid} />
						<CropImage
							{related_id}
							related_type_={related_type}
							default_image={old_filename}
							aspect_ratio
							name="file"
						/>
					</div>
					<SubmitButton style="w-100" {loading} />
				{/if}
				{#if !uuid}
					<div class="mb-3 mt-3 text-center">
						<div class="alert alert-success" role="alert">
							<h2 class="alert-heading">ចំណាំ!</h2>
							<p>
								<i class="fa-regular fa-circle-check"></i> ឯកសារដែលអ្នកបានបញ្ចូលបានរក្សាទុកជោគជ័យ។
							</p>
							<hr />
							<p class="mb-0 text-danger">គ្មាន QR Code ដើម្បីបញ្ជូលទិន្ន័យ!</p>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
</Form>
