import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import archiver from 'archiver';
import 'dotenv/config';
import { env } from '$env/dynamic/private';
const BACKUP_DIR = path.join(process.cwd(), 'backups');
const UPLOADS_DIR = path.join(process.cwd(), 'uploads');
const DB_NAME = env.DB_NAME;
const DB_USER = env.DB_USER;
const DB_PASSWORD = env.DB_PASSWORD;
const DB_HOST = env.DB_HOST;
const DB_PORT = env.DB_PORT;

export async function backupAll() {
	const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
	const backupFileName = `backup-${timestamp}.zip`;
	const backupFilePath = path.join(BACKUP_DIR, backupFileName);
	const dumpFilePath = path.join(BACKUP_DIR, `dump-${timestamp}.sql`);

	// Ensure backup directory exists
	if (!fs.existsSync(BACKUP_DIR)) {
		fs.mkdirSync(BACKUP_DIR, { recursive: true });
	}

	console.log('🔄 Dumping MySQL database...');

	const dumpCommand = `mysqldump -u${DB_USER} -p${DB_PASSWORD} -h${DB_HOST} -P ${DB_PORT} ${DB_NAME} > "${dumpFilePath}"`;

	// Step 1: Dump the MySQL database
	await new Promise<void>((resolve, reject) => {
		exec(dumpCommand, (error, _stdout, stderr) => {
			if (error) {
				console.error('❌ MySQL dump failed:', stderr);
				reject(error);
			} else {
				console.log('✅ MySQL dump completed.');
				resolve();
			}
		});
	});

	console.log('📦 Creating zip archive...');

	// Step 2: Create ZIP archive
	await new Promise<void>((resolve, reject) => {
		const output = fs.createWriteStream(backupFilePath);
		const archive = archiver('zip', { zlib: { level: 9 } });

		output.on('close', () => {
			console.log(`✅ Backup archive created: ${backupFilePath}`);
			resolve();
		});

		archive.on('error', (err) => {
			reject(err);
		});

		archive.pipe(output);

		archive.directory(UPLOADS_DIR, 'uploads');
		archive.file(dumpFilePath, { name: 'mysql_dump.sql' });

		archive.finalize();
	});

	// Optional: delete the standalone SQL file after zipping
	fs.unlinkSync(dumpFilePath);
}
