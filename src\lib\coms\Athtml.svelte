<script lang="ts">
	interface Props {
		html?: string | null | undefined;
		contenteditable?: boolean;
	}

	let { html = $bindable(''), contenteditable = false }: Props = $props();
</script>

{#if contenteditable}
	<div style="max-width:100%;" contenteditable="true" bind:innerHTML={html}></div>
{:else}
	<div style="max-width:100%;" contenteditable="false" bind:innerHTML={html}></div>
{/if}
<!-- <style>
	[contenteditable] {
		padding: 0.5em;
		border: 1px solid #eee;
		border-radius: 4px;
	}
</style> -->
