<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { dobToAge } from '$lib/helper';

	interface Props {
		form: ActionData;
		data: PageServerData;
	}

	let { form, data }: Props = $props();
	let staff_id = $state<number>();
	let { get_staffs, items, get_roles } = $derived(data);
	let find_staff = $derived(get_staffs.filter((e) => e.id === staff_id));
	let n: number = $state(1);
</script>

<DeleteModal action="?/delete_staff" id={find_staff[0]?.id} />
<!-- @_Visite_Modal -->
<div class="modal fade" id="modal-visite">
	<div class="modal-dialog modal-dialog-centered modal-sm">
		<div class="modal-content">
			<div class="modal-header">
				<button aria-label="submit" type="submit" class="btn btn-success btn-lg p-4"
					><i class=" fas fa-stethoscope fa-3x"></i></button
				>
				<button aria-label="submit" type="submit" class="btn btn-danger btn-lg p-4"
					><i class=" fas fa-procedures fa-3x"></i></button
				>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('staff')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/staff" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-users nav-icon"></i>
					{locale.T('staff')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col">
				<HeaderQuery>
					<div class="col-sm-3">
						<HandleQ />
					</div>
					<div class="col-sm-3">
						<select class="form-control" name="role" id="role">
							<option selected value="">ALL</option>
							{#each get_roles as item}
								<option value={item.role}>{item.role}</option>
							{/each}
						</select>
					</div>
				</HeaderQuery>
			</div>

			<div class="col-auto">
				<a href="/staff/create" class="btn btn-success"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add')}
				</a>
			</div>
		</div>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
		<table class="table table-hover table-bordered table-light">
			<thead class="sticky-top bg-light table-active">
				<tr class="text-center">
					<th class="text-center">{locale.T('n')}</th>
					<th>{locale.T('picture')}</th>
					<th>{locale.T('name')}</th>
					<th>{locale.T('specific')}</th>
					<th>{locale.T('gender')}</th>
					<th>{locale.T('dob')}</th>
					<th>{locale.T('age')}</th>
					<th>{locale.T('address')}</th>
					<th>{locale.T('contact')}</th>
					<th>{locale.T('status')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_staffs as item, index}
					<tr class="text-center">
						<td class="text-center">{n + index}</td>

						<td class="text-center">
							<img
								src={item?.uploads?.filename ? `${item?.uploads?.filename}` : '/no-user.png'}
								alt=""
								height="30"
							/>
						</td>
						<td>
							<a
								href="/staff/create/?staff_id={item.id}&village_id={item.village_id}&district_id={item.district_id}&commune_id={item.commune_id}&province_id={item.province_id}"
							>
								{item?.name_khmer} <br />
								{item?.name_latin}
							</a>
						</td>
						<td>{item?.specialist}</td>
						<td>{item?.gender}</td>
						<td>
							<DDMMYYYYFormat style="date" date={item?.dob} />
						</td>
						<td>{dobToAge(item?.dob, null)} </td>
						<td>
							{item?.village?.type ?? ''}
							{item?.village?.name_khmer ?? ''}
							{item?.commune?.type ?? ''}
							{item?.commune?.name_khmer ?? ''}
							{item?.district?.type ?? ''}
							{item?.district?.name_khmer ?? ''}
							{item?.provice?.type ?? ''}
							{item?.provice?.name_khmer ?? ''}
						</td>
						<td>{item?.telephone}</td>

						<td class={item.datetime_stop ? 'text-danger text-start' : 'text-success text-start'}>
							<div>
								{locale.T('start_working')} : <DDMMYYYYFormat
									style="date"
									date={item?.datetime_start}
								/> <br />
								{locale.T('stop_working')} : <DDMMYYYYFormat
									style="date"
									date={item?.datetime_stop}
								/>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
