import { boolean, datetime, int, mysqlTable, text } from 'drizzle-orm/mysql-core';
import { progressNote, visit } from './visit';
import { relations } from 'drizzle-orm';

export const appointment = mysqlTable('appointment', {
	id: int().primaryKey().autoincrement(),
	visit_id: int().references(() => visit.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	datetime: datetime({ mode: 'string' }),
	description: text(),
	datetime_come: datetime({ mode: 'string' }),
	status: boolean().default(false).notNull()
});
export const appointmentRelations = relations(appointment, ({ one }) => ({
	visit: one(visit, {
		references: [visit.id],
		fields: [appointment.visit_id]
	}),
	progressNote: one(progressNote, {
		references: [progressNote.id],
		fields: [appointment.progress_note_id]
	})
}));
