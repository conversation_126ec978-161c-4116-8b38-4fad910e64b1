import { db } from '$lib/server/db';
import { category, unit } from '$lib/server/schemas';
import logError from '$lib/server/utils/logError';
import type { Actions, PageServerLoad } from './$types';
import { eq } from 'drizzle-orm';

export const load = (async () => {
	const get_category = await db.query.category.findFirst({
		where: eq(category.name, 'Vaccine'),
		with: {
			group: {
				with: {
					unitsToGroups: {
						with: {
							unit: true
						}
					}
				}
			}
		}
	});

	return {
		get_category
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	update_vaccine_dose: async ({ request, url }) => {
		const body = await request.formData();
		const { vaccine_dose, unit_id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(unit)
			.set({
				vaccine_dose: vaccine_dose
			})
			.where(eq(unit.id, +unit_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
