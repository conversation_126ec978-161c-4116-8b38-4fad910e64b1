import { relations } from 'drizzle-orm';
import { int, mysqlTable, varchar, text } from 'drizzle-orm/mysql-core';
import { visit } from './visit';

export const subjective = mysqlTable('subjective', {
	id: int().primaryKey().autoincrement(),
	cheif_complaint: text(),
	current_medication: varchar({ length: 255 }),
	history_of_present_illness: varchar({ length: 255 }),
	past_medical_history: text(),
	allesgy_medicine: varchar({ length: 255 }),
	surgical_history: varchar({ length: 255 }),
	pre_diagnosis: varchar({ length: 255 }),
	family_and_social_history: varchar({ length: 255 }),
	visit_id: int()
		.references(() => visit.id, { onDelete: 'cascade', onUpdate: 'cascade' })
		.unique()
});

export const subjectiveRelations = relations(subjective, ({ one }) => ({
	visit: one(visit, {
		fields: [subjective.visit_id],
		references: [visit.id]
	})
}));
