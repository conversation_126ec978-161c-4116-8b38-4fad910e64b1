import { datetime, int, mysqlTable, text, varchar } from 'drizzle-orm/mysql-core';
import { progressNote, visit } from './visit';
import { relations } from 'drizzle-orm';
export const document = mysqlTable('document', {
	id: int().primaryKey().autoincrement(),
	datetime: datetime({ mode: 'string' }),
	title: varchar({ length: 255 }),
	content: text(),
	visit_id: int().references(() => visit.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	})
});
export const documentRelations = relations(document, ({ one, many }) => ({
	visit: one(visit, {
		fields: [document.visit_id],
		references: [visit.id]
	}),
	progressNote: one(progressNote, {
		fields: [document.progress_note_id],
		references: [progressNote.id]
	}),
	fields: many(fields)
}));

export const fields = mysqlTable('fields', {
	id: int().primaryKey().autoincrement(),
	name: varchar({ length: 255 }),
	result: text(),
	document_id: int().references(() => document.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	})
});

export const fieldsRelations = relations(fields, ({ one }) => ({
	document: one(document, {
		fields: [fields.document_id],
		references: [document.id]
	})
}));
