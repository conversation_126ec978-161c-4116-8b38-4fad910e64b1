import { relations } from 'drizzle-orm';
import { int, mysqlTable, varchar } from 'drizzle-orm/mysql-core';
import { visit } from './visit';

export const exam = mysqlTable('exam', {
	id: int().primaryKey().autoincrement(),
	examination: varchar({ length: 255 })
});

export const physical = mysqlTable('physical', {
	id: int().primaryKey().autoincrement(),
	physical: varchar({ length: 150 }),
	exam_id: int().references(() => exam.id, { onDelete: 'cascade', onUpdate: 'cascade' })
});

export const physicalExam = mysqlTable('physical_exam', {
	id: int().primaryKey().autoincrement(),
	physical_id: int().references(() => physical.id),
	result: varchar({ length: 255 }),
	visit_id: int().references(() => visit.id, { onDelete: 'cascade', onUpdate: 'cascade' })
});

export const examRelations = relations(exam, ({ many }) => ({
	physical: many(physical)
}));

export const physicalRelations = relations(physical, ({ one }) => ({
	exam: one(exam, {
		fields: [physical.exam_id],
		references: [exam.id]
	})
}));
export const physicalExamRelations = relations(physicalExam, ({ one }) => ({
	visit: one(visit, {
		fields: [physicalExam.visit_id],
		references: [visit.id]
	}),
	physical: one(physical, {
		fields: [physicalExam.physical_id],
		references: [physical.id]
	})
}));
