import { db } from '$lib/server/db';
import { and, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { uploads, visit } from '$lib/server/schemas';
export const load = (async ({ params }) => {
	const { id } = params;
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +id),
		with: {
			accessment: true,
			patient: {
				with: {
					village: true,
					district: true,
					commune: true,
					provice: true
				}
			},
			staff: true,
			imagerieRequest: {
				with: {
					product: true
				}
			}
		}
	});
	const s = get_visit?.imagerieRequest?.sort((a, b) => {
		const groupIdA = a?.product?.group_id as number;
		const groupIdB = b?.product?.group_id as number;
		if (+groupIdA < +groupIdB) return -1;
		if (+groupIdA > +groupIdB) return 1;
		return 0;
	});
	return { get_clinic_info, get_visit, sort_by_group: s, get_upload };
}) satisfies PageServerLoad;
