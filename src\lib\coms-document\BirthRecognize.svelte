<script lang="ts">
	import InputDocument from '$lib/coms-form/InputDocument.svelte';
	import type { TAddress, TDocumentSetting, TFields } from '$lib/type';
	import KhDateTime from '$lib/coms-document/KhDateInput.svelte';
	import { khmerDate } from '$lib/helper';
	import Header from '$lib/coms-document/Header.svelte';
	interface Prop {
		p_name: string;
		p_address?: TAddress;
		p_occupation: string;
		fields: TFields[];
		occupation_list?: string[];
		get_document_setting?: TDocumentSetting;
		title_khm: string;
		title_eng: string;
		logo: string;
	}
	let {
		p_name,
		p_occupation,
		p_address: address,
		fields,
		logo,
		title_eng,
		get_document_setting,
		title_khm,
		occupation_list
	}: Prop = $props();
	let p_address = $derived(
		`${address?.village?.type ?? ''} ${address?.village?.name_khmer ?? ''} ${address?.commune?.type ?? ''} ${address?.commune?.name_khmer ?? ''} ${address?.district?.type ?? ''} ${address?.district?.name_khmer ?? ''} ${address?.provice?.type ?? ''} ${address?.provice?.name_khmer ?? ''}`
	);
	let genders = $state(['ប្រុស', 'ស្រី']);
	let n = $derived(fields.find((e) => e.name === 'n')?.result ?? '');
	let r_name = $derived(fields.find((e) => e.name === 'r_occupation')?.result ?? '');
	let r_occupation = $derived(fields.find((e) => e.name === 'r_name')?.result ?? '');
	let n_child = $derived(fields.find((e) => e.name === 'n_child')?.result ?? '');
	let name_child_1 = $derived(fields.find((e) => e.name === 'name_child_1')?.result ?? '');
	let name_child_2 = $derived(fields.find((e) => e.name === 'name_child_2')?.result ?? '');
	let name_child_3 = $derived(fields.find((e) => e.name === 'name_child_3')?.result ?? '');
	let gender_child_1 = $derived(fields.find((e) => e.name === 'gender_child_1')?.result ?? '');
	let gender_child_2 = $derived(fields.find((e) => e.name === 'gender_child_2')?.result ?? '');
	let gender_child_3 = $derived(fields.find((e) => e.name === 'gender_child_3')?.result ?? '');
	let date_1 = $state(fields.find((e) => e.name === 'date_1')?.result ?? '');
	let date_2 = $derived(fields.find((e) => e.name === 'date_2')?.result ?? '');
	let date_3 = $derived(fields.find((e) => e.name === 'date_3')?.result ?? '');
	let times_birth = $derived(fields.find((e) => e.name === 'times_birth')?.result ?? '');
	let weigh = $derived(fields.find((e) => e.name === 'weigh')?.result ?? '');
</script>

<input type="hidden" name="title" value="birth_recognize" />
<main style="max-width: 1200px;">
	<Header {get_document_setting} {logo} {n} {title_eng} {title_khm} />
	<div class="text-center">
		<h4
			style="color: {get_document_setting?.title_color}"
			class="kh_font_muol_light text-decoration-underline"
		>
			លិខិតទទួលស្គាល់កំណើត
		</h4>
	</div>
	<br />
	<div class="section fs-5">
		<div class="mb-2">
			<table class="table table-bordered" border="1" cellspacing="0" cellpadding="5">
				<tbody>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">បានទទួលស្គាល់លោកស្រី</td>
						<td style="color: {get_document_setting?.text_input_color}" colspan="3">{p_name}</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">អាសយដ្ឋាន</td>
						<td style="color: {get_document_setting?.text_input_color}" colspan="3">{p_address}</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">មុខរបរ</td>
						<td style="color: {get_document_setting?.text_input_color}" colspan="3">
							{p_occupation}
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">ឈ្មោះប្តី</td>
						<td colspan="3">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={r_name}
								name="r_name"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">មុខរបរ</td>
						<td colspan="3">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								data_list={occupation_list}
								value={r_occupation}
								name="r_occupation"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">បានកើតកូនចំនួន</td>
						<td colspan="3">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={n_child}
								name="n_child"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">ឈ្មោះកូនទី១</td>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={name_child_1}
								name="name_child_1"
								width="100%"
								type="text"
							/>
						</td>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center">ភេទ</td>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								data_list={genders}
								value={gender_child_1}
								name="gender_child_1"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">ឈ្មោះកូនទី២</td>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={name_child_2}
								name="name_child_2"
								width="100%"
								type="text"
							/>
						</td>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center">ភេទ</td>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								data_list={genders}
								value={gender_child_2}
								name="gender_child_2"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">ឈ្មោះកូនទី៣</td>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={name_child_3}
								name="name_child_3"
								width="100%"
								type="text"
							/>
						</td>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center">ភេទ</td>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								data_list={genders}
								value={gender_child_3}
								name="gender_child_3"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td
							style="color: {get_document_setting?.text_body_color}"
							class="text-center"
							colspan="4"
							>នៅ ៖ {title_khm}
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">ថ្ងៃទី</td>
						<td>
							<KhDateTime
								style="color: {get_document_setting?.text_input_color}"
								bind:date={date_1}
								type="datetime-local"
								name="date_1"
							/>
						</td>
						<td class="text-center" style="color: {get_document_setting?.text_body_color}"
							>នៅវេលាម៉ោង</td
						>
						<td style="color: {get_document_setting?.text_input_color}">
							{khmerDate(date_1, 'time')}
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">ប្រសូតលើកទី</td>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={times_birth}
								name="times_birth"
								width="100%"
								type="text"
							/>
						</td>
						<td class="text-center" style="color: {get_document_setting?.text_body_color}">ទំងន់</td
						>
						<td style="color: {get_document_setting?.text_body_color}">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={weigh}
								name="weigh"
								width="120px"
								type="text"
							/> គីឡូក្រាម
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>

	<br />
	<div class="text-end fs-5" style="margin-right: 5em;">
		<KhDateTime date={date_2} name="date_2" />
		<div style="padding-right: 55px;font-weight: bold;">គ្រូពេទ្យព្យាបាល</div>
	</div>
	<div style="margin-left: 5em;" class="fs-5">
		<KhDateTime date={date_3} name="date_3" />
		<div style="padding-left: 45px;">បានឃើញ និងឯកភាព</div>
		<div style="padding-left: 20px;font-weight: bold;">ប្រធានគ្លីនិក/ប្រធានមន្ទីរពេទ្យ</div>
	</div>
</main>
