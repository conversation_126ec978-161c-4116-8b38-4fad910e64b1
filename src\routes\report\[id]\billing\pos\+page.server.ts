import { db } from '$lib/server/db';
import { billing, visit } from '$lib/server/schemas';
import { and, eq, gt, ne } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { uploads } from '$lib/server/schemas';

export const load = (async ({ params }) => {
	const { id } = params;
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	const get_currency = await db.query.currency.findFirst({});
	const get_billing = await db.query.billing.findFirst({
		where: eq(billing.id, +id),
		with: {
			payment: {
				with: {
					paymentType: true
				}
			},
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			charge: {
				with: {
					productOrder: {
						with: {
							product: true,
							unit: true
						}
					}
				}
			}
		}
	});
	const get_billings = await db.query.billing.findMany({
		where: and(
			gt(billing.balance, 0),
			eq(billing.patient_id, Number(get_billing?.patient_id)),
			ne(billing.id, +id)
		)
	});
	const patient = {
		patient: get_billing?.patient,
		date_checkout: get_billing?.created_at,
		id: get_billing?.id,
		patient_id: get_billing?.patient_id
	};
	const previous_due = get_billings.reduce((s: number, i) => s + +i.balance!, 0);
	return {
		get_currency,
		get_clinic_info,
		previous_due,
		patient,
		get_billing,
		get_upload
	};
}) satisfies PageServerLoad;
