<script lang="ts">
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData, ActionData } from './$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}

	let { data, form }: Props = $props();
	let { get_suppliers, items } = $derived(data);
	let supplier_id = $state(0);
	let find_supplier = $derived(get_suppliers?.find((e) => e.id === supplier_id));
	let loading = $state(false);
	let n = $state(1);
	function pushSupplierId(id: number) {
		supplier_id = 0;
		supplier_id = id;
	}
</script>

<DeleteModal action="?/delete_supplier" id={find_supplier?.id} />

<!-- @_Add_Supplier -->
<div class="modal fade" id="create_supplier" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action="?/create_supplier"
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => document.getElementById('close_supplier')?.click()}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('supplier')}</h4>
				<button
					id="close_supplier"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={find_supplier?.id} type="hidden" name="supplier_id" />
								<label for="name">{locale.T('name')}</label>
								<input
									value={find_supplier?.name ?? ''}
									name="name"
									type="text"
									class="form-control"
									id="name"
								/>
								{#if form?.name}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="company">{locale.T('company')}</label>
								<input
									value={find_supplier?.company_name ?? ''}
									name="company"
									type="text"
									class="form-control"
									id="company"
								/>
								{#if form?.company}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="contact">{locale.T('contact')}</label>
								<input
									value={find_supplier?.contact ?? ''}
									name="contact"
									type="text"
									class="form-control"
									id="contact"
								/>
								{#if form?.contact}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="address">{locale.T('address')}</label>
								<input
									value={find_supplier?.address ?? ''}
									name="address"
									type="text"
									class="form-control"
									id="address"
								/>
								{#if form?.address}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('supplier')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/supplier" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-people-carry-box"></i>
					{locale.T('supplier')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row gap-1">
			<div class="col">
				<HeaderQuery>
					<div class="col-sm-3">
						<HandleQ />
					</div>
				</HeaderQuery>
			</div>
			<div class="col-auto text-end">
				<button
					type="button"
					class="btn btn-success"
					data-bs-toggle="modal"
					data-bs-target="#create_supplier"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('new_supplier')}
				</button>
			</div>
		</div>
	</div>

	<div style="height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
		<table class="table table-hover table-bordered table-light">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th class="text-center">{locale.T('n')}</th>
					<th>{locale.T('name')}</th>
					<th>{locale.T('company')}</th>
					<th>{locale.T('contact')}</th>
					<th>{locale.T('address')}</th>
					<th>{locale.T('other')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_suppliers as item, index}
					<tr class="text-center">
						<td>
							{index + n}
						</td>
						<td>
							{item?.name ?? ''}
						</td>
						<td>
							{item?.company_name ?? ''}
						</td>
						<td>
							{item?.contact ?? ''}
						</td>
						<td>
							{item?.address ?? ''}
						</td>
						<td>
							<div class=" m-0 p-0">
								<button
									onclick={() => pushSupplierId(item.id)}
									aria-label="createproduct"
									type="button"
									class="btn btn-primary btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#create_supplier"
									><i class="fa-solid fa-file-pen"></i>
								</button>
								<button
									onclick={() => pushSupplierId(item.id)}
									aria-label="deletemodal"
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
								<a href="/product/supplier/{item.id}" class="btn btn-success btn-sm">
									{locale.T('invoice')}
								</a>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations {items} bind:n />
	</div>
</div>
