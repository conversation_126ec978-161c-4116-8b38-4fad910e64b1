<script lang="ts">
	import type { PageServerData, ActionData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}
	let { data, form }: Props = $props();
	let { get_staff } = $derived(data);
	let loading = $state(false);
</script>

<div class="card bg-light">
	<Form
		class="card-body"
		action={get_staff.user?.id ? '?/update_user' : '?/register'}
		method="post"
		bind:loading
		reset={false}
	>
		{form?.message}
		<div class="pb-3">
			<input value={get_staff?.user?.id} type="hidden" name="user_id" />
			<input value={get_staff?.id} type="hidden" name="staff_id" />
			<label for="username">{locale.T('username')}</label>
			{#if form?.username}
				<span class="text-danger">{locale.T('invalid_username')}</span>
			{/if}
			<input
				value={get_staff?.user?.username ?? ''}
				required
				name="username"
				type="text"
				class="form-control"
				id="username"
			/>
		</div>
		<div class="pb-3">
			<label for="password">{locale.T('new_password')}</label>
			<input
				placeholder="*****"
				required
				name="password"
				type="password"
				class="form-control"
				id="password"
			/>
		</div>
		<div class="float-end">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
