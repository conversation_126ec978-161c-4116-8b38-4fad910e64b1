<script lang="ts">
	import { page } from '$app/state';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { dobToAge } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	import type { LayoutServerData } from './$types';
	interface Props {
		data: LayoutServerData;
		children?: import('svelte').Snippet;
	}

	let { data, children }: Props = $props();
	let { get_imagerie_request, get_visit, patient_info } = $derived(data);

	let age_p_visit = $derived(
		dobToAge(
			get_imagerie_request?.visit?.patient.dob ?? '',
			get_imagerie_request?.visit?.date_checkup ?? ''
		)
	);
</script>

<div class="row">
	<div class="col-sm-6">
		<a href="/imagerie" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/imagerie" class="btn btn-link p-0 text-secondary"
					><i class="nav-icon fas fa-image"></i>
					{locale.T('imagerie')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/imagerie/result" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-file-circle-check"></i>
					{locale.T('result')}
				</a>
			</li>
		</ol>
	</div>
</div>
<PatientInfo {patient_info} />

<br />
<div class="row">
	<div class="col-6">
		{#if !get_imagerie_request.resultImagerie.some((e) => e.result)}
			<a
				class:active={page.url.pathname === '/imagerie/result/general'}
				href="/imagerie/result/general?imagerie_request_id={get_imagerie_request.id}&group_id={get_imagerie_request
					.product?.group_id}"
				class="btn btn-outline-primary w-100"
				>General Report
			</a>
		{:else}
			<button disabled class="btn btn-outline-primary w-100"
				>General Report {get_imagerie_request.result}
			</button>
		{/if}
	</div>
	<div class="col-6">
		{#if !get_imagerie_request.result}
			<a
				class:active={page.url.pathname === '/imagerie/result/ob'}
				href="/imagerie/result/ob?imagerie_request_id={get_imagerie_request.id}"
				class="btn btn-outline-primary w-100"
				>OB Report
			</a>
		{:else}
			<button disabled class="btn btn-outline-primary w-100">OB Report </button>
		{/if}
	</div>
</div>

{@render children?.()}
