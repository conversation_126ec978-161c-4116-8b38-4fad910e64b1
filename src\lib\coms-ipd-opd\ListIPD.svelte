<script lang="ts">
	import type { PageServerData } from '../../routes/(dash)/patient/ipd/$types';
	import { store } from '$lib/store/store.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { enhance } from '$app/forms';
	import { locale } from '$lib/translations/locales.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import ActiveVisit from '$lib/coms-ipd-opd/ActiveVisit.svelte';
	import GenderAge from '$lib/coms/GenderAge.svelte';
	import type { SubmitFunction } from '@sveltejs/kit';
	type Data = Pick<PageServerData, 'get_pregress_notes'>;
	interface Props {
		data: Data;
		n: number;
	}

	let { data, n }: Props = $props();
	let progress_note_id = $state(0);
	let editEtiology = $state(false);
	let { get_pregress_notes } = $derived(data);
	const onSubmit: SubmitFunction = () => {
		store.globalLoading = true;
		return async ({ update }) => {
			await update({ reset: false });
			store.globalLoading = false;
			editEtiology = false;
		};
	};
	let total_male = $derived(
		get_pregress_notes.filter((e) => e.patient.gender.toLowerCase() === 'male').length
	);
</script>

<DeleteModal
	delete_modal="delete_ipd"
	action="/patient/ipd?/delete_progress_note"
	id={progress_note_id}
/>

<table class="table table-bordered table-light text-nowrap table-hover mb-0">
	<thead class="sticky-top table-active">
		<tr class="text-center">
			<th style="width: 3%;">{locale.T('n')}</th>
			<th style="width: 7%;">{locale.T('date')}</th>
			<th style="width: 5%;">{locale.T('id')} </th>
			<th>{locale.T('patient_name')}</th>
			<th>{locale.T('symptoms')}</th>
			<th>{locale.T('department')}</th>
			<th>{locale.T('doctor')}</th>
			<th>{locale.T('room')}</th>
			<th colspan="2">{locale.T('payment')}</th>
			<th>{locale.T('status')}</th>
			<th></th>
		</tr>
	</thead>
	<tbody class="table-sm">
		{#each get_pregress_notes as item, index}
			{@const activeDepartment = item?.activeDepartment.find((e) => e.active === true)}
			<tr class="text-center">
				<td class="text-left">{index + n}</td>
				<td>
					<DDMMYYYYFormat style="date" date={item.date_checkup} />
					<br />
					<DDMMYYYYFormat style="time" date={item.date_checkup} />
				</td>

				<td class="text-center">VS{item.id ?? ''} <br /> PT{item.patient_id ?? ''}</td>
				<td>
					{#if item.status === 'LOADING'}
						<ActiveVisit
							active_department_id={activeDepartment?.id}
							action="/patient/ipd?/active_visit"
							visit_id={item.id}
							class="btn btn-link text-danger"
						>
							{item.patient?.name_khmer} <br />
							{item.patient?.name_latin}

							<GenderAge dob={item.patient.dob} date={new Date()} gender={item.patient.gender} />
						</ActiveVisit>
					{:else}
						<a aria-label="linksubjective" class="btn btn-link" href="/ipd/{item.id}/progress-note">
							{item.patient?.name_khmer} <br />
							{item.patient?.name_latin}
							<GenderAge dob={item.patient.dob} date={new Date()} gender={item.patient.gender} />
						</a>
					{/if}
				</td>
				<td>
					{#if editEtiology && item.id === progress_note_id}
						<form
							onfocusout={() => (editEtiology = false)}
							data-sveltekit-keepfocus
							use:enhance={onSubmit}
							method="post"
							action="/patient/ipd?/update_etiology"
							onchange={(e) => {
								e.currentTarget.requestSubmit();
								editEtiology = false;
							}}
						>
							<input
								name="etiology"
								class="bg-primary-subtle form-control text-center"
								value={item.etiology}
								type="text"
							/>
							<input type="hidden" name="id" value={item.id} />
						</form>
					{:else}
						<button
							class="btn text-break"
							onclick={() => {
								editEtiology = true;
								progress_note_id = item.id;
							}}>{item.etiology ?? ''}</button
						>
					{/if}
				</td>
				<td class="text-start">
					{#each item.activeDepartment || [] as iitem}
						{#if iitem.active === true}
							{iitem?.department?.products}
							<a
								href="/ipd/move?progress_note_id={item.id}&patient_id={item.patient_id}"
								aria-label="change_department"
								class={item.activeDepartment.length > 1
									? 'btn btn-link p-0 text-end float-end '
									: 'btn btn-link p-0 text-end float-end text-secondary'}
								><i class="fa-solid fa-arrow-right-arrow-left"></i></a
							>
						{/if}
					{/each}
				</td>
				<td>
					<div>
						{activeDepartment?.getter?.name_khmer} <br />
						{activeDepartment?.getter?.name_latin}
					</div>
				</td>

				<td>
					{#each item?.activeBed || [] as iitem}
						{#if iitem.active === true}
							<span class="badge text-bg-success">{iitem.bed?.room?.room ?? ''}</span><br />
							<span class="badge text-bg-success">{iitem.bed?.room?.product?.products ?? ''}</span>
						{/if}
					{/each}
				</td>
				<td>
					<a
						href="/patient/payment-service?id={item.billing?.paymentService?.id}&billing_id={item
							.billing?.id}">{item.billing?.serviceType?.by || locale.T('none_data')}</a
					>
				</td>

				<td>
					{#if item.billing?.status === 'checking'}
						<button type="button" class="btn btn-warning btn-sm"
							><i class="fa-regular fa-circle-xmark"></i> {locale.T('not_yet_paid')}</button
						>
					{:else if item.billing?.status === 'paying'}
						<button type="button" class="btn btn-danger btn-sm"
							><i class="fa-solid fa-spinner fa-spin"></i>
							{locale.T('send_to_payment')}</button
						>
					{:else}
						<button type="button" class="btn btn-primary btn-sm"
							><i class="fa-regular fa-circle-check"></i> {locale.T('already_paid')}</button
						>
					{/if}
				</td>

				<td>
					{#if item.status === 'LOADING'}
						<button class="btn btn-warning btn-sm"
							><i class="fa-solid fa-arrow-down-1-9"></i> {locale.T('loading')}</button
						>
					{/if}
					{#if item.status === 'CHECKING'}
						<button class="btn btn-success btn-sm"
							><i class="fa-solid fa-spinner fa-spin"></i>
							{locale.T('checking')}</button
						>
					{/if}
					{#if item.status === 'DONE'}
						<button class="btn btn-primary btn-sm"
							><i class="fa-regular fa-circle-check"></i> {locale.T('done')}</button
						>
					{/if}
				</td>
				<td>
					<div>
						<button
							aria-label="deletemodal"
							onclick={() => {
								progress_note_id = 0;
								progress_note_id = item.id;
							}}
							type="button"
							class="btn btn-danger btn-sm"
							data-bs-toggle="modal"
							data-bs-target="#delete_ipd"
							><i class="fa-solid fa-trash-can"></i>
						</button>
					</div>
				</td>
			</tr>
		{/each}
		<tr class="text-center table-success">
			<td colspan="12">
				{locale.T('total')}: {get_pregress_notes.length}
				{locale.T('people')},
				{locale.T('male')}: {total_male}
				{locale.T('female')}: {get_pregress_notes.length - total_male}
			</td>
		</tr>
	</tbody>
</table>
