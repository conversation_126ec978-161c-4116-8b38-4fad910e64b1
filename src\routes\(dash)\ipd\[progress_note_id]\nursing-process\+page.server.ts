import { db } from '$lib/server/db';
import { desc, eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { activeDepartment, nursingProcess, progressNote } from '$lib/server/schemas';
import logError from '$lib/server/utils/logError';
import { redirect } from '@sveltejs/kit';
import { YYYYMMDD_Format } from '$lib/server/utils';

export const load: PageServerLoad = async ({ params }) => {
	const { progress_note_id } = params;
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +progress_note_id),
		with: {
			patient: true,
			activeDepartment: {
				with: {
					department: true
				},
				orderBy: desc(activeDepartment.datetime_in)
			},
			nursingProcess: {
				with: {
					nursingSign: true,
					activeDepartment: true
				},
				orderBy: desc(nursingProcess.datetime)
			}
		}
	});
	return {
		progress_note_id,
		get_progress_note
	};
};

export const actions: Actions = {
	delete_nursing_process: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(nursingProcess)
			.where(eq(nursingProcess.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_nursing_process: async ({ request, params, locals, url }) => {
		const body = await request.formData();
		const progress_note_id = params.progress_note_id;
		const { nursing_process_id, accessment, health_problems, actions, evolution, time, date } =
			Object.fromEntries(body) as Record<string, string>;
		const get_progress_note = await db.query.progressNote.findFirst({
			where: eq(nursingProcess.id, +progress_note_id),
			with: {
				activeDepartment: {
					with: {
						department: true
					}
				}
			}
		});
		const get_active_department = get_progress_note?.activeDepartment.find(
			(e) => e.active === true
		);
		if (!nursing_process_id) {
			await db
				.insert(nursingProcess)
				.values({
					nursing_sign: locals?.user?.staff_id,
					datetime: YYYYMMDD_Format.datetime(new Date()),
					accessment: accessment,
					health_problems: health_problems,
					actions: actions,
					evolution: evolution,
					progress_note_id: +progress_note_id,
					active_department_id: get_active_department?.id
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (nursing_process_id) {
			await db
				.update(nursingProcess)
				.set({
					accessment: accessment,
					health_problems: health_problems,
					actions: actions,
					evolution: evolution,
					datetime: date.toString().slice(0, 11).concat(' ').concat(time.toString())
				})
				.where(eq(nursingProcess.id, +nursing_process_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		redirect(302, `/ipd/${progress_note_id}/nursing-process`);
	}
};
