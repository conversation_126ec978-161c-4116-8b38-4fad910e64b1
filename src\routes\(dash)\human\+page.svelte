<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { dobToAge } from '$lib/helper';

	interface Props {
		form: ActionData;
		data: PageServerData;
	}

	let { form, data }: Props = $props();
	let staff_id = $state<number>();
	let { get_staffs, items, get_roles } = $derived(data);
	let find_staff = $derived(get_staffs.filter((e) => e.id === staff_id));
	let n: number = $state(1);
</script>

<DeleteModal action="?/delete_staff" id={find_staff[0]?.id} />

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('staff')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/staff" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-users nav-icon"></i>
					{locale.T('staff')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col">
				<HeaderQuery>
					<div class="col-sm-3">
						<HandleQ />
					</div>
					<div class="col-sm-3">
						<select class="form-control" name="role" id="role">
							<option selected value="">ALL</option>
							{#each get_roles as item}
								<option value={item.role}>{item.role}</option>
							{/each}
						</select>
					</div>
				</HeaderQuery>
			</div>

			<div class="col-auto">
				<a href="/human/dashboard" type="button" class="btn btn-success"
					><i class="fa-solid fa-chart-line"></i>
					{locale.T('dashboard')}
				</a>
			</div>
		</div>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive">
		<div class="row g-1">
			{#each get_staffs as item (item.id)}
				<div class="col-md-3 col-lg-4">
					<div class="card shadow-sm" style="width: 540px; margin: 0 auto;height: 200px;">
						<div class="row g-0">
							<div class="col-md-4  p-3 text-center">
								<!-- svelte-ignore a11y_img_redundant_alt -->
								<img
									style="width: 120px;height: 120px;"
									src={item?.uploads?.filename ? `${item?.uploads?.filename}` : '/no-user.png'}
									class="rounded-circle img-thumbnail"
									alt="Profile Picture"
								/>
								<div class="mt-2">
									{#if item?.datetime_stop}
										<button class="btn btn-danger btn-sm m-1 p-1">{locale.T('stop_working')}</button
										>
									{:else}
										<button class="btn btn-primary btn-sm m-1 p-1">{locale.T('working')}</button>
									{/if}
								</div>
							</div>
							<div class="col-md-8">
								<div class="card-body">
									<a
										href="/human/create/profile?staff_id={item.id}&province_id={item.province_id}&district_id={item.district_id}&commune_id={item.commune_id}&village_id={item.village_id}"
										class=" fs-5"
									>
										{item?.name_khmer}
										({item?.name_latin})
									</a>
									<p class="card-text text-truncate text-muted my-1">
										<i class="fas fa-briefcase"></i>
										{item?.specialist}
									</p>
									<p class="card-text text-truncate text-muted">
										<i class="fa-solid fa-user-doctor"></i>
										{#each item?.staffToDemartment as department (department.department_id)}
											{department?.department?.products ?? ''}
										{/each}
									</p>
									<div class="border-top pt-2">
										<div class="row text-center">
											<div class="col">
												<h6>{locale.T('id')}</h6>
												<strong>ST{item?.id}</strong>
											</div>
											<div class="col border-start">
												<h6>{locale.T('age')}</h6>
												<strong>{dobToAge(item?.dob, null)}</strong>
											</div>
											<div class="col border-start">
												<h6>{locale.T('gender')}</h6>
												<strong
													>{item?.gender?.toLowerCase() === 'male'
														? locale.T('male')
														: item?.gender?.toLowerCase() === 'female'
															? locale.T('female')
															: locale.T('none')}</strong
												>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
