import * as fs from 'fs/promises';
import path from 'path';
import sharp from 'sharp';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import { db } from '../db';
import { uploads } from '../schemas';
import { eq } from 'drizzle-orm';
const location = 'uploads';
const location_pdf = 'uploads/pdf';
function generateRandomId(bytes: number): string {
	const randomBytes = crypto.getRandomValues(new Uint8Array(bytes));
	return encodeBase32LowerCase(randomBytes);
}
export async function uploadFile(file: File) {
	if (!file?.size || file.size > 10000000) return;
	try {
		const uniqueId = generateRandomId(20);
		const fileExtension = file.type.split('/')[1];
		if (!['jpeg', 'png', 'jpg', 'pdf'].includes(fileExtension)) return;
		if (fileExtension === 'pdf') {
			const folderPath = path.join(process.cwd(), location_pdf);
			await fs.mkdir(folderPath, { recursive: true });
			const filePath = path.join(folderPath, `${uniqueId}.${fileExtension}`);
			const arrayBuffer = await file.arrayBuffer();
			await fs.writeFile(filePath, Buffer.from(arrayBuffer));
			return `pdf/${uniqueId}.${fileExtension}`;
		} else {
			// Create folder name based on current year and month
			const date = new Date();
			const folderName = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
			// Create the folder if it doesn't exist
			const folderPath = path.join(process.cwd(), location, folderName);
			await fs.mkdir(folderPath, { recursive: true });
			const filePath = path.join(folderPath, `${uniqueId}.${fileExtension}`);
			const arrayBuffer = await file.arrayBuffer(); // orignal size can use this is down need compress file
			const optimizedBuffer = await sharp(Buffer.from(arrayBuffer))
				.resize(1000)
				.jpeg({ progressive: true, force: false, quality: 50 })
				.png({ progressive: true, force: false, quality: 50 })
				.toBuffer();

			await fs.writeFile(filePath, optimizedBuffer);
			return `/${location}/${folderName}/${uniqueId}.${fileExtension}`;
		}
	} catch (e) {
		console.error('Error uploading file:', e);
		throw e;
	}
}
export async function updateFile(file: File, oldFileName: string) {
	if (!file.size) return;
	try {
		await deleteFile(oldFileName);
		await db.delete(uploads).where(eq(uploads.filename, oldFileName));
		return await uploadFile(file);
	} catch (e) {
		console.error('Error updating file:', e);
		throw e;
	}
}

export async function deleteFile(fileName: string): Promise<void> {
	if (!fileName) return;
	try {
		if (fileName.includes('.pdf')) {
			const filePath = path.join(process.cwd(), location, fileName);
			await fs.unlink(filePath);
			return;
		} else {
			const filePath = path.join(process.cwd(), location, fileName);
			await fs.unlink(filePath);
			// Remove empty folder if it exists
			const folderPath = path.dirname(filePath);
			const uploads_ = await fs.readdir(folderPath);
			if (uploads_.length === 0) {
				await fs.rmdir(folderPath);
			}
			await db.delete(uploads).where(eq(uploads.filename, fileName));
		}
	} catch (e) {
		console.error('Error deleting file:', e);
	}
}
export async function getBuffer(file: File) {
	const arrayBuffer = await file.arrayBuffer();
	return Buffer.from(arrayBuffer).toString('base64');
}
