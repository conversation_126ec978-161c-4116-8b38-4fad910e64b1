<script lang="ts">
	import type { PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let backup_name = $state('');
	let loading = $state(false);
	let { list_files } = $derived(data);
	let file: any = $state();
</script>

<DeleteModal action="?/delete" slug={backup_name} />

<div class="row">
	<div class="col-sm-6">
		<h1 class="m-0">{locale.T('backup_list')}</h1>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-database"></i>
					{locale.T('backup')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<Form class="col" enctype="multipart/form-data" method="post" bind:loading action="?/backup">
				<div class="row pb-2">
					<div class="col-sm-6">
						<input
							bind:value={file}
							type="file"
							name="sql"
							accept=".sql"
							class="custom-file-input"
							id="exampleInputFile"
						/>
					</div>
					<div class="col-sm-6">
						<button disabled={!file} type="submit" class="btn btn-info">
							<i class="fa-solid fa-arrows-rotate"></i>
							{locale.T('restore')}
						</button>
						<button formaction="?/backup" class="btn btn-success">
							<i class="fa-solid fa-database"></i>
							{locale.T('backup')}
						</button>
					</div>
				</div>
			</Form>
			<Form class="col" action="?/backup_all" method="post">
				<button type="submit" class="btn btn-info">
					<i class="fa-solid fa-database"></i>
					{locale.T('backup')}
				</button>
			</Form>
		</div>
	</div>
	<div class="card-body table-responsive p-0">
		<table class="table table-bordered table-hover text-nowrap table-light">
			<tbody class="table-light">
				{#each list_files || [] as item}
					<tr>
						<td style="width: 5%;">{locale.T('date')}</td>
						<td style="width: 20%;">
							{item.date}
						</td>
						<td>
							<div>
								<Form bind:loading action="?/restore" method="post">
									<input type="hidden" value={item.name} name="name_backup" />
									<button type="submit" class="btn btn-info btn-sm"
										><i class="fa-solid fa-arrows-rotate"></i>
										{locale.T('restore')}
									</button>
									<a download href="/backup/{item}" class="btn btn-success btn-sm"
										><i class="fa-solid fa-download"></i> {locale.T('download')}
									</a>
									<a
										href={'#'}
										onclick={() => {
											backup_name = '';
											backup_name = item.name || '';
										}}
										type="button"
										class="btn btn-danger btn-sm"
										data-bs-toggle="modal"
										data-bs-target="#delete_modal"
										><i class="fa-solid fa-trash-can"></i>
										{locale.T('delete_backup')}
									</a>
								</Form>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
