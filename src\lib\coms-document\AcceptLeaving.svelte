<script lang="ts">
	import InputDocument from '$lib/coms-form/InputDocument.svelte';
	import type { TAddress, TDocumentSetting, TFields } from '$lib/type';
	import KhDateTime from '$lib/coms-document/KhDateInput.svelte';
	import { khmerDate } from '$lib/helper';
	import Header from '$lib/coms-document/Header.svelte';
	interface Prop {
		p_date_checkup: string;
		p_nation: string;
		p_date_checkout: string;
		p_name: string;
		address?: TAddress;
		fields: TFields[];
		title_khm: string;
		title_eng: string;
		get_document_setting?: TDocumentSetting;
		logo: string;
		death_leave?: boolean;
	}
	let {
		fields,
		p_name,
		logo,
		title_eng,
		p_nation,
		title_khm,
		address,
		p_date_checkup,
		p_date_checkout,
		get_document_setting,
		death_leave = false
	}: Prop = $props();
	let default_address = $derived(
		`${address?.village?.type ?? ''} ${address?.village?.name_khmer ?? ''} ${address?.commune?.type ?? ''} ${address?.commune?.name_khmer ?? ''} ${address?.district?.type ?? ''} ${address?.district?.name_khmer ?? ''} ${address?.provice?.type ?? ''} ${address?.provice?.name_khmer ?? ''}`
	);
	let address_ = $derived(
		fields.find((e) => e.name === 'address')?.result
			? fields.find((e) => e.name === 'address')?.result
			: default_address
	);
	let comment = $derived(fields.find((e) => e.name === 'comment')?.result ?? '');
	let send_to = $derived(fields.find((e) => e.name === 'send_to')?.result ?? '');
	let n = $derived(fields.find((e) => e.name === 'n')?.result ?? '');
	let date_1 = $derived(fields.find((e) => e.name === 'date_1')?.result ?? '');
	let diagnosis_in = $derived(fields.find((e) => e.name === 'diagnosis_in')?.result ?? '');
	let diagnosis_out = $derived(fields.find((e) => e.name === 'diagnosis_out')?.result ?? '');
	let icd_10_1 = $derived(fields.find((e) => e.name === 'icd_10_1')?.result ?? '');
	let icd_10_2 = $derived(fields.find((e) => e.name === 'icd_10_2')?.result ?? '');
	let date_2 = $derived(fields.find((e) => e.name === 'date_2')?.result ?? '');
	let death = $derived(fields.some((e) => e.result === 'death'));
	let pregnant_of_death = $derived(fields.some((e) => e.result === 'pregnant_of_death'));
	let datetime_death = $derived(fields.find((e) => e.name === 'datetime_death')?.result ?? '');
	let reson_of_death = $derived(fields.find((e) => e.name === 'reson_of_death')?.result ?? '');
</script>

<input type="hidden" name="title" value="accept_leaving" />
<main style="max-width: 1200px;">
	<Header {get_document_setting} {logo} {n} {title_eng} {title_khm} />
	<div class="text-center">
		<h4
			style="color: {get_document_setting?.title_color}"
			class="kh_font_muol_light text-decoration-underline"
		>
			{#if death || pregnant_of_death}
				លិខិតមរណភាព
			{:else}
				លិខិតអនុញ្ញាតចេញពីពេទ្យ
			{/if}
		</h4>
	</div>
	<br />
	<div class="section fs-5">
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			អ្នកជំងឺឈ្មោះ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				readonly
				value={p_name}
				width="760px"
				type="text"
			/>
			សញ្ជាតិ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				readonly
				value={p_nation}
				width="100px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			{`អាសយដ្ឋាន ៖`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="address"
				value={address_ ?? ''}
				width="930px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			ថ្ងៃខែឆ្នាំចូលសម្រាកពេទ្យ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				readonly
				value={khmerDate(p_date_checkup, 'datetime')}
				width="850px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			រោគវិនិច្ឆ័យពេល ចូល
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="diagnosis_in"
				value={diagnosis_in}
				width="870px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			(ICD-10 ) <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={icd_10_1}
				name="icd_10_1"
				width="960px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			ថ្ងៃខែឆ្នាំចេញពីមន្ទីរពេទ្យ <InputDocument
				readonly
				value={khmerDate(p_date_checkout, 'datetime')}
				width="855px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			រោគវិនិច្ឆ័យពេល
			{#if death || pregnant_of_death}
				ស្លាប់
			{:else}
				ចេញ
			{/if}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="diagnosis_out"
				value={diagnosis_out}
				width="870px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			(ICD-10 ) <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={icd_10_2}
				name="icd_10_2"
				width="956px"
				type="text"
			/>
		</div>
		<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
			ចំនួនថ្ងៃសម្រាក់ព្យាបាល <InputDocument readonly value={''} width="850px" type="text" />
		</div>
		{#if !death_leave}
			<div
				style="color: {get_document_setting?.title_color}"
				class="mb-2 text-center kh_font_muol_light text-decoration-underline"
			>
				ស្ថានភាពពេលចេញ
			</div>
			<div class="fs-5 checkbox-group">
				<div class="row justify-content-center mb-2">
					<div class="col-auto">
						<div class="form-check">
							<input
								checked={fields.some((e) => e.result === 'leave_with_permission')}
								class="form-check-input"
								type="checkbox"
								value="leave_with_permission"
								name="box"
								id="leave_with_permission"
							/>
							<label class="form-check-label" for="leave_with_permission">
								ចេញដោយមានការអនុញ្ញាត</label
							>
						</div>
					</div>
					<div class="col-auto">
						<div class="form-check">
							<input
								checked={fields.some((e) => e.result === 'leave_without_permission')}
								class="form-check-input"
								type="checkbox"
								value="leave_without_permission"
								name="box"
								id="leave_without_permission"
							/>

							<label class="form-check-label" for="leave_without_permission">
								ចេញដោយគ្មានការអនុញ្ញាត</label
							>
						</div>
					</div>
					<div class="col-auto">
						<div class="form-check">
							<input
								checked={fields.some((e) => e.result === 'normal_leave')}
								class="form-check-input"
								type="checkbox"
								value="normal_leave"
								name="box"
								id="normal_leave"
							/>

							<label class="form-check-label" for="normal_leave"> ចេញដោយបញ្ចូន </label>
						</div>
					</div>
					<div class="col-auto">
						<div class="form-check">
							<input
								bind:checked={death}
								class="form-check-input"
								type="checkbox"
								value="death"
								name="box"
								id="death"
							/>
							<label class="form-check-label" for="death"> ស្លាប់</label>
						</div>
					</div>
				</div>
				<div
					style="color: {get_document_setting?.title_color}"
					class="mb-2 text-center kh_font_muol_light text-decoration-underline"
				>
					លទ្ធផលនៃការព្យាបាល
				</div>
				<div class="row justify-content-center mb-2">
					<div class="col-auto">
						<div class="form-check">
							<input
								checked={fields.some((e) => e.result === 'heal')}
								class="form-check-input"
								type="checkbox"
								value="heal"
								name="box"
								id="heal"
							/>
							<label class="form-check-label" for="heal"> ជាសះស្បើយ</label>
						</div>
					</div>
					<div class="col-auto">
						<div class="form-check">
							<input
								checked={fields.some((e) => e.result === 'relief')}
								class="form-check-input"
								type="checkbox"
								value="relief"
								name="box"
								id="relief"
							/>
							<label class="form-check-label" for="relief"> ធូរស្រាល </label>
						</div>
					</div>
					<div class="col-auto">
						<div class="form-check">
							<input
								checked={fields.some((e) => e.result === 'not_relief')}
								class="form-check-input"
								type="checkbox"
								value="not_relief"
								name="box"
								id="not_relief"
							/>
							<label class="form-check-label" for="not_relief"> មិនធូរស្រាល </label>
						</div>
					</div>
					<div class="col-auto">
						<div class="form-check">
							<input
								bind:checked={pregnant_of_death}
								class="form-check-input"
								type="checkbox"
								value="pregnant_of_death"
								name="box"
								id="pregnant_of_death"
							/>
							<label class="form-check-label" for="pregnant_of_death">
								តើអ្នកស្លាប់ជាស្ត្រីមានផ្ទៃពោះ, សម្រាលកូន ឬ ៤២ថ្ងៃក្រោយសម្រាលកូន ឬទេ?)
							</label>
						</div>
					</div>
				</div>
			</div>
		{/if}

		{#if death || pregnant_of_death}
			<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
				មូលហេតុដោយ <InputDocument
					style="color: {get_document_setting?.text_input_color}"
					name="reson_of_death"
					value={reson_of_death}
					width="920px"
					type="text"
				/>
			</div>
			<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
				បានទទួលមរណភាពនៅថ្ងៃទី

				<KhDateTime width="810px" type="datetime-local" date={datetime_death} name="datetime_death" />
			</div>
		{:else}
			<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
				បញ្ចូនទៅកាន់ <InputDocument
					style="color: {get_document_setting?.text_input_color}"
					name="send_to"
					value={send_to}
					width="940px"
					type="text"
				/>
			</div>
			<div style="color: {get_document_setting?.text_body_color}" class="mb-2">
				ដំបូន្មានរបស់គ្រូពេទ្យព្យាបាលៈ <InputDocument
					style="color: {get_document_setting?.text_input_color}"
					value={comment}
					name="comment"
					width="805px"
					type="text"
				/>
			</div>
		{/if}

		<br />
		<div class="text-end fs-5" style="margin-right: 5em;">
			<KhDateTime date={date_1} name="date_1" />
			<div style="padding-right: 55px;font-weight: bold;">គ្រូពេទ្យព្យាបាល</div>
		</div>
		<div style="margin-left: 5em;" class="fs-5">
			<KhDateTime date={date_2} name="date_2" />
			<div style="padding-left: 45px;">បានឃើញ និងឯកភាព</div>
			<div style="padding-left: 20px;font-weight: bold;">ប្រធានគ្លីនិក/ប្រធានមន្ទីរពេទ្យ</div>
		</div>
	</div>
</main>
