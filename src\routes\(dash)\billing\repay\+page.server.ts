import { db } from '$lib/server/db';
import { and, eq, gt, inArray, ne, desc } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { billing, uploads, paymentType, payment } from '$lib/server/schemas';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { fail } from '@sveltejs/kit';
import { fileHandle } from '$lib/server/upload';
import { billingProcess } from '$lib/server/models';
import logError from '$lib/server/utils/logError';

export const load: PageServerLoad = async ({ url }) => {
	const id = url.searchParams.get('billing_id') ?? '';
	const get_currency = await db.query.currency.findFirst({});
	const get_billing = await db.query.billing.findFirst({
		where: eq(billing.id, +id || 0),
		with: {
			payment: {
				with: {
					paymentType: true,
					staff: true
				}
			},
			patient: true,
			visit: {
				with: {
					presrciption: {
						with: {
							product: true
						}
					},
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					department: true,
					progressNote: {
						with: {
							activeBed: {
								with: {
									bed: {
										with: {
											ward: true,
											room: {
												with: {
													product: true
												}
											}
										}
									}
								}
							},
							patient: {
								with: {
									commune: true,
									district: true,
									provice: true,
									village: true
								}
							}
						}
					}
				}
			},
			progressNote: {
				with: {
					activeBed: {
						with: {
							bed: {
								with: {
									ward: true,
									room: {
										with: {
											product: true
										}
									}
								}
							}
						}
					},
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					}
				}
			}
		}
	});
	const get_payment_types = await db.query.paymentType.findMany({
		orderBy: desc(paymentType.id)
	});
	const get_billings_due = await db.query.billing.findMany({
		where: and(
			gt(billing.balance, 0),
			eq(billing.patient_id, get_billing?.patient_id || 0),
			ne(billing.id, get_billing?.id || 0)
		)
	});
	const get_visit = get_billing?.visit;
	let patient_info;
	if (get_visit) {
		patient_info = {
			...get_visit?.patient,
			date_checkup: get_visit?.date_checkup
		};
	}
	if (get_visit?.progressNote) {
		patient_info = {
			...get_visit?.progressNote?.patient,
			date_checkup: get_visit?.progressNote?.date_checkup
		};
	}
	if (get_billing?.progressNote) {
		patient_info = {
			...get_billing?.progressNote?.patient,
			date_checkup: get_billing?.progressNote?.date_checkup ?? ''
		};
	}
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'billing'), eq(uploads.related_id, get_billing?.id || 0))
	});
	const get_upload_payment = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'payment'),
			inArray(uploads.related_id, get_billing?.payment.map((item) => item.id) || [])
		)
	});

	return {
		get_billing: {
			...get_billing,
			uploads: get_upload,
			payment: get_billing?.payment.map((item) => {
				return {
					...item,
					uploads: get_upload_payment.find((upload) => upload.related_id === item.id)
				};
			})
		},
		get_payment_types,
		get_currency,
		patient_info,
		get_billings_due
	};
};
export const actions: Actions = {
	repayment: async ({ request, locals, url }) => {
		const body = await request.formData();
		const { value, payment_type_id, billing_id, note, disc, tax, payment_id } = Object.fromEntries(
			body
		) as Record<string, string>;
		const staff_id = locals.user?.staff_id;
		const file = body.get('file') as File;
		const validErr = {
			value: false,
			payment_type_id: false,
			billing_id: false,
			staff_id: false
		};
		if (isNaN(+value) || +value <= 0) validErr.value = true;
		if (!payment_type_id) validErr.payment_type_id = true;
		if (!billing_id) validErr.billing_id = true;
		if (!staff_id) validErr.staff_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const dateitme = YYYYMMDD_Format.datetime(new Date());
		let paymentId: number | null = +payment_id;
		if (payment_id) {
			await db
				.update(payment)
				.set({
					note: note,
					payment_type_id: +payment_type_id,
					value: +value,
					staff_id: Number(staff_id)
				})
				.where(eq(payment.id, +payment_id));
		}
		if (!payment_id) {
			const create_payment = await db
				.insert(payment)
				.values({
					billing_id: +billing_id,
					note: note,
					payment_type_id: +payment_type_id,
					value: +value,
					datetime: dateitme,
					staff_id: Number(staff_id)
				})
				.$returningId();
			paymentId = create_payment[0]?.id;
		}
		if (file.size) {
			await fileHandle.insert(file, +paymentId, 'payment');
		}
		await billingProcess({
			billing_id: +billing_id,
			disc: disc,
			tax: +tax,
			note: note,
			body: body,
			url: url
		});
	},
	delete_payment: async ({ request, url }) => {
		const body = await request.formData();
		const { id: payment_id } = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			payment_id: false
		};

		if (!payment_id) validErr.payment_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const get_upload = await db.query.uploads.findFirst({
			where: and(eq(uploads.related_type, 'payment'), eq(uploads.related_id, +payment_id))
		});
		const get_payment = await db.query.payment.findFirst({
			where: eq(payment.id, +payment_id),
			with: {
				billing: true
			}
		});
		if (get_upload?.filename) await fileHandle.drop(get_upload?.filename);
		const get_billing = await db.query.billing.findFirst({
			where: eq(billing.id, get_payment?.billing_id || 0)
		});
		if (get_billing) {
			await db
				.delete(payment)
				.where(eq(payment.id, +payment_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
			await billingProcess({
				billing_id: get_billing!.id,
				disc: get_billing!.discount,
				note: get_billing!.note ?? '',
				tax: get_billing!.tax,
				body: body,
				url: url
			});
		}
	}
};
