services:
  svelte:
    container_name: svelte
    restart: always
    depends_on:
      db:
        condition: service_started
        required: true
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ~/uploads:/home/<USER>/app/uploads
      - /app/build
    networks:
      - hms_network
  db:
    image: mysql:8.0
    container_name: db
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    expose:
      - 3306
    ports:
      - 3306:3306
    environment:
      MYSQL_ROOT_PASSWORD: coffeelake
      MYSQL_DATABASE: hms
    volumes:
      - ~/mysql-data:/var/lib/mysql
    networks:
      - hms_network
  adminer:
    container_name: adminer
    image: adminer
    
    restart: always
    networks:
      - hms_network
  caddy:
    container_name: caddy
    image: caddy:2.8.4-alpine
    restart: always
    networks:
      - hms_network
    ports:
      - 80:80
      - 443:443
      - 8080:8080
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - ~/uploads:/uploads
      - ~/caddy-data:/data/caddy

networks:
  hms_network:
    driver: bridge
