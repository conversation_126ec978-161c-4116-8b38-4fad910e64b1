import { db } from '$lib/server/db';
import { uploads, inventory, product } from '$lib/server/schemas';
import { eq, desc, and } from 'drizzle-orm';
import type { PageServerLoad, Actions } from './$types';
import { fail } from '@sveltejs/kit';
import logError from '$lib/server/utils/logError';
import { pagination } from '$lib/server/utils';
// import { get_product_qty } from '$lib/server/models/productQuery';
export const load = (async ({ params, parent, url }) => {
	await parent();
	const get_currency = await db.query.currency.findFirst({});
	const { id } = params;
	const get_product = await db.query.product.findFirst({
		where: eq(product.id, +id),
		with: {
			subUnit: {
				with: {
					unit: true
				}
			},
			unit: true,
			group: true,
			parameter: true,
			category: true,
			inventory: {
				with: {
					costUnit: true,
					exspend: {
						with: { supplier: true }
					}
				},
				orderBy: desc(inventory.datetime_buy)
			}
		}
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'product'), eq(uploads.related_id, +id))
	});
	// await get_product_qty({ product_id: +id });
	const get_inventories = await db.query.inventory.findMany({
		where: eq(inventory.product_id, +id),
		with: {
			costUnit: true,
			product: {
				with: {
					subUnit: {
						with: {
							unit: true
						}
					},
					unit: true
				}
			}
		},
		...pagination(url)
	});
	const items = await db.$count(inventory, eq(inventory.product_id, +id));
	return {
		get_product: {
			...get_product,
			uploads: get_upload
		},
		get_currency,
		get_inventories,
		items
	};
}) satisfies PageServerLoad;
export const actions: Actions = {
	use_inventory: async ({ request, url }) => {
		const body = await request.formData();
		const { inventory_id, product_id, is_close_inventory } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		const validErr = {
			inventory_id: false,
			product_id: false
		};
		if (!inventory_id) validErr.inventory_id = true;
		if (!product_id) validErr.product_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (is_close_inventory === 'on') {
			await db
				.update(inventory)
				.set({
					is_close_inventory: true
				})
				.where(eq(inventory.product_id, +product_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
			await db
				.update(inventory)
				.set({
					is_close_inventory: false
				})
				.where(eq(inventory.id, +inventory_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			await db
				.update(inventory)
				.set({
					is_close_inventory: true
				})
				.where(eq(inventory.id, +inventory_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	delete_inventory: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (isNaN(+id)) return fail(400, { id: true });
		const get_inventory = await db.query.inventory.findFirst({
			where: eq(inventory.id, +id)
		});
		const get_product = await db.query.product.findFirst({
			with: {
				inventory: true
			},
			where: eq(product.id, Number(get_inventory?.product_id))
		});
		if (Number(get_product?.inventory.length) > 1) {
			await db
				.delete(inventory)
				.where(eq(inventory.id, +id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	}
};
