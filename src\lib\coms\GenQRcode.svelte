<script lang="ts">
	import QRCode from 'qrcode';
	interface Props {
		text: string;
	}
	let { data }: { data: Props } = $props();
	let { text } = $derived(data);
	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
	$effect(() => {
		const img = document.getElementById(id) as HTMLImageElement;
		if (text) {
			QRCode.toDataURL(
				text,
				{
					margin: 0, // removes border
					width: 250
				},
				(err, url) => {
					if (err) {
						console.error(err);
						return;
					}
					img.src = url;
				}
			);
		}
	});
</script>

<!-- svelte-ignore a11y_missing_attribute -->
<img class="img-thumbnail p-1" {id} />
