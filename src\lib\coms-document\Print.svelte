<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';

	let {
		id,
		class: className,
		icon = false,
		margin="0px"
	}: { id: string; class: string; icon?: boolean, margin?: string } = $props();

	function printDiv(id: string): Promise<void> {
		return new Promise((resolve, reject) => {
			const element = document.getElementById(id);
			if (!element) {
				reject(new Error(`Element with ID "${id}" not found`));
				return;
			}

			const body = document.body;
			// Store current children of the body
			const originalChildren = Array.from(body.children);

			// Create a style element for print-specific CSS
			const printStyle = document.createElement('style');
			printStyle.textContent = `
            @media print {
                @page {
                    margin: ${margin} !important;
                }
            }
        `;

			// Create a container for printing to isolate our content
			const printContainer = document.createElement('div');
			printContainer.id = 'print-container';

			// Clone the element to be printed (deep clone)
			const elementClone = element.cloneNode(true) as HTMLElement;
			printContainer.appendChild(elementClone);

			// Hide all body children
			for (const child of originalChildren) {
				(child as HTMLElement).style.display = 'none';
			}

			// Add our print style and container to the body
			body.appendChild(printStyle);
			body.appendChild(printContainer);

			// Wait for images/styles to load if needed
			setTimeout(() => {
				try {
					window.print();
				} catch (err) {
					reject(err);
				} finally {
					// Always clean up, even if print fails
					printStyle.remove();
					printContainer.remove();

					// Restore original children visibility
					for (const child of originalChildren) {
						(child as HTMLElement).style.display = '';
					}

					resolve();
				}
			}, 100); // Short delay to ensure DOM updates
		});
	}
</script>

<button
	class={className}
	onclick={async (e) => {
		e.preventDefault();
		await printDiv(id);
	}}
>
	{#if icon}
		<i class="fas fa-print"></i>
	{:else}
		<i class="fas fa-print"></i>
		{locale.T('print')}
	{/if}
</button>
