<script lang="ts">
	import type { PageServerData } from './$types';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import { store } from '$lib/store/store.svelte';
	import Name from '$lib/coms/Name.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import GenderAge from '$lib/coms/GenderAge.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_patients, items, get_laboratoies } = $derived(data);
	let total_male = $derived(
		get_laboratoies.filter((e) => e.patient?.gender.toLowerCase() === 'male').length
	);
	let n = $state(0);
	let see_id: number | null = $state(null);
</script>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('laboratory')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/laboratory" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-flask nav-icon"></i>
					{locale.T('laboratory')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('start')}</span>
					<input type="date" name="start" class="form-control" />
				</div>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('end')}</span>
					<input type="date" name="end" class="form-control" />
				</div>
			</div>
			<div class="col-sm-3">
				<SelectParam
					q_name="q"
					placeholder={locale.T('patient')}
					name="patient_id"
					items={get_patients.map((e) => ({
						id: e.id,
						name: e.name_khmer?.concat(` ${e.name_latin}`)
					}))}
				/>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('status')}</span>
					<select class="form-control" name="status" id="status">
						<option value="">All</option>
						<option value="true">Done</option>
						<option value="false">Not Done</option>
					</select>
				</div>
			</div>
		</HeaderQuery>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-light text-nowrap table-hover">
			<thead class="sticky-top table-active">
				<tr class="text-center">
					<th style="width: 4%;">{locale.T('n')}</th>
					<th style="width: 7%;">{locale.T('date')}</th>
					<th style="width: 5%;">{locale.T('id')} </th>
					<th style="width: 10%;"> {locale.T('patient_name')}</th>
					<th style="width: 7%;"> {locale.T('requester')}</th>
					<th style="width: 7%;"> {locale.T('checker')}</th>
					<th style="width: 5%;"> {locale.T('visit_type')}</th>
					<th style="width: 30%;"> {locale.T('request_check')}</th>
					<th style="width: 10%;"> {locale.T('result')}</th>
					<th style="width: 10%;"></th>
				</tr>
			</thead>
			<tbody>
				{#each get_laboratoies as item, index}
					{@const laboratoryRequests = item?.visit?.laboratoryRequest || []}
					{@const visit = item?.visit}

					<tr class="text-center">
						<td class="text-left">{index + n}</td>
						<td>
							<DDMMYYYYFormat style="date" date={item.request_datetime} />
							<br />
							<DDMMYYYYFormat style="time" date={item.request_datetime} />
						</td>
						<td class="text-center">
							PT{item.patient_id ?? ''}
							<br />
							LB{item?.id ?? ''}
						</td>
						<td>
							<button class="btn btn-link">
								{item?.patient?.name_khmer}
								<br />
								{item?.patient?.name_latin}
								<GenderAge
									dob={item?.patient?.dob}
									date={new Date()}
									gender={item?.patient?.gender}
								/>
							</button>
						</td>
						<td>
							<Name staff={item?.visit?.staff} />
						</td>
						<td>
							<Name name_khmer={item.inputBy?.name_khmer} name_latin={visit?.staff?.name_latin} />
						</td>
						<td>{item?.visit?.checkin_type}</td>
						<td class="text-wrap text-start">
							{#if laboratoryRequests.length > 4}
								{#if see_id && see_id === item.id}
									{#each laboratoryRequests as iitem}
										<span class="m-1 badge text-bg-primary">{iitem.product?.products}</span>
									{/each}
								{:else}
									{#each laboratoryRequests.slice(0, 4) as iitem}
										<span class="m-1 badge text-bg-primary">{iitem.product?.products}</span>
									{/each}
								{/if}
								<button
									onclick={() => {
										see_id = see_id === item.id ? null : item.id;
									}}
									class="btn btn-link text-dark btn-sm"
								>
									{#if see_id && see_id === item.id}
										{locale.T('see_less')}...
									{:else}
										{locale.T('see_more')}...
									{/if}
								</button>
							{:else}
								{#each laboratoryRequests as iitem}
									<span class="m-1 badge text-bg-primary">{iitem.product?.products}</span>
								{/each}
							{/if}
						</td>
						<td class="text-center">
							{#if item?.status === false}
								<button disabled type="button" class="btn btn-info btn-sm">View </button>
							{:else}
								<a
									target="_blank"
									href="/report/{item?.visit_id}/laboratory"
									type="button"
									class="btn btn-info btn-sm"
								>
									{locale.T('view')}
								</a>
							{/if}
							{#if item?.status === true}
								<a href="/laboratory/result?visit_id={item.visit_id}" class="btn btn-primary btn-sm"
									>{locale.T('more_fill')}
								</a>
							{:else}
								<a href="/laboratory/result?visit_id={item.visit_id}" class="btn btn-warning btn-sm"
									>{locale.T('fill')}
								</a>
							{/if}
						</td>
						<td>
							<div>
								<a
									aria-label="Edit"
									href="/opd/{item.visit_id}/laboratory"
									class="btn btn-primary btn-sm"
									><i class="fa-solid fa-file-pen"></i>
								</a>
								<button style="background-color: deeppink;" type="button" class="btn btn-sm"
									>{locale.T('invoice')}
								</button>
							</div>
						</td>
					</tr>
				{/each}
				<tr class="table-success">
					<td colspan="10" class="text-center">
						{locale.T('total')}: {get_laboratoies.length}
						{locale.T('people')},
						{locale.T('male')}: {total_male}
						{locale.T('female')}: {get_laboratoies.length - total_male}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
