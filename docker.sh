#!/bin/bash

set -e  # Exit on any error


# === Update System ===
echo "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# === Install Docker ===
echo "Installing Docker..."

# Install prerequisites
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Set up Docker repo
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] \
  https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" \
  | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Enable Docker on boot
sudo systemctl enable docker
sudo systemctl start docker

# Add new user to Docker group
sudo usermod -aG docker "$USER"
newgrp docker
echo "Docker installation complete."

# === Done ===
echo "Setup complete. To switch to the new user, run:"
echo "Then, run: docker --version to verify Docker is available."
