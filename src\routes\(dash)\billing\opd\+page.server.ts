import { db } from '$lib/server/db';
import { and, desc, eq, isNotNull, isNull } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { billing } from '$lib/server/schemas';
import { betweenHelper, pagination } from '$lib/server/utils';

export const load: PageServerLoad = async ({ parent, url }) => {
	await parent();
	const get_billings = await db.query.billing.findMany({
		where: and(
			betweenHelper(url, billing.created_at),
			eq(billing.status, 'paying'),
			isNotNull(billing.visit_id),
			isNull(billing.progress_note_id),
			eq(billing.billing_type, 'OPD')
		),
		with: {
			serviceType: true,
			paymentService: true,
			visit: {
				with: {
					patient: true,
					staff: true,
					department: true,
					presrciption: true
				}
			},
			charge: {
				with: {
					productOrder: {
						with: {
							product: true
						}
					}
				}
			}
		},
		orderBy: desc(billing.created_at),
		...pagination(url)
	});
	const count = await db.$count(
		billing,
		and(
			betweenHelper(url, billing.created_at),
			eq(billing.status, 'paying'),
			isNotNull(billing.visit_id),
			isNull(billing.progress_note_id),
			eq(billing.billing_type, 'OPD')
		)
	);

	return {
		get_billings,
		items: count
	};
};
