<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData, ActionData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import { page } from '$app/state';
	import { DDMMYYYY_Format, YYYYMMDD_Format } from '$lib/helper';
	import Form from '$lib/coms-form/Form.svelte';
	import SubmiteSearch from '$lib/coms/SubmiteSearch.svelte';
	import { store } from '$lib/store/store.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import GetBack from '$lib/coms/GetBack.svelte';
	import SetBack from '$lib/coms/SetBack.svelte';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}
	let { data, form }: Props = $props();
	let { get_suppliers, get_currency, get_products, get_exspend, get_inventories, items } =
		$derived(data);
	let supplier_id = $derived<number | null | string>(get_exspend?.supplier_id ?? '');
	let exspend_id: string = $derived(page.url.searchParams.get('exspend_id') || '');
	let inventory_id: string = $derived(page.url.searchParams.get('inventory_id') || '');
	let invoice_no = $state(data.get_exspend?.invoice_no);
	let inventory = $derived(get_inventories?.find((e) => e.id === Number(inventory_id)));
	let subUnits = $derived(inventory?.product?.subUnit);
	let datetime_expire = $derived(YYYYMMDD_Format.datetime(inventory?.datetime_expire));
	let cost_unit_id = $derived(inventory?.cost_unit_id ?? '');
	let qty_per_unit = $derived(
		subUnits?.find((e) => e.unit_id === +cost_unit_id)?.qty_per_unit || 1
	);
	let datetime_invoice = $state(
		data.get_exspend?.datetime_invoice
			? YYYYMMDD_Format.datetime(data.get_exspend?.datetime_invoice)
			: ''
	);
	let loading = $state(false);
	let n = $state(1);
	let qty_bought = $state(0);
	let cost = $state(0);
	let inventory_id_ = $state('');
	$effect(() => {
		if (inventory) {
			qty_bought = inventory?.qty_bought ?? 0;
			cost = inventory?.cost ?? 0;
		}
		if (!inventory) {
			qty_bought = 0;
			cost = 0;
		}
	});
	let percent_inerhight = $derived(Number(store.inerHight.replace('px', '')) / 1.8);
</script>

<DeleteModal action="?/remove_product" id={Number(inventory_id_)} />
<div class="row">
	<div class="col-sm-6">
		<GetBack href="/product/purchase" />
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/purchase" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('purchase')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class={!inventory ? 'card bg-success-subtle' : 'card'}>
	<div class="card-body">
		<Form action="?/create_exspend" method="post" reset={false} bind:loading>
			<input type="hidden" name="exspend_id" value={exspend_id ?? ''} />
			<div class="row">
				<div class="col-sm-3">
					<label for="datetime_invoice">{locale.T('date')}</label>
					<input
						class="form-control"
						bind:value={datetime_invoice}
						step="any"
						type="datetime-local"
						name="datetime_invoice"
						id="datetime_invoice"
					/>
					{#if form?.datetime_invoice}
						<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
					{/if}
				</div>
				<div class="col-sm-3">
					<label for="invoice_no">{locale.T('invoice_no')}</label>
					<input
						class="form-control"
						type="text"
						name="invoice_no"
						bind:value={invoice_no}
						id="invoice_no"
					/>
					{#if form?.invoice_no}
						<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
					{/if}
				</div>
				<div class="col-sm-4">
					<label for="supplier_id">{locale.T('supplier')}</label>

					<div class="input-group">
						<SelectParam
							placeholder={locale.T('select')}
							name="supplier_id"
							bind:value={supplier_id}
							items={get_suppliers.map((e) => ({
								id: e.id,
								name: e.name
									?.concat(', ')
									.concat(e?.company_name ?? '')
									.concat(', ')
									.concat(e?.address ?? '')
							}))}
						/>
					</div>
					{#if form?.supplier_id}
						<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
					{/if}
				</div>
				<div class="col-sm-2">
					<label for="save"></label>
					<div class="">
						<button class=" form-control w-100 text-success" type="submit">
							{locale.T('save')}
						</button>
					</div>
				</div>
			</div>
		</Form>
		<br />
		<div class="row">
			<div class="col-sm-10">
				{#if form?.productAdded}
					<label class="text-danger" for="supplier_id"
						>ទំនិញបានបញ្ជូលរូចហើយ <i class="fa-solid fa-xmark"></i>
					</label>
				{/if}
				<SubmiteSearch
					q_name="q"
					placeholder={'ស្វែងរកតាមយៈបាកូដឫឈ្មោះទំនិញ (ស្វែងរកមិនឃើញសូមបន្ថែមផលិតផលថ្មី) '}
					items={get_products.map((e) => ({ id: e.id, name: e.products, price: e.price }))}
					action="?/add_product"
				>
					<input type="hidden" name="exspend_id" value={exspend_id} />
				</SubmiteSearch>
			</div>
			<div class="col-sm-2">
				<SetBack
					href="/product/create{exspend_id ? '?exspend_id='.concat(exspend_id) : ''}"
					class="form-control text-decoration-none text-center text-warning"
				>
					<i class="fa-solid fa-square-plus"></i>
					{locale.T('new_product')}</SetBack
				>
			</div>
		</div>
	</div>
</div>
<div class={inventory ? 'card bg-success-subtle mt-2' : 'card mt-2'}>
	<Form
		class="card-body"
		data_sveltekit_keepfocus={false}
		data_sveltekit_noscroll={false}
		action="?/update_exspend_and_inventory"
		method="post"
		reset={false}
		bind:loading
	>
		<input type="hidden" value={inventory?.exspend_id ?? ''} name="exspend_id" />
		<input type="hidden" value={inventory?.id ?? ''} name="inventory_id" />
		<input type="hidden" value={inventory?.product_id ?? ''} name="product_id" />
		<input type="hidden" value={inventory?.product?.group_id} name="group_id" />
		<div class="row mb-3">
			<div class="col-md-3">
				<label for="product">{locale.T('products')}</label>
				<span class="form-control">{inventory?.product.products ?? '...'}</span>
			</div>
			<div class="col-md-3">
				<div>
					<label for="datetime_expire">{locale.T('expires_date')}</label>
					<input
						class="form-control"
						value={datetime_expire}
						type="datetime-local"
						name="datetime_expire"
						id="datetime_expire"
					/>
				</div>
			</div>
			<div class="col-md-3">
				<label for={inventory?.id.toString().concat('is_count_stock')}
					>&#10004; {locale.T('check_for_count')}</label
				>
				<div class="form-control">
					<div class="form-check m-0">
						<input
							checked={inventory?.is_count_stock}
							name="is_count_stock"
							class="form-check-input"
							type="checkbox"
							id={inventory?.id.toString().concat('is_count_stock')}
						/>
						<label for={inventory?.id.toString().concat('is_count_stock')}
							>{locale.T('is_count_stock')}
						</label>
					</div>
				</div>
			</div>
			<div class="col-md-3 text-success">
				<label for="cost">{locale.T('cost')}</label>
				<CurrencyInput name="cost" bind:amount={cost} symbol={get_currency?.currency} />
			</div>
		</div>
		<div class="row">
			<div class="col-md-3">
				<label for="cost">{locale.T('unit')}</label>
				<select
					bind:value={cost_unit_id}
					class="form-control"
					name="cost_unit_id"
					id="cost_unit_id"
				>
					<option value={inventory?.product?.unit_id}
						>{inventory?.product?.unit?.unit ?? ''}
					</option>
					{#each subUnits || [] as iitem (iitem.id)}
						<option value={iitem?.unit_id ?? ''}>{iitem.unit.unit ?? ''}</option>
					{/each}
				</select>
			</div>
			<div class="col-md-3">
				<label for="product"
					>{locale.T('qty')}
					{#each subUnits || [] as iitem (iitem.id)}
						{#if iitem?.unit_id === cost_unit_id}
							{iitem.qty_per_unit} {inventory?.product.unit?.unit} / {iitem.unit.unit}
						{/if}
					{/each}
				</label>
				<input
					bind:value={qty_bought}
					class="form-control"
					type="number"
					step="qty_bought"
					name="qty_bought"
					id="qty_bought"
				/>
			</div>
			<input type="hidden" name="qty_per_unit" value={qty_per_unit} />
			<input type="hidden" value={Number(qty_bought) * Number(cost)} name="total_expense" />
			<div class="col-md-3">
				<label for="total_expense">{locale.T('total')}</label>
				<Currency
					class="form-control"
					amount={Number(qty_bought) * Number(cost)}
					symbol={get_currency?.currency}
				/>
			</div>
			<div class="col-md-3">
				<label for=""></label>
				<button disabled={!inventory?.id} type="submit" class="form-control text-success"
					>{locale.T('save')}
				</button>
			</div>
		</div>
	</Form>
</div>
<div class="card bg-light mt-2">
	<div style="height: {percent_inerhight}px;" class="card-body table-responsive p-0 m-0">
		<table class="table table-bordered table-hover text-nowrap table-sm">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th style="width: 3%;" class="text-center">{locale.T('n')}</th>
					<th style="width: 20%;">{locale.T('products')}</th>
					<th style="width: 10%;">{locale.T('price')}</th>
					<th style="width: 10%;">{locale.T('purchase_qty')}</th>
					<th style="width: 10%;">{locale.T('total')}</th>
					<th style="width: 10%;">{locale.T('expires_date')}</th>
					<th style="width: 10%;">{locale.T('action')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_inventories || [] as item, index}
					<tr class={item.id === Number(inventory_id) ? 'table-success' : ''}>
						<td class="text-center">{n + index}</td>
						<td class="text-start">
							<div class="row g-2">
								<div class="col-auto">
									{item?.product?.products}
								</div>
							</div>
						</td>
						<td>
							<Currency amount={item?.cost} symbol={get_currency?.currency} />
						</td>
						<td>
							{item.qty_bought}
							{item?.costUnit?.unit}
						</td>
						<td>
							<Currency amount={item?.total_expense} symbol={get_currency?.currency} />
						</td>
						<td>
							{#if item?.datetime_expire}
								{DDMMYYYY_Format(item?.datetime_expire, 'date')}
							{:else}
								<span class="text-danger">{locale.T('none')}</span>
							{/if}
						</td>
						<td class="text-center">
							<div class=" m-0 p-0">
								<a
									href={inventory_id.includes(item.id.toString())
										? `?exspend_id=${exspend_id}`
										: `?exspend_id=${exspend_id}&inventory_id=${item.id}`}
									type="button"
									class="btn btn-primary btn-sm"
								>
									<i class="fa-solid fa-pen-to-square"></i>
									{locale.T('edit')}
								</a>
								<button
									onclick={() => (inventory_id_ = String(item.id))}
									aria-label="deletemodal"
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i> {locale.T('delete_')}
								</button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
