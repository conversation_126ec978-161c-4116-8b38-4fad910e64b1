import { db } from '$lib/server/db';
import { and, desc, eq, gt, like, or } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { billing, patient, payment, paymentType, uploads } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import { betweenHelper, YYYYMMDD_Format, pagination } from '$lib/server/utils';
import logError from '$lib/server/utils/logError';
import { billingProcess } from '$lib/server/models';
import { ne } from 'drizzle-orm';
import { fileHandle } from '$lib/server/upload';

export const load: PageServerLoad = async ({ parent, url }) => {
	await parent();
	const q = url.searchParams.get('q') ?? '';
	const patient_id = Number(url.searchParams.get('patient_id'));
	const service_type_id = Number(url.searchParams.get('service_type_id'));
	const billing_type = url.searchParams.get('billing_type') as 'IPD' | 'OPD' | 'POS';
	const status = url.searchParams.get('status') as
		| 'paid'
		| 'partial'
		| 'debt'
		| 'checking'
		| 'paying';
	const get_currency = await db.query.currency.findFirst({});
	const get_service_types = await db.query.serviceType.findMany({});
	const get_billings = await db.query.billing.findMany({
		where: and(
			ne(billing.status, 'checking'),
			ne(billing.status, 'paying'),
			status ? eq(billing.status, status) : undefined,
			patient_id ? eq(billing.patient_id, patient_id) : undefined,
			betweenHelper(url, billing.created_at),
			billing_type ? eq(billing.billing_type, billing_type) : undefined,
			service_type_id ? eq(billing.service_type_id, service_type_id) : undefined,
			gt(billing.amount, 0)
		),
		with: {
			serviceType: true,
			progressNote: true,
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			visit: {
				with: {
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					staff: true,
					department: true
				}
			},
			charge: {
				with: {
					productOrder: {
						with: {
							product: true
						}
					}
				}
			},
			payment: {
				with: {
					paymentType: true,
					staff: true
				}
			},
			staff: true
		},
		orderBy: desc(billing.created_at),
		...pagination(url)
	});

	const items = await db.$count(
		billing,
		and(
			ne(billing.status, 'checking'),
			ne(billing.status, 'paying'),
			status ? eq(billing.status, status) : undefined,
			patient_id ? eq(billing.patient_id, patient_id) : undefined,
			betweenHelper(url, billing.created_at),
			billing_type ? eq(billing.billing_type, billing_type) : undefined,
			service_type_id ? eq(billing.service_type_id, service_type_id) : undefined,
			gt(billing.amount, 0)
		)
	);

	const get_payment_types = await db.query.paymentType.findMany({
		orderBy: desc(paymentType.id)
	});

	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`)
		),
		limit: 200
	});

	// const remove_none_chacking = get_billings.filter((e) => {
	// 	if (e.paid && e.status !== 'checking') return e
	// })
	return {
		get_billings,
		get_payment_types,
		get_currency,
		items: items,
		get_patients,
		get_service_types
	};
};

export const actions: Actions = {
	repayment: async ({ request, locals, url }) => {
		const body = await request.formData();
		const { value, payment_type_id, billing_id, note, disc, tax, payment_id } = Object.fromEntries(
			body
		) as Record<string, string>;
		const staff_id = locals.user?.staff_id;
		const file = body.get('file') as File;
		const validErr = {
			value: false,
			payment_type_id: false,
			billing_id: false,
			staff_id: false
		};
		if (isNaN(+value)) validErr.value = true;
		if (!payment_type_id) validErr.payment_type_id = true;
		if (!billing_id) validErr.billing_id = true;
		if (!staff_id) validErr.staff_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const dateitme = YYYYMMDD_Format.datetime(new Date());
		let paymentId: number | null = +payment_id;
		if (payment_id) {
			await db
				.update(payment)
				.set({
					note: note,
					payment_type_id: +payment_type_id,
					value: +value,
					datetime: dateitme,
					staff_id: Number(staff_id)
				})
				.where(eq(payment.id, +payment_id));
		}
		if (!payment_id) {
			const create_payment = await db
				.insert(payment)
				.values({
					billing_id: +billing_id,
					note: note,
					payment_type_id: +payment_type_id,
					value: +value,
					datetime: dateitme,
					staff_id: Number(staff_id)
				})
				.$returningId();
			paymentId = create_payment[0]?.id;
		}
		if (file.size) {
			await fileHandle.insert(file, +paymentId, 'payment');
		}
		await billingProcess({
			billing_id: +billing_id,
			disc: disc,
			tax: +tax,
			note: note,
			body: body,
			url: url
		});
	},
	delete_payment: async ({ request, url }) => {
		const body = await request.formData();
		const { id: payment_id } = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			payment_id: false
		};

		if (!payment_id) validErr.payment_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const get_upload = await db.query.uploads.findFirst({
			where: and(eq(uploads.related_type, 'payment'), eq(uploads.related_id, +payment_id))
		});
		const get_payment = await db.query.payment.findFirst({
			where: eq(payment.id, +payment_id),
			with: {
				billing: true
			}
		});
		if (get_upload?.filename) await fileHandle.drop(get_upload?.filename);
		const get_billing = await db.query.billing.findFirst({
			where: eq(billing.id, get_payment?.billing_id || 0)
		});
		if (get_billing) {
			await db
				.delete(payment)
				.where(eq(payment.id, +payment_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
			await billingProcess({
				billing_id: get_billing!.id,
				disc: get_billing!.discount,
				note: get_billing!.note ?? '',
				tax: get_billing!.tax,
				body: body,
				url: url
			});
		}
	}
};
