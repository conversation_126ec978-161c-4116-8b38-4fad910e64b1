<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import Athtml from '$lib/coms/Athtml.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import { type TBillingStatus } from '$lib/type';
	let loading = $state(false);
	interface Props {
		status: TBillingStatus | undefined;
		paid_title?: string;
		checking_title?: string;
		paying_title?: string;
		action?: string;
		btn_size?: 'btn-sm' | 'btn-md';
		children?: import('svelte').Snippet;
	}
	let {
		action = '/patient/opd/?/process_billing',
		status,
		paid_title,
		checking_title,
		paying_title,
		btn_size = 'btn-sm',
		children
	}: Props = $props();
	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
</script>

{#if ['paid', 'partial', 'debt'].includes(status ?? '')}
	<button class={`btn ${btn_size} btn-primary`} type="button">
		{#if paid_title}
			<Athtml html={paid_title} />
		{:else}
			<i class="fa-regular fa-circle-check"></i>
			{locale.T('already_paid')}
		{/if}
	</button>
{:else if status === 'checking'}
	<button
		type="button"
		class={`btn ${btn_size} btn-outline-warning`}
		data-bs-toggle="modal"
		data-bs-target={'#'.concat(id?.toString() ?? '')}
	>
		{#if checking_title}
			<Athtml html={checking_title} />
		{:else}
			<i class="fa-solid fa-comments-dollar"></i>
			{locale.T('send_to_payment')}
		{/if}
	</button>
{:else if status === 'paying'}
	<button
		class={`btn ${btn_size} btn-warning`}
		type="button"
		data-bs-toggle="modal"
		data-bs-target={'#'.concat(id?.toString() ?? '')}
	>
		{#if paying_title}
			<Athtml html={paying_title} />
		{:else}
			<i class="fa-solid fa-spinner fa-spin"></i>
			{locale.T('sended_to_payment')}
		{/if}
	</button>
{/if}

<div class="modal fade" tabindex="-1" role="dialog" {id} data-bs-backdrop="static">
	<Form
		{action}
		bind:loading
		fnSuccess={() => document.getElementById('close_confirm_submit')?.click()}
		method="post"
		class="modal-dialog modal-md"
	>
		<div class="modal-content rounded-3 shadow">
			<div class="modal-header py-2 justify-content-center text-bg-warning">
				<span class="fs-3">
					{#if status === 'checking'}
						{locale.T('send_to_payment')}
					{:else if status === 'paying'}
						{locale.T('send_back')}
					{/if}
				</span>
			</div>
			<div class="modal-body">
				{@render children?.()}
			</div>
			<div class="modal-footer flex-nowrap p-0">
				<button
					id="close_delete_modal"
					type="button"
					class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
					data-bs-dismiss="modal">{locale.T('no')}</button
				>
				<button
					data-bs-dismiss="modal"
					disabled={loading}
					type="submit"
					class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
				>
					<strong>{locale.T('yes')}</strong>
				</button>
			</div>
		</div>
	</Form>
</div>
