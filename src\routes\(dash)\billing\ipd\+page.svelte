<script lang="ts">
	import type { PageServerData } from './$types';
	import { store } from '$lib/store/store.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { dobToAge } from '$lib/helper';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_billings, items } = $derived(data);
	let n = $state(1);
</script>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('billing_ipd')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-money-bills"></i>
					{locale.T('billing')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class=" fas fa-procedures"></i>
					{locale.T('ipd')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery>
			<div class="col-sm-3">
				<div class="input-group">
					<span class="input-group-text">{locale.T('start')}</span>
					<input type="date" name="start" class="form-control" />
				</div>
			</div>
			<div class="col-sm-3">
				<div class="input-group">
					<span class="input-group-text">{locale.T('end')}</span>
					<input type="date" name="end" class="form-control" />
				</div>
			</div>
		</HeaderQuery>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-hover text-nowrap table-light">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th style="width: 3%;">{locale.T('n')}</th>
					<th style="width: 7%;">{locale.T('date')}</th>
					<th style="width: 5%;">{locale.T('id')}</th>
					<th style="width: 10%;"> {locale.T('patient_name')}</th>
					<th style="width: 5%;"> {locale.T('gender')}</th>
					<th style="width: 5%;"> {locale.T('age')}</th>
					<th style="width: 10%;"> {locale.T('doctor')}</th>
					<th style="width: 10%;">{locale.T('prescription')}</th>

					<th style="width: 10%;">{locale.T('payment')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_billings as item, index}
					{@const paymentService = item?.paymentService}
					{@const serviceType = item?.serviceType}
					<tr class="text-center">
						<td>{n + index}</td>
						<td class="text-center">
							{#if item.billing_type === 'CHECKING'}
								<DDMMYYYYFormat style="date" date={item.visit?.date_checkup} />
								<br />
								<DDMMYYYYFormat style="time" date={item.visit?.date_checkup} />
							{:else}
								<DDMMYYYYFormat style="date" date={item.progressNote?.date_checkup} />
								<br />
								<DDMMYYYYFormat style="time" date={item.progressNote?.date_checkup} />
							{/if}
						</td>

						<td class="text-center">
							PT{item?.patient_id}
							<br />
							VS{item.billing_type === 'OPD'
								? item.visit_id
								: item?.visit?.progress_note_id || item.progress_note_id}
						</td>
						<td>
							{#if item.billing_type === 'IPD'}
								<a class="btn btn-link" href="/billing/ipd/{item.progress_note_id}"
									>{item?.patient?.name_khmer} <br />
									{item?.patient?.name_latin}</a
								>
							{:else}
								<a class="btn btn-link" href="/billing/opd/{item.visit_id}"
									>{item?.patient?.name_khmer} <br />
									{item?.patient?.name_latin}</a
								>
							{/if}
						</td>

						<td class="text-center">{item?.patient?.gender}</td>
						<td class="text-center">{dobToAge(item?.patient?.dob, new Date())}</td>
						<td>
							{#if item.billing_type === 'CHECKING'}
								{item?.visit?.staff?.name_latin}
							{:else}
								{item?.progressNote?.staff?.name_latin}
							{/if}
						</td>
						<td>
							{#if item.progressNote?.presrciption.length}
								<a class="btn btn-link" href="/prescription/{item.progress_note_id}/ipd">
									{locale.T('view')} {locale.T('prescription')}</a
								>
							{:else}
								{locale.T('none')}
							{/if}
						</td>

						<td>
							{#if paymentService?.id}
								<a
									class="btn btn-link"
									href="/patient/payment-service?id={paymentService.id}&&visit_id={item.visit_id}"
									>{serviceType?.by}</a
								>
							{:else}
								<a class="btn btn-link" href="/patient/payment-service?billing_id={item.id}"
									>{locale.T('none_data')}</a
								>
							{/if}
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
