<script lang="ts">
	import { dobToAge, khmerDate } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	import type { LayoutServerData } from '../../routes/(dash)/opd/[id]/$types';
	import GenQRcode from '$lib/coms/GenQRcode.svelte';
	import Print from '$lib/coms-document/Print.svelte';
	interface Props {
		patient_info?: LayoutServerData['patient_info'];
	}
	let { patient_info }: Props = $props();
	let age_p_visit = $derived(dobToAge(patient_info?.dob, patient_info?.date_checkup));
</script>

<div class="card bg-primary-subtle border-2 border-primary">
	<div class="card-header bg-seconday-subtle">
		<span class="fs-5"
			># {locale.T('id')}
			/ PT{patient_info?.id}
		</span>

		<Print id="print-label" icon class="btn btn-success btn-sm float-end my-0" />
	</div>
	<div class="card-body table-responsive p-0 row g-1 justify-content-between">
		<div class="col-auto">
			<table class="table table-borderless table-primary">
				<thead>
					<tr>
						<td>{locale.T('date')}</td>
						<td>
							{khmerDate(patient_info?.date_checkup, 'date')}
						</td>
						<td>{locale.T('hour')}</td>
						<td>
							{khmerDate(patient_info?.date_checkup, 'time')}
						</td>
					</tr>

					<tr>
						<td>
							{locale.T('patient_name')}
						</td>
						<td>
							{patient_info?.name_khmer},{patient_info?.name_latin}
						</td>
						<td
							>{locale.T('gender')} :

							{#if patient_info?.gender?.toLowerCase() === 'male'}
								{locale.T('male')}
							{:else if patient_info?.gender?.toLowerCase() === 'female'}
								{locale.T('female')}
							{:else}
								{locale.T('none')}
							{/if}
						</td>
						<td>
							{locale.T('age')} :
							{age_p_visit}
						</td>
					</tr>
					<tr>
						<td>{locale.T('address')}</td>
						<td>
							{patient_info?.village?.type}
							{patient_info?.village?.name_khmer}
							{patient_info?.commune?.type}
							{patient_info?.commune?.name_khmer}
							{patient_info?.district?.type}
							{patient_info?.district?.name_khmer}
							{patient_info?.provice?.type}
							{patient_info?.provice?.name_khmer}
						</td>
						<td>{locale.T('contact')}</td>
						<td>
							{patient_info?.telephone}
						</td>
					</tr>
				</thead>
			</table>
		</div>
		<div id="print-label" class="col-auto p-2">
			<div class="row pt-1 g-1">
				<div class="col-auto">
					<div style="width: 130px;">
						<GenQRcode data={{ text: patient_info?.id.toString() ?? '' }} />
					</div>
				</div>
				<div class="col-auto">
					<table class="text-start table-sm table-light table-borderless">
						<tbody>
							<tr>
								<td>{locale.T('patient_name')}</td>
								<td>
									{patient_info?.name_khmer}
								</td></tr
							>
							<tr>
								<td>{locale.T('gender')}</td>
								<td>
									{#if patient_info?.gender?.toLowerCase() === 'male'}
										{locale.T('male')}
									{:else if patient_info?.gender?.toLowerCase() === 'female'}
										{locale.T('female')}
									{:else}
										{locale.T('none')}
									{/if}
								</td></tr
							>
							<tr>
								<td>{locale.T('id')}</td>
								<td>
									PT{patient_info?.id}
								</td></tr
							>
							<tr>
								<td>
									{locale.T('contact')}
								</td>
								<td>
									{patient_info?.telephone}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<style scoped>
	@media print {
		@page {
			size: A4;
			margin: 0mm;
		}
	}
</style>
