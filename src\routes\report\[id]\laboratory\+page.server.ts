import { db } from '$lib/server/db';
import { and, asc, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { parameter, visit, uploads } from '$lib/server/schemas';
export const load: PageServerLoad = async ({ params }) => {
	const visit_id = params.id ?? '';
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +visit_id || 0),
		with: {
			staff: {
				with: {
					title: true
				}
			},
			laboratory: true,
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			laboratoryRequest: {
				with: {
					product: {
						with: {
							parameter: {
								with: {
									paraUnit: true,
									laboratoryResult: true
								},
								orderBy: asc(parameter.id)
							},
							laboratoryGroup: true
						}
					},
					laboratoryResult: {
						with: {
							parameter: {
								with: {
									paraUnit: true
								}
							}
						}
					}
				}
			}
		}
	});
	const removeDuplicateName = get_visit?.laboratoryRequest.filter(
		(value, index, selt) =>
			index ===
			selt.findIndex((t) => t.product?.laboratory_group_id === value.product?.laboratory_group_id)
	);
	const sort_laboraytor = get_visit?.laboratoryRequest.sort((a) => {
		if (a.product?.products.includes('CBC')) return -1;
		else return 1;
	});
	const sort_duplicateName = removeDuplicateName?.sort((a) => {
		if (a.product?.products.includes('CBC')) return -1;
		else return 1;
	});
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_imagers = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'laboratory'),
			eq(uploads.related_id, get_visit?.laboratory?.id || 0)
		)
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	const get_staff_sign = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_type, 'staffSign'),
			eq(uploads.related_id, get_visit?.staff?.id || 0)
		)
	});
	return {
		get_visit: {
			...get_visit,
			staff: {
				...get_visit?.staff,
				sign: get_staff_sign
			}
		},
		sort_laboraytor,
		get_clinic_info,
		get_imagers,
		sort_duplicateName,
		get_upload
	};
};
