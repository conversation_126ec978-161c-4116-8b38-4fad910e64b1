import { eq } from 'drizzle-orm';
import { db } from '../db';
import { charge, product, productOrder } from '../schemas';
import { YYYYMMDD_Format } from '../utils';
import { updateCharge } from './updateCharge';
import logError from '../utils/logError';
import { fail } from '@sveltejs/kit';
type TCProductOrder = {
	charge_id: number | null;
	product_id: number | null;
	price: number | null;
	qty: number | null;
	unit_id?: number | null;
	url: URL;
	body: FormData;
};
export async function createProductOrder({
	charge_id,
	price,
	product_id,
	qty,
	url,
	body,
	unit_id
}: TCProductOrder) {
	if (!charge_id || !product_id || !qty || price === null) return;
	const get_product = await db.query.product.findFirst({
		where: eq(product.id, product_id),
		with: {
			subUnit: {
				with: {
					unit: true
				}
			}
		}
	});
	if (!get_product?.unit_id) return fail(400, { errUnit: true });
	const get_charge = await db.query.charge.findFirst({
		where: eq(charge.id, charge_id),
		with: { productOrder: true }
	});
	const get_sub_unit = get_product?.subUnit.find((e) => e.unit_id === unit_id);
	const get_product_order = get_charge?.productOrder.find((e) => e.product_id === product_id);
	let product_order_id = Number(get_product_order?.id);
	if (get_product_order) {
		if (get_product_order.discount !== null || get_product_order.discount !== 0) {
			const qty_ = get_product_order.qty + qty;
			await updateProductOrder({
				product_order_id: product_order_id,
				qty: qty_,
				price: price,
				disc: get_product_order?.discount ?? '',
				url,
				body
			});
		}
	} else {
		const create_product_order: { id: number }[] = await db
			.insert(productOrder)
			.values({
				price: +price,
				total: +price * +qty,
				product_id: product_id,
				created_at: YYYYMMDD_Format.datetime(new Date()),
				charge_id: charge_id,
				qty_adjustment: get_sub_unit ? +qty * get_sub_unit.qty_per_unit : qty,
				qty: qty,
				unit_id: unit_id ? unit_id : get_product?.unit_id
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});
		product_order_id = create_product_order[0].id;
	}
	await updateCharge(charge_id);
}

type TUProductOrder = {
	product_order_id: number;
	qty: number;
	price: number;
	disc: string;
	unit_id?: number | null;
	url: URL;
	body: FormData;
};
export async function updateProductOrder({
	product_order_id,
	disc,
	price,
	qty,
	unit_id,
	url,
	body
}: TUProductOrder) {
	const get_product_order = await db.query.productOrder.findFirst({
		where: eq(productOrder.id, product_order_id),
		with: {
			product: {
				with: {
					subUnit: {
						with: {
							unit: true
						}
					}
				}
			},
			charge: true
		}
	});
	if (!get_product_order?.id) return fail(400, { errProductOrder: true });
	const get_sub_unit = get_product_order?.product?.subUnit.find((e) => e.unit_id === unit_id);
	const qty_adjustment = get_sub_unit ? +qty * get_sub_unit.qty_per_unit : qty;
	const calulator_disc = disc.includes('%')
		? price - (price * Number(disc.replace('%', ''))) / 100
		: price - Number(disc);
	await db
		.update(productOrder)
		.set({
			price: +price,
			discount: disc,
			qty_adjustment: qty_adjustment,
			qty: qty,
			unit_id: unit_id ? unit_id : undefined,
			total: calulator_disc * +qty
		})
		.where(eq(productOrder.id, product_order_id))
		.catch((e) => logError({ url, body, err: e }));

	await updateCharge(Number(get_product_order?.charge_id));
}
export async function deleteProductOrder(product_order_id: number) {
	const get_product_order = await db.query.productOrder.findFirst({
		where: eq(productOrder.id, product_order_id)
	});
	if (!get_product_order) return fail(400, { errProductOrder: true });
	await db.delete(productOrder).where(eq(productOrder.id, product_order_id));
	await updateCharge(Number(get_product_order?.charge_id));
}
