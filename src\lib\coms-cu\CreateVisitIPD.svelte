<script lang="ts">
	import { dobToAge } from '$lib/helper';
	import type { PageServerData } from '../../routes/(dash)/ipd/[progress_note_id]/progress-note/$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	type Data = Pick<PageServerData, 'get_progress_note' | 'get_staffs' | 'user'>;
	interface Props {
		data: Data;
	}

	let { data }: Props = $props();
	let { get_progress_note, get_staffs, user } = $derived(data);
	let age_p_visit = $derived(
		dobToAge(get_progress_note!.patient?.dob, get_progress_note!.date_checkup)
	);
	let loading = $state(false);
</script>

<!-- @_Create Unit -->
<div class="modal fade" id="create_visit_ipd" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-lg">
		<Form
			action="?/create_visit_ipd"
			method="post"
			bind:loading
			fnSuccess={() => {
				document.getElementById('close_create_visit_ipd')?.click();
			}}
			class="modal-content"
		>
			<div class="modal-header">
				<h4 class="modal-title">New Progress Note</h4>
				<button
					id="close_create_visit_ipd"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body pb-0">
				<div class="card border p-0">
					<table class="table text-nowrap table-borderless">
						<thead class="text-bold">
							<tr>
								<td>#{locale.T('patient')}</td>
								<td> : </td>
								<td
									>{get_progress_note?.patient?.name_khmer},{get_progress_note?.patient
										?.name_latin ?? ''}</td
								>
								<td>#{locale.T('gender')}</td>
								<td> : </td>

								<td>{get_progress_note?.patient?.gender ?? ''}</td>
							</tr>
							<tr>
								<td>#{locale.T('contact')}</td>
								<td> : </td>
								<td>{get_progress_note?.patient?.telephone ?? ''}</td>
								<td>#{locale.T('age')}</td>
								<td> : </td>

								<td>
									{#if get_progress_note?.patient?.dob}
										{locale.T('age')}
										{age_p_visit}
									{/if}
								</td>
							</tr>
						</thead>
					</table>
				</div>
				<div class="pt-4 pb-2">
					<input value={get_progress_note?.patient_id} type="hidden" name="patient_id" />
					<div class=" row">
						<label for="staff" class="col-sm-2 col-form-label">{locale.T('doctor')}</label>
						<div class="col-sm-10">
							<SelectParam
								value={user?.staff_id}
								name="staff_id"
								items={get_staffs.map((e) => ({ id: e.id, name: e?.name_latin ?? '' }))}
							/>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
