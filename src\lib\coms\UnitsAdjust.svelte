<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { TUnit, TSubUnit } from '$lib/type';
	interface TTSubUnit extends TSubUnit {
		unit: TUnit;
	}
	interface Props {
		unit?: TUnit | null;
		subUnit?: TTSubUnit[] | null;
		qty_adjustment: number;
	}
	let { qty_adjustment, subUnit, unit }: Props = $props();
</script>

{qty_adjustment}
{unit?.unit ?? ''}
{#each subUnit || [] as item (item.id)}
	{@const qty = Number(qty_adjustment / item.qty_per_unit).toFixed(2)}
	<br />
	{locale.T('or')}
	{qty}
	{item.unit.unit ?? ''}
{/each}
