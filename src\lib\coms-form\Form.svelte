<script lang="ts">
	import { applyAction, enhance } from '$app/forms';
	import { store } from '$lib/store/store.svelte';
	import type { SubmitFunction } from '@sveltejs/kit';
	import { locale } from '$lib/translations/locales.svelte';
	import type { FormEventHandler } from 'svelte/elements';
	import { goto, invalidateAll } from '$app/navigation';
	interface Props {
		children: import('svelte').Snippet;
		class?: string;
		method?: 'GET' | 'POST' | 'get' | 'post';
		enctype?: 'multipart/form-data' | 'text/plain' | 'application/x-www-form-urlencoded';
		loading?: boolean;
		reset?: boolean;
		action?: string;
		showToast?: boolean;
		fnSuccess?: () => void;
		fnError?: () => void;
		onchange?: FormEventHandler<HTMLFormElement> | null | undefined;
		data_sveltekit_keepfocus?: boolean;
		data_sveltekit_noscroll?: boolean;
		id?: string;
	}
	let {
		children,
		class: className,
		method = 'GET',
		enctype = 'application/x-www-form-urlencoded',
		loading = $bindable(),
		showToast = true,
		reset = true,
		action,
		fnSuccess,
		fnError,
		onchange,
		data_sveltekit_keepfocus = false,
		data_sveltekit_noscroll = false,
		id
	}: Props = $props();
	const onSubmit: SubmitFunction = () => {
		loading = true;
		store.globalLoading = true;
		return async ({ update, result, action }) => {
			await update({ reset: reset });
			loading = false;
			store.globalLoading = false;
			if (result.type === 'failure') {
				if (showToast) {
					new bs5.Toast({
						body: `<i class="fa-solid fa-circle-xmark"></i> ${locale.T('submit_was_not_successfully')}  (${action.search.replace('?/', '')}) `,
						className: 'border-0 bg-danger text-white me-1',
						btnCloseWhite: true,
						margin: '60px'
					}).show();
					fnError?.();
				}
			}
			if (result.type === 'success' || result.type === 'redirect') {
				if (showToast) {
					new bs5.Toast({
						body: `<i class="fa-solid fa-circle-check"></i> ${locale.T('submit_successfully')}  (${action.search.replace('?/', '')})`,
						className: 'border-0 bg-success text-white me-1',
						btnCloseWhite: true,
						margin: '60px'
					}).show();
				}
				fnSuccess?.();
			}
		};
	};
	// $effect(() => {
	// 	if (loading) {
	// 		const interval = setInterval(() => {
	// 			invalidateAll();
	// 		}, 3000);

	// 		return () => {
	// 			clearInterval(interval);
	// 		};
	// 	}
	// });
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
<form
	{id}
	data-sveltekit-keepfocus={data_sveltekit_keepfocus}
	data-sveltekit-noscroll={data_sveltekit_noscroll}
	{onchange}
	{action}
	{method}
	{enctype}
	class={className}
	use:enhance={onSubmit}
>
	{@render children?.()}
</form>
