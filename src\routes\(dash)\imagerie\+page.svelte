<script lang="ts">
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import GenderAge from '$lib/coms/GenderAge.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_patients, items, get_imagires } = $derived(data);
	let total_male = $derived(
		get_imagires.filter((e) => e.visit?.patient?.gender.toLowerCase() === 'male').length
	);
	let n = $state(1);
</script>

<div class="modal fade" id="modal-visite">
	<div class="modal-dialog modal-dialog-centered modal-sm">
		<input
			id="close_visit_modal"
			class="hide"
			data-bs-dismiss="modal"
			aria-label="Close"
			type="hidden"
		/>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('imagerie')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/imagerie" class="btn btn-link p-0 text-secondary"
					><i class="nav-icon fas fa-image"></i>
					{locale.T('imagerie')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('start')}</span>
					<input type="date" name="start" class="form-control" />
				</div>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('end')}</span>
					<input type="date" name="end" class="form-control" />
				</div>
			</div>
			<div class="col-sm-3">
				<SelectParam
					q_name="q"
					placeholder={locale.T('patient')}
					name="patient_id"
					items={get_patients.map((e) => ({
						id: e.id,
						name: e.name_khmer?.concat(` ${e.name_latin}`)
					}))}
				/>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('status')}</span>
					<select class="form-control" name="status" id="status">
						<option value="">All</option>
						<option value="true">Done</option>
						<option value="false">Not Done</option>
					</select>
				</div>
			</div>
		</HeaderQuery>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-light text-nowrap table-hover">
			<thead class="sticky-top table-active">
				<tr class="text-center">
					<th style="width: 3%;">{locale.T('n')}</th>
					<th style="width: 7%;">{locale.T('date')}</th>
					<th style="width: 5%;">{locale.T('id')}</th>
					<th style="width: 10%;"> {locale.T('patient_name')}</th>
					<th style="width: 10%;"> {locale.T('doctor')}</th>
					<th style="width: 5%;"> {locale.T('visit_type')}</th>
					<th style="width: 20%;">{locale.T('request_check')}</th>
					<td style="width: 5%;">{locale.T('view')}</td>
					<th style="width: 10%;">{locale.T('result')}</th>
					<th style="width: 15%;"></th>
				</tr>
			</thead>
			<tbody>
				{#each get_imagires as item, index}
					<tr class="text-center">
						<td>{n + index}</td>
						<td class="text-center">
							<DDMMYYYYFormat style="date" date={item.visit?.date_checkup} />
							<br />
							<DDMMYYYYFormat style="time" date={item.visit?.date_checkup} />
						</td>

						<td class="text-center">
							PT{item.visit?.patient_id}
							<br />
							IM{item.id}
						</td>
						<td>
							{item.visit?.patient?.name_khmer} <br />
							{item.visit?.patient?.name_latin}
							<GenderAge
								dob={item?.visit?.patient?.dob}
								date={new Date()}
								gender={item?.visit?.patient?.gender}
							/>
						</td>

						<td>{item?.visit?.staff?.name_latin}</td>
						<td>{item?.visit?.checkin_type}</td>
						<td>
							<div>
								<span class=" badge text-bg-info text-start">{item.product?.products ?? ''}</span>
								<br />
							</div>
						</td>
						<td class="text-start">
							{#if item.status}
								<a
									class="btn btn-light btn-sm"
									target="_blank"
									href="/report/{item.id}/imagerie?row=true"
									><i class="fa-regular fa-image"></i> {locale.T('up_down')}
								</a>

								<a
									class="btn btn-light btn-sm"
									target="_blank"
									href="/report/{item.id}/imagerie?row=false"
									><i class="fa-regular fa-file-image"></i> {locale.T('left_right')}
								</a>
								<a class="btn btn-light btn-sm" target="_blank" href="/report/{item.id}/imagerie"
									><i class="fa-solid fa-images"></i> {locale.T('ecg')}
								</a>
							{/if}
						</td>
						<td class="text-center">
							{#if item.status}
								{#if !item.is_ob_form}
									<a
										href="/imagerie/result/general?imagerie_request_id={item.id}&group_id={item
											.product?.group_id}"
										class="btn btn-warning btn-sm"
										>{locale.T('edit')}
									</a>
								{:else}
									<a
										href="/imagerie/result/ob?imagerie_request_id={item.id}"
										class="btn btn-warning btn-sm"
										>{locale.T('edit')}
									</a>
								{/if}
							{:else if !item.is_ob_form}
								<a
									href="/imagerie/result/general?imagerie_request_id={item.id}&group_id={item
										.product?.group_id}"
									class="btn btn-primary btn-sm"
									>{locale.T('result')}
								</a>
							{:else}
								<a
									href="/imagerie/result/ob?imagerie_request_id={item.id}"
									class="btn btn-primary btn-sm"
									>{locale.T('result')}
								</a>
							{/if}
						</td>
						<td>
							<div>
								<a
									style="background-color: deeppink;"
									href={'#'}
									type="button"
									class="btn btn-sm text-white"
									>{locale.T('invoice')}
								</a>
							</div>
						</td>
					</tr>
				{/each}
				<tr class="table-success">
					<td colspan="10" class="text-center">
						{locale.T('total')}: {get_imagires.length}
						{locale.T('people')},
						{locale.T('male')}: {total_male}
						{locale.T('female')}: {get_imagires.length - total_male}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
