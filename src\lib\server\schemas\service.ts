import { boolean, int, mysqlTable } from 'drizzle-orm/mysql-core';
import { progressNote, visit } from './visit';
import { relations } from 'drizzle-orm';
import { product } from './product';
import { operationProtocol } from './operationProtocol';

export const service = mysqlTable('service', {
	id: int().primaryKey().autoincrement(),
	visit_id: int().references(() => visit.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	product_id: int().references(() => product.id, {
		onDelete: 'cascade'
	}),
	is_paid_ipd: boolean().default(false).notNull()
});
export const serviceRelations = relations(service, ({ one }) => ({
	visit: one(visit, {
		references: [visit.id],
		fields: [service.visit_id]
	}),
	progressNote: one(progressNote, {
		references: [progressNote.id],
		fields: [service.progress_note_id]
	}),
	product: one(product, {
		references: [product.id],
		fields: [service.product_id]
	}),
	operationProtocol: one(operationProtocol)
}));
