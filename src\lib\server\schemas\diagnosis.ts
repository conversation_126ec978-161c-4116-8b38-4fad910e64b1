import { relations } from 'drizzle-orm';
import { int, mysqlTable, varchar } from 'drizzle-orm/mysql-core';

export const diagnosis = mysqlTable('diagnosis', {
	id: int().primaryKey().autoincrement(),
	diagnosis: varchar({ length: 255 }),
	diagnosis_khmer: varchar({ length: 255 }),
	diagnosis_type_id: int().references(() => diagnosisType.id)
});

export const diagnosisRelations = relations(diagnosis, ({ one }) => ({
	diagnosisType: one(diagnosisType, {
		fields: [diagnosis.diagnosis_type_id],
		references: [diagnosisType.id]
	})
}));

export const diagnosisType = mysqlTable('diagnosis_type', {
	id: int().primaryKey().autoincrement(),
	diagnosis_type: varchar({ length: 150 }).notNull()
});

export const diagnosisTypeRelations = relations(diagnosisType, ({ many }) => ({
	diagnosis: many(diagnosis)
}));
