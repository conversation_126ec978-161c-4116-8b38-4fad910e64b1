<script lang="ts">
	import { page } from '$app/state';
	import ClinichInfo from '$lib/coms-report/ClinichInfo.svelte';
	import Sign from '$lib/coms-report/Sign.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { dobToAge } from '$lib/helper';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_clinic_info, get_visit, sort_by_group, get_upload } = $derived(data);
	let age_p_visit = $derived(dobToAge(get_visit?.patient.dob ?? '', get_visit?.date_checkup ?? ''));
</script>

<div class="header">
	<ClinichInfo data={{ get_clinic_info, get_upload }} />

	<div class="border p-2 pb-0">
		<table class=" table table-sm table-borderless table-light">
			<thead style="font-size: 120%;">
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Khmer Name</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.patient?.name_khmer}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Gender</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.patient?.gender ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">ID</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						<span>PT{get_visit?.patient_id}</span>
						<span>PR{get_visit?.id}</span>
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Latin Name</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.patient?.name_latin}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Age</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						{age_p_visit}
					</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">CheckIn Date</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						<DDMMYYYYFormat date={get_visit?.date_checkup} />
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Consultation</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.staff?.name_latin ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Symptoms</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.etiology ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Phone</td>
					<td class="p-0 m-0"> : </td>
					<td class="en_font_times_new_roman p-0 m-0">{get_visit?.patient?.telephone ?? ''}</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Visit</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.checkin_type ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Address</td>
					<td class="p-0 m-0"> : </td>
					<td colspan="7" class="kh_font_battambang p-0 m-0">
						{get_visit?.patient?.village?.type ?? ''}
						{get_visit?.patient?.village?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.commune?.type ?? ''}
						{get_visit?.patient?.commune?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.district?.type ?? ''}
						{get_visit?.patient?.district?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.provice?.type ?? ''}
						{get_visit?.patient?.provice?.name_khmer ?? ''}
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Diagnosis (Dx)</td>
					<td class="p-0 m-0"> : </td>
					<td colspan="7" class="kh_font_battambang p-0 m-0"
						>{get_visit?.accessment?.diagnosis_or_problem}</td
					>
				</tr>
			</thead>
		</table>
	</div>
</div>

<table class="w-100 table-light">
	<thead>
		<tr>
			<td>
				<div class="header-space">&nbsp;</div>
			</td>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
				<h4 class="kh_font_muol_light text-center pt-1">{'ការស្នើរសុំរបស់គ្រូពេទ្យ'}</h4>
				<h4 class="en_font_times_new_roman text-center pt-1">Clinical Request</h4>
				<h4 class="en_font_times_new_roman pt-1 text-decoration-underline">#Laboratory tests</h4>
			</td>
		</tr>
		<tr>
			<td>
				<div class="row">
					{#each sort_by_group || [] as item, index}
						<div class="col-4 fs-5 p-2">
							{index + 1} : {item?.product?.products ?? ''}
						</div>
					{/each}
				</div>
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td>
				<div class="footer-space">&nbsp;</div>
			</td>
		</tr>
	</tfoot>
</table>

<div class="footer">
	<Sign
		right={{
			name: get_visit?.staff?.name_khmer,
			date: get_visit?.date_checkup,
			img: '/sign.png'
		}}
		qr={page.url.href}
	/>

	<div>
		<hr />
		<h6 style="color:#0000FF" class="text-center">
			{get_clinic_info?.address ?? ''}
		</h6>
	</div>
</div>
