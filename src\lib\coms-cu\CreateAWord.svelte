<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: {
			word: string | null;
			id: number;
		}[];
		actionCreate: string;
		actionDelete: string;
		title: string;
		modal: string;
	}
	let { data, actionCreate, actionDelete, title, modal }: Props = $props();
	let id: number | null = $state(null);
	let loading = $state(false);
	let isEdit = $state(false);
	let find_word = $derived(data.find((e) => e.id === id));
	let word = $state('');
	let q = $state('');
	let resut_q = $derived(data.filter((e) => e?.word?.toLowerCase().includes(q.toLowerCase())));
	let valueWord = $state('');
	let isFoundWord = $derived(data.some((e) => e.word === valueWord));
</script>

<div class="modal fade" id={modal} data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">{title}</h4>
				<button
					id="clos_modal_word"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="alert alert-secondary">
					<Form
						action={actionCreate}
						method="post"
						bind:loading
						fnSuccess={() => {
							isEdit = false;
							valueWord = '';
						}}
					>
						{#if isEdit && id}
							<input type="hidden" name="id" value={id} />
						{/if}
						<div class="row">
							<div class="col">
								{#if find_word && isEdit}
									<input
										autocomplete="off"
										oninput={(e) => (valueWord = e.currentTarget.value)}
										value={word}
										required
										name="word"
										type="text"
										placeholder={locale.T('exspend_type')}
										class="form-control"
									/>
								{:else}
									<input
										autocomplete="off"
										bind:value={valueWord}
										required
										name="word"
										type="text"
										placeholder={locale.T('exspend_type')}
										class="form-control"
									/>
								{/if}
							</div>
							<div class="col-auto">
								<fieldset disabled={isFoundWord}>
									{#if isEdit && id}
										<SubmitButton {loading} name={locale.T('update')} />
									{:else}
										<SubmitButton {loading} name={locale.T('add')} />
									{/if}
								</fieldset>
							</div>
						</div>
					</Form>
				</div>
				<div class="card">
					<div class="card-header">
						<input
							autocomplete="off"
							bind:value={q}
							placeholder={locale.T('search')}
							class="form-control"
							type="text"
						/>
					</div>
					<div style="height: 500px;" class="card-body overflow-auto">
						<div class=" row">
							{#each resut_q as item}
								{#if item.word}
									<div class="col-auto p-2">
										<Form
											bind:loading
											fnSuccess={() => {
												isEdit = false;
												valueWord = '';
											}}
											action={actionDelete}
											method="post"
										>
											<input type="hidden" name="id" value={item.id} />
											<label
												class:text-danger={valueWord === item.word}
												class:fs-1={valueWord === item.word}
												for={item.id.toString()}
												class="custom-control-label">{item.word}</label
											>
											<button
												aria-label="submit"
												type="button"
												class={id === item.id && isEdit
													? 'btn btn-link m-0 p-0'
													: 'btn btn-link text-secondary m-0 p-0'}
												onclick={() => {
													id = 0;
													id = item.id;
													word = item?.word ?? '';
													isEdit = !isEdit;
													valueWord = '';
												}}><i class="fa-solid fa-file-pen"></i></button
											>
											{#if id === item.id && isEdit}
												<button
													onclick={(e) =>
														confirm(locale.T('confirm_delete')) &&
														e.currentTarget.form?.requestSubmit()}
													aria-label="submit"
													class="btn btn-link text-danger m-0 p-0"
													type="button"><i class="fa-solid fa-x"></i></button
												>
											{/if}
										</Form>
									</div>
								{/if}
							{/each}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
