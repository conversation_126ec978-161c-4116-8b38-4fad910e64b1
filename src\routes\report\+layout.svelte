<script lang="ts">
	import { page } from '$app/state';
	import { locale } from '$lib/translations/locales.svelte';
	import type { LayoutServerData } from './$types';
	interface Props {
		data: LayoutServerData;
		children?: import('svelte').Snippet;
	}
	let { data, children }: Props = $props();
	let { get_clinic_info } = $derived(data);

	let zoom = $state(0.85);
	function handlePrint() {
		window.scrollTo({ top: 0, behavior: 'smooth' });

		setTimeout(() => {
			window.print();
		}, 500); // Adjust the timeout as needed
	}
</script>

<svelte:head>
	<style>
		@media print {
			.footer,
			.footer-space {
				height: 280px;
			}
			.header,
			.header-space {
				height: 350px;
			}
			.header {
				width: 100%;
				position: fixed;
			}
			.footer {
				width: 100%;
				position: fixed;
				bottom: 0mm;
			}
			.main {
				zoom: 100% !important;
			}
		}
	</style>
</svelte:head>
<div class="main">
	<div class="row g-0 sticky-top">
		<div class="col-auto">
			{#if page.url.pathname.includes('/billing')}
				<a
					href="/billing/report"
					class="text-center rounded-0 w-100 mt-1 mb-1 btn btn-warning btn-lg d-print-none text-decoration-none"
					><i class="fa-solid fa-arrow-left"></i> {locale.T('back')}</a
				>
			{/if}
		</div>
		<div class="col">
			<button
				onclick={handlePrint}
				class="text-center rounded-0 w-100 mt-1 mb-1 btn btn-primary btn-lg d-print-none text-decoration-none"
			>
				<i class="fa-solid fa-print"></i>
				{locale.T('print')}
			</button>
		</div>
	</div>

	{@render children?.()}
</div>

<style>
	@page {
		@bottom-right {
			content: counter(page) '/' counter(pages);
			padding-bottom: 5mm;
			size: A4;
		}
		margin: 30px;
	}
	.main {
		max-width: 1200px;
		margin-left: auto;
		margin-right: auto;
		zoom: 85%;
	}
</style>
