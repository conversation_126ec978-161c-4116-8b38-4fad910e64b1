import { db } from '$lib/server/db';
import { exspend, inventory, payment, product } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { and, between, desc, eq, isNull, like, or } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';
import { pagination } from '$lib/server/utils';

export const load = (async ({ url }) => {
	const start = url.searchParams.get('start') ?? '';
	const end = url.searchParams.get('end') ?? '';
	const status = url.searchParams.get('status') ?? '';
	let p = false;
	if (start || end || status) p = true;
	const q = url.searchParams.get('q') || '';
	const exspend_id = url.searchParams.get('exspend_id') || '';
	const get_payments = await db.query.payment.findMany({
		where: eq(payment.exspend_id, +exspend_id)
	});
	const get_exspends = await db.query.exspend.findMany({
		where: p
			? and(
					start && end ? between(exspend.datetime_invoice, start, end) : undefined,
					isNull(exspend.exspend_type_id),
					or(like(exspend.description, `%${q}%`), like(exspend.invoice_no, `%${q}%`))
				)
			: isNull(exspend.exspend_type_id),
		with: { exspendType: true, supplier: true },
		orderBy: desc(exspend.datetime_invoice),
		...pagination(url)
	});
	const get_currency = await db.query.currency.findFirst({});
	const count = await db.$count(
		exspend,
		p
			? and(
					start && end ? between(exspend.datetime_invoice, start, end) : undefined,
					isNull(exspend.exspend_type_id),
					or(like(exspend.description, `%${q}%`), like(exspend.invoice_no, `%${q}%`))
				)
			: isNull(exspend.exspend_type_id)
	);
	return { get_exspends, get_currency, get_payments, items: count };
}) satisfies PageServerLoad;
export const actions: Actions = {
	delete_exspend: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (isNaN(+id)) return fail(400, { id: true });
		const get_exspend = await db.query.exspend.findFirst({
			where: eq(exspend.id, +id),
			with: {
				inventory: true
			}
		});
		for (const e of get_exspend?.inventory || []) {
			const get_product = await db.query.product.findFirst({
				where: eq(product.id, e.product_id),
				with: { inventory: true }
			});
			if (Number(get_product?.inventory?.length) <= 1) {
				await db
					.update(inventory)
					.set({ exspend_id: null, is_close_inventory: false })
					.where(eq(inventory.id, Number(get_product?.inventory[0].id)))
					.catch((e) => {
						logError({ url, body, err: e });
					});
			}
		}
		await db
			.delete(exspend)
			.where(eq(exspend.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
