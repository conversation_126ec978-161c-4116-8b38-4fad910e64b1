<script lang="ts">
	import { invalidateAll } from '$app/navigation';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	interface Diagnosis {
		id: number;
		diagnosis: string | null;
		diagnosis_khmer: string | null;
		diagnosis_type_id: number | null;
	}
	interface Diagnosis_Type {
		id: number;
		diagnosis_type: string;
	}
	let q: string = $state('');
	function handleText(text: string) {
		if (value.includes(' '.concat(text).concat(','))) {
			value = value.replace(' '.concat(text.concat(',')), '');
		} else {
			value = value.concat(' '.concat(text)).concat(',');
		}
	}

	let diagnosis_english = $state('');
	let diagnosis_khmer = $state('');
	interface Props {
		diagnosis: Diagnosis[];
		diagnosis_type: Diagnosis_Type[];
		modal_name: string;
		value?: string;
		name?: string;
		Class?: string;
		isEdit: boolean;
		diagnosis_id: number | null;
	}

	let {
		diagnosis,
		isEdit = $bindable(),
		diagnosis_id = $bindable(),
		diagnosis_type,
		modal_name,
		value = $bindable(''),
		name = '',
		Class = 'btn btn-outline-primary btn-sm'
	}: Props = $props();
	let diagnosis_type_id: number | null = $state(null);
	let get_diagnosis_by_type = $derived(
		diagnosis.filter((e) => e.diagnosis_type_id === diagnosis_type_id)
	);
	let find_diagnosis = $derived(
		get_diagnosis_by_type.filter(
			(el: Diagnosis) =>
				el?.diagnosis?.toLowerCase().includes(q.toLowerCase()) || el.diagnosis_khmer?.includes(q)
		)
	);
	$effect(() => {
		if (isEdit === false) {
			diagnosis_english = '';
			diagnosis_khmer = '';
			diagnosis_id = null;
		}
	});
	let loading = $state(false);
	const delete_diagnosis = async (id: string | number | null) => {
		store.globalLoading = true;
		const formData = new FormData();
		formData.append('diagnosis_id', id?.toString() ?? '');
		await fetch('?/delete_diagnosis', {
			method: 'POST',
			body: formData
		});
		await invalidateAll();
		loading = false;
		store.globalLoading = false;
	};
</script>

<div class="modal fade" id={modal_name}>
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">{name}</h4>
				<button
					id="close_diagnosis"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<Form
					fnSuccess={() => {
						diagnosis_english = '';
						diagnosis_khmer = '';
						diagnosis_id = null;
						isEdit = false;
					}}
					fnError={() => {
						loading = false;
					}}
					bind:loading
					action="?/create_diagnosis"
					method="post"
				>
					<input type="hidden" name="diagnosis_id" value={diagnosis_id ?? ''} />
					<div class=" pb-3">
						<div class="row">
							<div class="col">
								<input
									bind:value={diagnosis_english}
									required
									name="diagnosis_english"
									type="text"
									placeholder="Diagnosis English"
									class="form-control"
								/>
							</div>
							<div class="col">
								<input
									bind:value={diagnosis_khmer}
									required
									name="diagnosis_khmer"
									type="text"
									placeholder="Diagnosis Khmer"
									class="form-control"
								/>
							</div>

							<div class="col-auto">
								<button disabled={loading} type="submit" class="btn btn-success">
									{#if diagnosis_id}
										{locale.T('update')}
									{:else}
										{locale.T('add')}
									{/if}
								</button>
							</div>
						</div>
						<div class="col-12 pt-2">
							<SelectParam
								bind:value={diagnosis_type_id}
								name="diagnosis_type_id"
								items={diagnosis_type.map((e) => ({ id: e.id, name: e.diagnosis_type }))}
							/>
						</div>
					</div>
				</Form>
				<div class="card">
					<div class="card-header">
						<input bind:value={q} type="search" placeholder="Search" class="form-control" />
					</div>
					<div style="height: 500px;" class="card-body overflow-auto">
						<div class="p-2 row">
							{#each find_diagnosis as item (item.id)}
								<div class="p-2 col-12">
									<input type="hidden" name="id" value={item.id} />
									<input
										onclick={() => {
											handleText(item?.diagnosis ?? '');
										}}
										checked={value.includes(' '.concat(item?.diagnosis ?? '').concat(','))}
										class="form-check-input"
										type="checkbox"
										id={item.id.toString()}
										value={item.diagnosis}
									/>
									<label for={item.id.toString()} class="custom-control-label"
										>{item.diagnosis} {item.diagnosis_khmer}</label
									>
									<button
										aria-label="wahtis"
										type="button"
										class={diagnosis_id === item.id && isEdit
											? 'btn btn-link m-0 p-0'
											: 'btn btn-link text-secondary m-0 p-0'}
										onclick={() => {
											diagnosis_id = item.id;
											isEdit = !isEdit;
											diagnosis_english = item?.diagnosis ?? '';
											diagnosis_khmer = item?.diagnosis_khmer ?? '';
										}}><i class="fa-solid fa-file-pen"></i></button
									>
									{#if diagnosis_id === item.id && isEdit}
										<button
											onclick={(e) => {
												e.stopPropagation();
												if (confirm(locale.T('confirm_delete'))) {
													delete_diagnosis(item.id);
												}
											}}
											aria-label="submit"
											class="btn btn-link text-danger m-0 p-0"
											type="submit"><i class="fa-solid fa-x"></i></button
										>
									{/if}
								</div>
							{/each}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
