<script lang="ts">
	import PrintBarcode from '$lib/coms-ipd-opd/PrintBarcode.svelte';
	import PrintCardPatient from '$lib/coms-ipd-opd/PrintCardPatient.svelte';
	import { khmerDate } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_patient, count_ipd, count_opd, get_clinic_info } = $derived(data);
</script>

<div class="row">
	<div class="col-sm-6">
		<a href="/patient/all" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-clock-rotate-left"></i>
					{locale.T('history')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fa-regular fa-user"></i>
					{locale.T('patient_info')}
				</a>
			</li>
		</ol>
	</div>
</div>
<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col-6">
				<div class="fs-4">{locale.T('patient_info')}</div>
			</div>
			<div class="col-6">
				<div class="fs-4 text-end">
					<a
						href="/patient/create?patient_id={get_patient?.id}&province_id={get_patient?.province_id}&district_id={get_patient?.district_id}&commune_id={get_patient?.commune_id}&village_id={get_patient?.village_id}"
						class="btn btn-link p-0"
						><i class="fa-solid fa-pen-to-square"></i>
						{locale.T('edit')}
					</a>
				</div>
			</div>
		</div>
	</div>
	<div class="card-body">
		<div class="row">
			<div class="col-sm-4">
				<div class="card">
					<div class="card-body">
						<div class=" alert alert-primary">
							<div class="text-center">
								<img
									src={get_patient?.uploads?.filename
										? `${get_patient?.uploads?.filename}`
										: '/no-user.png'}
									alt=""
									style="max-width: 300px;height: auto;"
									class="img-thumbnail"
								/>
							</div>
							<div class="text-center pt-2 mt-2">
								<h3 class="kh_font_muol_light">{get_patient?.name_khmer}</h3>
								<h3>{get_patient?.name_latin}</h3>
							</div>
						</div>

						<div>
							<div class="alert alert-primary mt-2 overflow-auto">
								<PrintCardPatient patient_info={get_patient} {get_clinic_info} is_show>
									{locale.T('print')} <i class="fa-solid fa-address-card"></i>
								</PrintCardPatient>
							</div>
							<div class="alert alert-primary mt-2">
								<PrintBarcode patient_info={get_patient} is_show>
									{locale.T('print')} <i class="fa-solid fa-barcode"></i>
								</PrintBarcode>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-sm-8">
				<div class="card">
					<div class="card-body">
						<div class="row">
							<div class="col-3">
								<div class="form-control">{locale.T('patient_name')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{get_patient?.name_khmer}
									{#if get_patient?.name_latin}
										({get_patient?.name_latin})
									{/if}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<div class="form-control">{locale.T('gender')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{#if get_patient?.gender?.toLowerCase() === 'male'}
										{locale.T('male')}
									{:else if get_patient?.gender?.toLowerCase() === 'female'}
										{locale.T('female')}
									{:else}
										{locale.T('none')}
									{/if}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<div class="form-control">{locale.T('dob')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{khmerDate(get_patient?.dob, 'date')}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<div class="form-control">{locale.T('contact')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{get_patient?.telephone || locale.T('none')}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<div class="form-control">{locale.T('education')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{get_patient?.education || locale.T('none')}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<div class="form-control">{locale.T('id_card_or_passport')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{get_patient?.id_cart_passport || locale.T('none')}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<div class="form-control">{locale.T('material')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{get_patient?.material_status || locale.T('none')}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<div class="form-control">{locale.T('occupation')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{get_patient?.occupation || locale.T('none')}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<div class="form-control">{locale.T('work_place')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{get_patient?.work_place || locale.T('none')}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<div class="form-control">{locale.T('other')}</div>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{get_patient?.other || locale.T('none')}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<a
									href="/visit?visit_type=ipd&patient_id={get_patient?.id}"
									class="form-control text-success"
								>
									<i class=" fas fa-procedures me-2"></i>
									{locale.T('ipd')}
								</a>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{count_ipd || 0}
									{locale.T('times_')}
								</div>
							</div>
						</div>
						<div class="row mt-3">
							<div class="col-3">
								<a
									href="/visit?visit_type=opd&patient_id={get_patient?.id}"
									class="form-control text-primary"
								>
									<i class=" fas fa-stethoscope me-2"></i>
									{locale.T('opd')}
								</a>
							</div>
							<div class="col-9">
								<div class="form-control text-primary">
									{count_opd || 0}
									{locale.T('times_')}
								</div>
							</div>
						</div>
						<div class="alert alert-primary mt-3">
							<div class="row mt-3">
								<div class="col-3">
									<div class="form-control">{locale.T('mother')}</div>
								</div>
								<div class="col-9">
									<div class="form-control text-primary">
										{`${get_patient?.m_name_khmer} (${get_patient?.m_name_latin})`},
										{locale.T('contact')}
										{get_patient?.m_telephone},
										{locale.T('occupation')}
										{get_patient?.m_occupation}
									</div>
								</div>
							</div>
							<div class="row mt-3">
								<div class="col-3">
									<div class="form-control">{locale.T('father')}</div>
								</div>
								<div class="col-9">
									<div class="form-control text-primary">
										{`${get_patient?.f_name_khmer} (${get_patient?.f_name_latin})`},
										{locale.T('contact')}
										{get_patient?.f_telephone},
										{locale.T('occupation')}
										{get_patient?.f_occupation}
									</div>
								</div>
							</div>
							<div class="row mt-3">
								<div class="col-3">
									<div class="form-control">{locale.T('carer')}</div>
								</div>
								<div class="col-9">
									<div class="form-control text-primary">
										{`${get_patient?.c_name_khmer} (${get_patient?.c_name_latin})`},
										{locale.T('contact')}
										{get_patient?.c_telephone},
										{locale.T('occupation')}
										{get_patient?.c_occupation}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
