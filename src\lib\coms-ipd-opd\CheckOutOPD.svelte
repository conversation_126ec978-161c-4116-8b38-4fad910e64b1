<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	let loading = $state(false);
	interface Props {
		status: string | undefined;
		class?: string;
		action: string;
		children?: import('svelte').Snippet;
	}

	let { status, class: className = 'btn btn-primary', children, action }: Props = $props();
	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
	let disableBTN = $derived(status === 'checking' || status === 'paying' ? false : true);
</script>

<button
	disabled={disableBTN}
	type="button"
	data-bs-toggle="modal"
	data-bs-target={'#'.concat(id?.toString() ?? '')}
	class={className}
>
	{#if status === 'checking'}
		{locale.T('discharge_and_go_home')}
	{:else if status === 'paying'}
		<i class="fa-solid fa-spinner fa-spin"></i> {locale.T('paying')}
	{:else}
		{locale.T('checkout')}
	{/if}
</button>

<div class="modal fade" tabindex="-1" role="dialog" {id} data-bs-backdrop="static">
	<Form
		{action}
		bind:loading
		fnSuccess={() => document.getElementById('close_confirm_submit')?.click()}
		method="post"
		class="modal-dialog modal-md"
	>
		{@render children?.()}
		<div class="modal-content rounded-3 shadow">
			<div class="modal-header py-2 justify-content-center text-bg-warning">
				<span class="fs-3">
					{#if status === 'checking'}
						{locale.T('discharge_and_go_home')}
					{:else}
						{locale.T('send_back_to_checking')}
					{/if}
				</span>
			</div>
			<div class="modal-body">
				<ul class="list-group">
					<li class="list-group-item">
						<i class="fa-solid fa-triangle-exclamation"></i>
						{locale.T('after_done_of_pay_you_can_not_edit_infomations')}
					</li>
				</ul>
			</div>
			<div class="modal-footer flex-nowrap p-0">
				<button
					id="close_delete_modal"
					type="button"
					class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
					data-bs-dismiss="modal">{locale.T('no')}</button
				>
				<button
					data-bs-dismiss="modal"
					disabled={loading}
					type="submit"
					class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
				>
					<strong>{locale.T('yes')}</strong>
				</button>
			</div>
		</div>
	</Form>
</div>
