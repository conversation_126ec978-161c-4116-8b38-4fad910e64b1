<script lang="ts">
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { LayoutServerData } from '../../routes/(dash)/$types';
	type Data = Pick<LayoutServerData, 'get_progress_note' | 'get_wards'>;
	interface Props {
		data: Data;
	}
	let { data }: Props = $props();
	let { get_wards, get_progress_note } = $derived(data);
</script>

<!-- @_List_Parameter -->
<div class="modal fade" id="view_room">
	<div class="modal-dialog modal-dialog-scrollabl modal-fullscreen">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('department')}-{locale.T('room')}-{locale.T('bed')}</h4>
				<button
					id="close_view_room"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<br />
				{#each get_wards as item}
					{@const rooms = item.room}
					<div class="card border-success border-3 mb-5 position-relative">
						<span class="position-absolute top-0 start-50 translate-middle btn btn-success btn-lg">
							{item.ward}
						</span>
						<div class="card-body">
							{#each rooms as iitem}
								{@const beds = iitem.bed}
								<div class="card border-primary border-3 mt-5 px-3 py-1 position-relative">
									<span class="position-absolute top-0 start-50 translate-middle btn btn-primary">
										{item.ward}-{iitem.room}
									</span>

									<div class="row pt-4">
										{#each beds as bed}
											{@const find_progress_note = get_progress_note.find(
												(e) => e.activeBed.find((e) => e.datetime_out === null)?.bed_id === bed.id
											)}
											<div class="card bg-light col-4">
												<div class="row">
													<div class:text-danger={find_progress_note} class="col-md-3 text-primary">
														<i class="fa-solid fa-bed fa-6x"></i>
													</div>
													<div class="col-md-9">
														<div class="card-body">
															{#if find_progress_note}
																<a
																	onclick={() =>
																		document.getElementById('close_view_room')?.click()}
																	href="/ipd/{find_progress_note?.id}/progress-note"
																	class=" btn btn-danger m-0 btn-sm"
																>
																	{bed.bed}
																</a>
															{:else}
																<button class="e btn btn-primary m-0 btn-sm">
																	{bed.bed}
																</button>
															{/if}

															<table>
																<thead>
																	{#if find_progress_note}
																		<tr>
																			<td>{locale.T('name_khmer')}</td>
																			<td> : </td>
																			<td>
																				<p class="card-text">
																					{find_progress_note?.patient.name_khmer ?? ''}
																				</p>
																			</td>
																		</tr>
																		<tr>
																			<td>{locale.T('name_latin')}</td>
																			<td> : </td>
																			<td>
																				<p class="card-text">
																					{find_progress_note?.patient.name_latin ?? ''}
																				</p>
																			</td>
																		</tr>
																		<tr>
																			<td>{locale.T('etiology')}</td>
																			<td> : </td>
																			<td>
																				<p class="card-text">
																					{find_progress_note?.etiology ?? ''}
																				</p>
																			</td>
																		</tr>
																		<tr>
																			<td>{locale.T('date')}</td>
																			<td> : </td>
																			<td>
																				<p class="card-text">
																					<DDMMYYYYFormat
																						date={find_progress_note?.date_checkup}
																					/>
																				</p>
																			</td>
																		</tr>
																	{:else}
																		<tr>
																			<td>{locale.T('name_khmer')}</td>
																			<td> : </td>
																			<td> </td>
																		</tr>
																		<tr>
																			<td>{locale.T('name_latin')}</td>
																			<td> : </td>
																			<td> </td>
																		</tr>
																		<tr>
																			<td>{locale.T('etiology')}</td>
																			<td> : </td>
																			<td> </td>
																		</tr>
																		<tr>
																			<td>{locale.T('date')}</td>
																			<td> : </td>
																			<td> </td>
																		</tr>
																	{/if}
																</thead>
															</table>
														</div>
													</div>
												</div>
											</div>
										{/each}
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/each}
				<!--  
				<div class="pt-4">
					{#each get_wards as item}
						{@const rooms = item.room}
						<div style="border:2px solid blue;" class=" position-relative rounded">
							<div
								style="
									left: 10%;
									-ms-transform: translate(-50%, -50%);
									transform: translate(-50%, -50%);"
								class="position-absolute top-0 m-0"
							>
								<button class="btn btn-primary btn-lg">{item.ward ?? ''}</button>
							</div>
							<br />
							{#each rooms as room}
								{@const beds = room.bed}
								<div class="separator"><button class="btn btn-primary">{room.room}</button></div>
								<br />
								<div>
									<div class="row">
										{#each beds as bed}
											{@const find_progress_note = get_progress_note.find(
												(e) => e.activeBed.find((e) => e.datetime_out === null)?.bed_id === bed.id
											)}
											<div class="card bg-light col-4">
												<div class="row gap-0">
													<div class:text-danger={find_progress_note} class="col-md-3 text-primary">
														<i class="fa-solid fa-bed fa-6x"></i>
													</div>
													<div class="col-md-9">
														<div class="card-body">
															{#if find_progress_note}
																<a
																	onclick={() =>
																		document.getElementById('close_view_room')?.click()}
																	href="/ipd/{find_progress_note?.id}/progress-note"
																	class=" btn btn-danger m-0 btn-sm"
																>
																	{bed.bed}
																</a>
															{:else}
																<button class="e btn btn-primary m-0 btn-sm">
																	{bed.bed}
																</button>
															{/if}
															{#if find_progress_note}
																<a
																	onclick={() =>
																		document.getElementById('close_view_room')?.click()}
																	href="/ipd?patient_id={find_progress_note?.patient_id}&progress_note_id={find_progress_note?.id}"
																	class:btn-warning={find_progress_note}
																	class="btn btn-primary m-0 btn-sm"
																>
																	{locale.T('chang_bed')}
																</a>
															{/if}
															<table>
																<thead>
																	{#if find_progress_note}
																		<tr>
																			<td>{locale.T('name_khmer')}</td>
																			<td> : </td>
																			<td>
																				<p class="card-text">
																					{find_progress_note?.patient.name_khmer ?? ''}
																				</p>
																			</td>
																		</tr>
																		<tr>
																			<td>{locale.T('name_latin')}</td>
																			<td> : </td>
																			<td>
																				<p class="card-text">
																					{find_progress_note?.patient.name_latin ?? ''}
																				</p>
																			</td>
																		</tr>
																		<tr>
																			<td>{locale.T('etiology')}</td>
																			<td> : </td>
																			<td>
																				<p class="card-text">
																					{find_progress_note?.etiology ?? ''}
																				</p>
																			</td>
																		</tr>
																		<tr>
																			<td>{locale.T('date')}</td>
																			<td> : </td>
																			<td>
																				<p class="card-text">
																					<DDMMYYYYFormat date={find_progress_note?.date_checkup} />
																				</p>
																			</td>
																		</tr>
																	{:else}
																		<tr>
																			<td>{locale.T('name_khmer')}</td>
																			<td> : </td>
																			<td> </td>
																		</tr>
																		<tr>
																			<td>{locale.T('name_latin')}</td>
																			<td> : </td>
																			<td> </td>
																		</tr>
																		<tr>
																			<td>{locale.T('etiology')}</td>
																			<td> : </td>
																			<td> </td>
																		</tr>
																		<tr>
																			<td>{locale.T('date')}</td>
																			<td> : </td>
																			<td> </td>
																		</tr>
																	{/if}
																</thead>
															</table>
														</div>
													</div>
												</div>
											</div>
										{/each}
									</div>
								</div>
							{/each}
						</div>
						<br />
						<br />
					{/each}
				</div>
				-->
			</div>
		</div>
	</div>
</div>
