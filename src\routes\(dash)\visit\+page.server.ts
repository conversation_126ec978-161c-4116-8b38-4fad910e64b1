import { db } from '$lib/server/db';
import {
	product,
	patient,
	visit,
	vitalSign,
	words,
	appointment,
	billing,
	progressNote,
	activeDepartment,
	activeBed
} from '$lib/server/schemas';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { eq, isNull } from 'drizzle-orm';
import { genVisitID, YYYYMMDD_Format } from '$lib/server/utils';
import logError from '$lib/server/utils/logError';
import {
	AddBedToCharge,
	billingIPD,
	billingOPD,
	billingService,
	createProductOrder
} from '$lib/server/models';

export const load = (async ({ url, parent }) => {
	await parent();
	const visit_id = url.searchParams.get('visit_id');
	const patient_id = url.searchParams.get('patient_id');
	const create_progress_note = url.searchParams.get('create_progress_note') ?? '';
	if (!url.searchParams.has('patient_id')) redirect(303, '/patient/opd');
	const get_departments = await db.query.product.findMany({
		where: eq(product.category_id, 11)
	});
	const get_patient = await db.query.patient.findFirst({
		where: eq(patient.id, Number(patient_id)),
		with: {
			provice: true,
			district: true,
			commune: true,
			village: true
		}
	});
	const get_progress_notes = await db.query.progressNote.findMany({
		where: isNull(progressNote.date_checkout),
		with: {
			activeBed: {
				with: {
					bed: {
						with: {
							room: {
								with: {
									product: true
								}
							}
						}
					}
				}
			},
			patient: true
		}
	});
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, Number(visit_id)),
		with: {
			vitalSign: true,
			physicalExam: true,
			billing: true
		}
	});
	const get_words_subjective = await db.query.words.findMany({
		where: eq(words.category, 'subjective')
	});
	const get_words_objective = await db.query.words.findMany({
		where: eq(words.category, 'objective')
	});
	const get_wards = await db.query.ward.findMany({
		with: {
			room: {
				with: {
					product: true,
					bed: {
						with: {
							room: {
								with: {
									product: true
								}
							},
							ward: true
						}
					}
				}
			}
		}
	});
	const get_beds = await db.query.bed.findMany({
		with: {
			room: {
				with: {
					product: true
				}
			},
			ward: true
		}
	});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +create_progress_note || 0),
		with: {
			activeBed: {
				with: {
					bed: {
						with: {
							room: {
								with: {
									product: true
								}
							}
						}
					}
				}
			}
		}
	});
	return {
		get_patient,
		get_departments,
		get_words_subjective,
		get_words_objective,
		get_visit,
		get_wards,
		get_beds,
		get_progress_note,
		get_progress_notes
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_visit_opd: async ({ request, locals, url }) => {
		const body = await request.formData();
		const created_at = YYYYMMDD_Format.datetime(new Date());
		const visitID = await genVisitID('OPD');
		const {
			patient_id,
			department_id,
			etiology,
			visit_id: visit_id_,
			appointment_id
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			bmi: false,
			etiology: false,
			patient_id: false,
			department_id: false,
			staff_id: false,
			visit_id: false
		};
		const validOPD = await db.query.visit.findFirst({
			where: eq(visit.id, +visitID)
		});
		if (validOPD) validErr.visit_id = true;
		if (!etiology) validErr.etiology = true;
		if (!patient_id) validErr.patient_id = true;
		if (!department_id) validErr.department_id = true;
		if (!locals?.user?.staff_id) validErr.staff_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (appointment_id) {
			await db
				.update(appointment)
				.set({
					status: true,
					datetime_come: created_at
				})
				.where(eq(appointment.id, +appointment_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (visit_id_) {
			await db
				.update(visit)
				.set({
					checkin_type: 'OPD',
					department_id: Number(department_id),
					etiology: etiology
				})
				.where(eq(visit.id, Number(visit_id_)))
				.catch((e) => {
					logError({ url, body, err: e });
				});

			await createVitalSign(body, url);
			await pushDepartmentToProductOrder({
				product_id: +department_id,
				visit_id: +visit_id_,
				body: body,
				url: url
			});
			redirect(
				303,
				`/visit/subjective?patient_id=${patient_id}&visit_type=opd&visit_id=${visit_id_}`
			);
		} else {
			const visit_id: { id: number }[] = await db
				.insert(visit)
				.values({
					id: +visitID,
					checkin_type: 'OPD',
					patient_id: Number(patient_id),
					date_checkup: created_at,
					sender_id: Number(locals?.user?.staff_id),
					department_id: Number(department_id),
					etiology: etiology
				})
				.$returningId()
				.catch((e) => {
					logError({ url, body, err: e });
					return [];
				});

			if (!visit_id[0].id) return fail(400, { visit_id: true });
			if (visit_id[0].id) {
				// doing billing
				await billingOPD({
					visit_id: visit_id[0].id,
					patient_id: +patient_id,
					body: body,
					url: url
				});
				// creae vital sign
				await createVitalSign(body, url);
			}
			await pushDepartmentToProductOrder({
				product_id: +department_id,
				visit_id: +visit_id[0].id,
				body: body,
				url: url
			});
			redirect(
				303,
				`/visit/subjective?patient_id=${patient_id}&visit_type=opd&visit_id=${visit_id[0].id}`
			);
		}
	},
	create_visit_ipd: async ({ request, locals, url }) => {
		const body = await request.formData();
		const created_at = YYYYMMDD_Format.datetime(new Date());
		const IPDvisitID = await genVisitID('IPD');
		const { patient_id, department_id, etiology, bed_id, visit_id, billing_id, product_id, price } =
			Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			patient_id: false,
			staff_id: false,
			department_id: false,
			etiology: false,
			bed_id: false,
			progress_note_id: false
		};
		if (!bed_id) validErr.bed_id = true;
		if (!etiology) validErr.etiology = true;
		if (!patient_id) validErr.patient_id = true;
		if (!department_id) validErr.department_id = true;
		if (!locals.user?.staff_id) validErr.staff_id = true;
		const validProgressNote = await db.query.progressNote.findFirst({
			where: eq(progressNote.id, +IPDvisitID)
		});
		if (validProgressNote) validErr.progress_note_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		// Updae Progress Note or bed

		const create_progress_note: { id: number }[] = await db
			.insert(progressNote)
			.values({
				id: +IPDvisitID,
				date_checkup: created_at,
				patient_id: +patient_id,
				department_id: Number(department_id),
				etiology: etiology
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});
		// Check is visit transwer form OPD ប្រសិនបើ ប្ដូរមកពី OPD  Visite ប្ដូរទៅជា Checking
		if (visit_id) {
			await db
				.update(visit)
				.set({
					transfer: true,
					checkin_type: 'IPD',
					progress_note_id: create_progress_note[0].id
				})
				.where(eq(visit.id, +visit_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
			await db
				.update(billing)
				.set({
					status: 'checking',
					billing_type: 'CHECKING'
				})
				.where(eq(billing.id, +billing_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
			await AddBedToCharge({
				price: +price,
				product_id: +product_id,
				visit_id: +visit_id,
				body: body,
				url: url
			});
		}
		// End check is visit from OPD
		//Create Active Department
		const create_adepartment: { id: number }[] = await db
			.insert(activeDepartment)
			.values({
				department_id: +department_id,
				datetime_in: created_at,
				progress_note_id: create_progress_note[0].id,
				sender_id: locals.user?.staff_id
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});
		//Create Active Bed
		await db
			.insert(activeBed)
			.values({
				bed_id: +bed_id,
				datetime_in: created_at,
				active_department_id: create_adepartment[0].id,
				progress_note_id: create_progress_note[0].id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		const create_billing_id = await billingIPD({
			progress_id: create_progress_note[0].id,
			patient_id: +patient_id,
			body: body,
			url: url
		});
		await billingService({
			progress_id: create_progress_note[0].id,
			patient_id: +patient_id,
			product_id: +department_id,
			body: body,
			url: url
		});
		redirect(
			303,
			`/visit/payment?patient_id=${patient_id}&visit_type=ipd&billing_id=${create_billing_id}`
		);
	}
};

interface IPushDepartment {
	visit_id: number;
	product_id: number;
	url: URL;
	body: FormData;
}

async function pushDepartmentToProductOrder(param: IPushDepartment) {
	const { product_id, visit_id, url, body } = param;
	const get_billing = await db.query.billing.findFirst({
		where: eq(billing.visit_id, visit_id),
		with: {
			charge: true
		}
	});
	const get_product = await db.query.product.findFirst({
		where: eq(product.id, product_id)
	});
	await createProductOrder({
		charge_id: Number(get_billing?.charge.find((e) => e.charge_on === 'service')?.id),
		price: get_product?.price || null,
		product_id: get_product?.id || null,
		qty: 1,
		body: body,
		url: url
	});
}

async function createVitalSign(FormData: FormData, url: URL) {
	const body = Object.fromEntries(FormData) as Record<string, string>;
	const {
		visit_id,
		bmi,
		rr,
		height,
		weight,
		pulse,
		sp02,
		t,
		sbp,
		dbp,
		progress_note_id,
		vital_sign_id
	} = body;
	if (!visit_id && !progress_note_id) return;
	if (visit_id && progress_note_id) return;
	if (bmi || rr || height || weight || pulse || sp02 || t || sbp || dbp) {
		if (visit_id && !progress_note_id) {
			await db
				.insert(vitalSign)
				.values({
					visit_id: +visit_id,
					bmi: +bmi > 0 ? +bmi : null,
					rr: rr ? +rr : null,
					height: height ? +height : null,
					weight: weight ? +weight : null,
					pulse: pulse ? +pulse : null,
					sp02: sp02 ? +sp02 : null,
					t: t ? +t : null,
					sbp: sbp ? +sbp : null,
					dbp: dbp ? +dbp : null
				})
				.catch((e) => {
					logError({ url, body: FormData, err: e });
				});
		}
		if (progress_note_id && !visit_id) {
			await db
				.insert(vitalSign)
				.values({
					progress_note_id: +progress_note_id,
					bmi: +bmi > 0 ? +bmi : null,
					rr: rr ? +rr : null,
					height: height ? +height : null,
					weight: weight ? +weight : null,
					pulse: pulse ? +pulse : null,
					sp02: sp02 ? +sp02 : null,
					t: t ? +t : null,
					sbp: sbp ? +sbp : null,
					dbp: dbp ? +dbp : null
				})
				.catch((e) => {
					logError({ url, body: FormData, err: e });
				});
		}
		if (vital_sign_id) {
			await db
				.update(vitalSign)
				.set({
					bmi: +bmi > 0 ? +bmi : null,
					rr: rr ? +rr : null,
					height: height ? +height : null,
					weight: weight ? +weight : null,
					pulse: pulse ? +pulse : null,
					sp02: sp02 ? +sp02 : null,
					t: t ? +t : null,
					sbp: sbp ? +sbp : null,
					dbp: dbp ? +dbp : null
				})
				.where(eq(vitalSign.id, Number(vital_sign_id)))
				.catch((e) => {
					logError({ url, body: FormData, err: e });
				});
		}
	}
}
