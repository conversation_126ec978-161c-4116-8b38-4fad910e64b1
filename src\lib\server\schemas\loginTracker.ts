import { datetime, int, mysqlTable, varchar } from 'drizzle-orm/mysql-core';

export const loginTracker = mysqlTable('login_tracker', {
	id: int().primaryKey().autoincrement(),
	datetime: datetime('log_datetime', { mode: 'string' }).notNull(),
	version: varchar({ length: 255 }).notNull(),
	username: varchar({ length: 255 }).notNull(),
	ip_address: varchar({ length: 255 }).notNull(),
	mac_address: varchar({ length: 255 }).notNull()
});
