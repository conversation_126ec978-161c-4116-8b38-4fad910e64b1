<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		action?: string;
		id?: number | undefined;
	}

	let { action = '', id = undefined }: Props = $props();
	let loading = $state(false);
</script>

<div class="modal fade" id="send_to_ipd" data-bs-backdrop="static">
	<Form
		method="post"
		fnSuccess={() => document.getElementById('close_send_to_ipd')?.click()}
		{action}
		bind:loading
		class="modal-dialog modal-dialog-centered modal-sm"
	>
		<input value={id} type="hidden" name="id" />
		<div class="modal-content">
			<div class="modal-header">
				<h3 class="modal-title">Send To IPD ?</h3>
			</div>
			<div class="modal-footer justify-content-between">
				<button id="close_send_to_ipd" type="button" class="btn btn-default" data-bs-dismiss="modal"
					><i class="fa-solid fa-rectangle-xmark"></i> No</button
				>
				<button type="submit" class="btn btn-info">
					{#if loading}
						<i class="fa-solid fa-share fa-spin"></i>
					{:else}
						<i class="fa-solid fa-share"></i>
					{/if} Yes Send</button
				>
			</div>
		</div>
	</Form>
</div>
