<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import SubUnit from '$lib/coms-form/SubUnit.svelte';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import { page } from '$app/state';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import { store } from '$lib/store/store.svelte';
	import GetBack from '$lib/coms/GetBack.svelte';
	import { browser } from '$app/environment';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}
	interface TSubUnit {
		id: number | string;
		qty_per_unit: number;
		unit_id: number;
		price: number | null;
	}
	let { form, data }: Props = $props();
	let { get_product, get_categories, get_currency, get_groups } = $derived(data);
	let category_id: number | null = $state(null);
	let subUnits: TSubUnit[] = $state([]);
	let group_id: number | null = $state(null);
	let loading = $state(false);
	let exspend_id = $derived(page.url.searchParams.get('exspend_id') || '');
	$effect(() => {
		if (get_product?.category_id) {
			category_id = get_product.category_id;
		}
		if (get_product?.group_id) {
			group_id = get_product.group_id;
		}
	});
	let unit_id: number | null = $state(null);
	$effect(() => {
		if (get_product?.unit_id) {
			unit_id = get_product?.unit_id;
		}
	});
	$effect(() => {
		if (get_product?.subUnit) {
			subUnits = get_product?.subUnit;
		}
	});
	let groups = $derived(get_groups?.filter((e) => e.category_id === category_id));
	let group = $derived(groups?.find((e) => e.id === group_id));
	let units = $derived(group?.unitsToGroups?.flatMap((e) => (e.unit ? [e.unit] : [])) || []);
	function pushSubUnit({ id, price, qty_per_unit, unit_id }: TSubUnit) {
		subUnits.push({
			id,
			price,
			qty_per_unit,
			unit_id
		});
	}
	function onclick(id: number | string) {
		if (confirm(locale.T('confirm_delete'))) {
			subUnits = subUnits.filter((e) => e.id !== id);
		}
	}
</script>

<div class="row">
	<div class="col-sm-6">
		<h2>
			<GetBack
				href={exspend_id ? `/product/purchase/create?exspend_id=${exspend_id}` : '/product'}
			/>
		</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('add')}
				</a>
			</li>
		</ol>
	</div>
</div>

<Form
	enctype="multipart/form-data"
	action={get_product?.id ? '?/update_product' : '?/create_product'}
	method="post"
	bind:loading
>
	<input type="hidden" name="exspend_id" value={exspend_id} />
	<div class="card bg-light">
		<div class="card-body">
			<div class="col-12 mb-2">
				<div>
					<input
						value={browser ? sessionStorage.getItem('from_url') : ''}
						type="hidden"
						name="from_url"
					/>
					<input value={get_product?.id} type="hidden" name="product_id" />
					<label for="name_product">{locale.T('product_name')}</label>
					<input
						value={get_product?.products ?? ''}
						name="name_product"
						type="text"
						class="form-control"
						id="name_product"
					/>

					{#if form?.name_product}
						<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
					{/if}
				</div>
			</div>
			<div class="row pb-3">
				<div class="col-6">
					<div>
						<label for="generic_name">{locale.T('generic_name')}</label>
						<input
							value={get_product?.generic_name ?? ''}
							name="generic_name"
							type="text"
							class="form-control"
							id="generic_name"
						/>
					</div>
				</div>
				<div class="col-6">
					<div>
						<label for="barcode">{locale.T('barcode')}</label>
						<input
							value={get_product?.barcode ?? ''}
							name="barcode"
							type="text"
							class="form-control"
							id="barcode"
						/>
					</div>
				</div>
			</div>

			<div class="row pb-3">
				<div class="col-6">
					<div>
						<label for="category_id">{locale.T('category')}</label>
						<div class="input-group">
							<SelectParam
								placeholder={locale.T('select')}
								bind:value={category_id}
								name="category_id"
								items={get_categories.map((e) => ({
									id: e.id,
									name: e.name
								}))}
							/>
							{#if form?.product_id}
								<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
							{/if}
						</div>
					</div>
				</div>
				<div class="col-6">
					<div>
						<label for="group_id">{locale.T('product_group')}</label>
						<div class="input-group">
							<SelectParam
								placeholder={locale.T('select')}
								bind:value={group_id}
								name="group_id"
								items={groups?.map((e) => ({
									id: e.id,
									name: e.name
								}))}
							/>
							{#if form?.product_id}
								<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
							{/if}
						</div>
					</div>
				</div>
			</div>

			<div class="row pb-3">
				<div class="col-3">
					<div>
						<label for="unit_id">{locale.T('unit')}</label>
						<div class="input-group">
							<SelectParam
								placeholder={locale.T('select')}
								bind:value={unit_id}
								name="unit_id"
								items={units?.map((e) => ({ id: e?.id, name: e?.unit }))}
							/>
						</div>
					</div>
				</div>

				<div class="col-3">
					<div>
						<label for="price">{locale.T('sales_price')}</label>
						<CurrencyInput
							name="price"
							amount={get_product?.price}
							symbol={get_currency?.currency}
						/>

						{#if form?.price}
							<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
						{/if}
					</div>
				</div>
				<div class="col-6">
					<div>
						<label for=""></label>
						<div class="input-group">
							<button
								onclick={() =>
									pushSubUnit({
										price: 0,
										qty_per_unit: 0,
										unit_id: unit_id!,
										id: Math.random().toString(36).substring(2, 9)
									})}
								aria-label="subunit"
								type="button"
								class=" form-control w-100">{locale.T('add_unit')}</button
							>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				{#if subUnits.length}
					<div class="text-hr pb-2 text-danger">បន្ថែមខ្នាតដើម្បីលក់ដុំ!</div>
				{/if}
				{#each subUnits || [] as i (i)}
					{#if i}
						<SubUnit
							data={{ get_currency, get_units: units || [] }}
							{form}
							{...i}
							main_unit_id={unit_id}
						/>
						<div class="col-3">
							<div>
								<label for=""></label>
								<div class="input-group">
									<button
										onclick={() => onclick(i.id)}
										aria-label="subunit"
										type="button"
										class="form-control text-danger">{locale.T('delete_unit')}</button
									>
								</div>
							</div>
						</div>
					{/if}
				{/each}
				<div class="col-12 pb-2">
					<div>
						<label for="picture">{locale.T('picture')}</label>
						<CropImage
							default_image={get_product?.uploads?.filename}
							name="file"
							related_id={get_product?.id}
							related_type_="product"
						/>
						<!-- <input accept="image/*" name="image" type="file" class="form-control" id="picture" /> -->
					</div>
				</div>
			</div>
		</div>
		<div class="card-footer text-end">
			<SubmitButton {loading} />
		</div>
	</div>
</Form>
