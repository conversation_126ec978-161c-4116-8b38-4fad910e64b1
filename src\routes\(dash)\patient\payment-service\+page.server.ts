import { db } from '$lib/server/db';
import { billing, paymentService, uploads, words } from '$lib/server/schemas';
import { and, eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import logError from '$lib/server/utils/logError';
import { fileHandle } from '$lib/server/upload';
import { fail } from '@sveltejs/kit';
export const load = (async ({ parent, url }) => {
	await parent();
	const id = url.searchParams.get('id') ?? '';
	const billing_id = url.searchParams.get('billing_id') ?? '';
	const get_billing = await db.query.billing.findFirst({
		where: eq(billing.id, +billing_id),
		with: {
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			visit: {
				with: {
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					progressNote: {
						with: {
							patient: {
								with: {
									commune: true,
									district: true,
									provice: true,
									village: true
								}
							}
						}
					}
				}
			},
			progressNote: {
				with: {
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					}
				}
			}
		}
	});
	const get_service_types = await db.query.serviceType.findMany({});
	const get_payment_service = await db.query.paymentService.findFirst({
		where: eq(paymentService.id, +id)
	});
	const get_words_payment_service = await db.query.words.findMany({
		where: eq(words.category, 'payment_service')
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'paymentService'), eq(uploads.related_id, +id))
	});
	const get_visit = get_billing?.visit;
	let patient_info;
	if (get_visit) {
		patient_info = {
			...get_visit?.patient,
			date_checkup: get_visit?.date_checkup
		};
	}
	if (get_visit?.progressNote) {
		patient_info = {
			...get_visit?.progressNote?.patient,
			date_checkup: get_visit?.progressNote?.date_checkup
		};
	}
	if (get_billing?.progressNote) {
		patient_info = {
			...get_billing?.progressNote?.patient,
			date_checkup: get_billing?.progressNote?.date_checkup ?? ''
		};
	}
	return {
		get_payment_service: {
			...get_payment_service,
			uploads: get_upload
		},
		get_service_types,
		get_words_payment_service,
		patient_info
	};
}) satisfies PageServerLoad;
export const actions: Actions = {
	update_payment_service: async ({ request, url }) => {
		const body = await request.formData();
		const { code, service_type_id, payment_service_id, billing_id } = Object.fromEntries(
			body
		) as Record<string, string>;
		if (!billing_id || !service_type_id) return fail(400, { errId: true });
		if (payment_service_id) {
			await db
				.update(paymentService)
				.set({
					service_type_id: service_type_id ? +service_type_id : null,
					code: code || '',
					status: 'debt'
				})
				.where(eq(paymentService.id, +payment_service_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			await db
				.insert(paymentService)
				.values({
					code: code || '',
					status: 'debt',
					service_type_id: +service_type_id,
					billing_id: +billing_id
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		await db
			.update(billing)
			.set({
				service_type_id: +service_type_id
			})
			.where(eq(billing.id, +billing_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await fileHandle.auto(body);
	}
};
