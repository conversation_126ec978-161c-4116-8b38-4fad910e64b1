<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_visit, get_currency, get_vaccine_groups } = $derived(data);
	let total_vaccine_service = $derived(
		data.get_visit?.billing?.charge.find((e) => e.charge_on === 'vaccine')?.price || 0
	);
	let loading = $state(false);
</script>

<fieldset disabled={get_visit?.billing?.status !== 'checking'}>
	<Form
		onchange={(e) => e.currentTarget.requestSubmit()}
		reset={false}
		bind:loading
		action="?/vaccine_service"
		method="post"
		class="row"
	>
		{#each get_vaccine_groups || [] as item (item.id)}
			{@const products = item.product}
			<div class="col-md-3 pb-2">
				<div class="card bg-light h-100">
					<div class="card-header fs-5 text-center">
						<span>{item?.name}</span>
					</div>
					<div class="card-body">
						{#each products || [] as iitem (iitem.id)}
							<div class="alert alert-primary py-1">
								<div class="form-check">
									<input
										name="product_id"
										class="form-check-input"
										type="checkbox"
										checked={get_visit?.vaccine.some((e) => e.product_id === iitem.id)}
										id={iitem.id.toString()}
										value={iitem.id}
									/>
									<label for={iitem.id.toString()} class="custom-control-label"
										>{iitem.products}
									</label>
									<Currency amount={iitem.price} symbol={get_currency?.currency} />
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
		{/each}
		<div class="card-footer row bg-light p-2 sticky-bottom">
			<div class="col text-end">
				<button type="button" class="btn btn-warning"
					>{locale.T('total')}
					<Currency
						class="fs-6"
						symbol={get_currency?.currency}
						amount={total_vaccine_service}
					/></button
				>
			</div>
		</div>
	</Form>
</fieldset>
