import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { uploads, product, staff, staffToDemartment, staffToRole } from '$lib/server/schemas';
import logError from '$lib/server/utils/logError';
import { and, eq } from 'drizzle-orm';
export const load: PageServerLoad = async ({ parent, url, locals }) => {
	await parent();
	const staff_id = url.searchParams.get('staff_id') ?? '';
	const get_staff = await db.query.staff.findFirst({
		where: eq(staff.id, +staff_id),
		with: {
			user: true,
			commune: true,
			district: true,
			title: true,
			village: true,
			provice: true,
			staffToRole: {
				with: {
					role: true
				}
			},
			staffToDemartment: {
				with: {
					department: true
				}
			}
		}
	});
	const get_roles = await db.query.role.findMany({});
	const get_products_department = await db.query.product.findMany({
		where: eq(product.category_id, 11)
	});
	const get_titles = await db.query.title.findMany({});
	const get_designations = await db.query.designation.findMany({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staff'), eq(uploads.related_id, +staff_id))
	});
	const get_upload_sign = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staffSign'), eq(uploads.related_id, +staff_id))
	});
	return {
		get_titles,
		get_staff: {
			...get_staff,
			uploads: get_upload,
			sign: get_upload_sign
		},
		get_roles,
		get_products_department,
		locals: locals,
		get_designations
	};
};

export const actions: Actions = {
	create_staff: async ({ request, url }) => {
		const body = await request.formData();
		const { staff_id, designation_id, title_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		const validErr = {
			designation_id: false,
			title_id: false
		};
		const role_id = body.getAll('role_id');
		const department_id = body.getAll('department_id');
		if (!title_id.trim()) validErr.title_id = true;
		if (!designation_id.trim()) validErr.designation_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await db
			.update(staff)
			.set({
				title_id: +title_id,
				designation_id: +designation_id
			})
			.where(eq(staff.id, +staff_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});

		const get_staff = await db.query.staff.findFirst({
			where: eq(staff.id, +staff_id),
			with: {
				staffToRole: true,
				staffToDemartment: true
			}
		});
		//emplement staff to role
		for (const e of role_id || []) {
			if (!get_staff?.staffToRole.some((ee) => ee.role_id === +e)) {
				await db.insert(staffToRole).values({
					staff_id: +staff_id,
					role_id: +e
				});
			}
		}
		for (const e of get_staff?.staffToRole || []) {
			if (!role_id.some((ee) => +ee === e.role_id)) {
				if (e.role_id && e.staff_id) {
					await db
						.delete(staffToRole)
						.where(and(eq(staffToRole.staff_id, +staff_id), eq(staffToRole.role_id, +e.role_id)));
				}
			}
		}
		// emplement staff to departments
		for (const e of department_id || []) {
			if (!get_staff?.staffToDemartment.some((ee) => ee.department_id === +e)) {
				await db.insert(staffToDemartment).values({
					staff_id: +staff_id,
					department_id: +e
				});
			}
		}
		for (const e of get_staff?.staffToDemartment || []) {
			if (!department_id?.some((ee) => +ee === e.department_id)) {
				if (e?.department_id && e.staff_id) {
					await db
						.delete(staffToDemartment)
						.where(
							and(
								eq(staffToDemartment.staff_id, +staff_id),
								eq(staffToDemartment.department_id, +e.department_id)
							)
						);
				}
			}
		}
	}
};
