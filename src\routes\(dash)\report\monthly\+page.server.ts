
import { db } from '$lib/server/db';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { and, between, desc, gt, isNull, ne } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { billing, exspend, payment, payroll } from '$lib/server/schemas';

export const load = (async ({ url }) => {
  const year = url.searchParams.get('year') || YYYYMMDD_Format.date(new Date()).slice(0, 4);
  const get_currency = await db.query.currency.findFirst()
  const start = year.split('-')[0].concat('-01-01');
  const end = year.split('-')[0].concat('-12-31');
  const get_billings = await db.query.billing.findMany({
    where: and(
      ne(billing.status, 'checking'),
      ne(billing.status, 'paying'),
      gt(billing.amount, 0),
      between(billing.created_at, start, end)
    ),
    orderBy: desc(billing.created_at)
  });
  const get_exspends = await db.query.exspend.findMany({
    where: between(exspend.datetime_invoice, start, end)
  })
  const get_payrolls = await db.query.payroll.findMany({
    where: between(payroll.payment_date, start, end)
  })
  const get_payments = await db.query.payment.findMany({
    where: and(
      between(payment.datetime, start, end),
      isNull(payment.exspend_id)
    ),
  })
  const months = [{ name: 'january', num: '01' }, { name: 'february', num: '02' }, { name: 'march', num: '03' }, { name: 'april', num: '04' }, { name: 'may', num: '05' }, { name: 'june', num: '06' }, { name: 'july', num: '07' }, { name: 'august', num: '08' }, { name: 'september', num: '09' }, { name: 'october', num: '10' }, { name: 'november', num: '11' }, { name: 'december', num: '12' }];
  return {
    get_currency,
    get_billings,
    get_exspends,
    get_payrolls,
    get_payments,
    months,
    year

  };
}) satisfies PageServerLoad;