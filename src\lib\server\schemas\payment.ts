import { datetime, decimal, int, mysqlTable, text, varchar } from 'drizzle-orm/mysql-core';
import { billing } from './billing';
import { relations } from 'drizzle-orm';
import { staff } from './staff';
import { exspend } from './supplier';

export const paymentType = mysqlTable('payment_type', {
	id: int().primaryKey().autoincrement(),
	by: varchar({ length: 255 })
});

export const payment = mysqlTable('payment', {
	id: int().primaryKey().autoincrement(),
	value: decimal({ precision: 18, scale: 2 }).$type<number>(),
	payment_type_id: int().references(() => paymentType.id, {
		onDelete: 'restrict'
	}),
	billing_id: int().references(() => billing.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	datetime: datetime({ mode: 'string' }),
	note: text('note'),
	staff_id: int().references(() => staff.id),
	exspend_id: int().references(() => exspend.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	})
});

export const paymentRelations = relations(payment, ({ one }) => ({
	paymentType: one(paymentType, {
		fields: [payment.payment_type_id],
		references: [paymentType.id]
	}),
	billing: one(billing, {
		fields: [payment.billing_id],
		references: [billing.id]
	}),
	staff: one(staff, {
		fields: [payment.staff_id],
		references: [staff.id]
	}),
	exspend: one(exspend, {
		fields: [payment.exspend_id],
		references: [exspend.id]
	})
}));
export const paymentService = mysqlTable('payment_service', {
	id: int().primaryKey().autoincrement(),
	code: varchar({ length: 255 }),
	reference: varchar({ length: 255 }),
	status: varchar({ length: 255 }).$type<'paid' | 'debt'>().default('paid'),
	datetime_paid: datetime({ mode: 'string' }),
	service_type_id: int().references(() => serviceType.id),
	billing_id: int()
		.references(() => billing.id, {
			onDelete: 'cascade',
			onUpdate: 'cascade'
		})
		.unique()
});

export const serviceType = mysqlTable('service_type', {
	id: int().primaryKey().autoincrement(),
	by: varchar({ length: 255 })
});

export const paymentServiceRelations = relations(paymentService, ({ one }) => ({
	serviceType: one(serviceType, {
		fields: [paymentService.service_type_id],
		references: [serviceType.id]
	}),
	billing: one(billing, {
		fields: [paymentService.billing_id],
		references: [billing.id]
	})
}));
