<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import type { PageServerData } from './$types';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_laboratory_group, get_visit, get_currency } = $derived(data);
	let total_laboratory = $derived(
		get_visit?.billing?.charge?.find((e) => e.charge_on === 'laboratory')?.price || 0
	);
	let loading = $state(false);
</script>

<hr />
<div class="row pb-2">
	<div class="col">
		<a
			target="_blank"
			aria-label="nersing_process"
			href="/print/{get_visit?.id}/laboratory-req"
			class="btn btn-success btn-sm float-end"
			><i class="fa-solid fa-print"></i>
		</a>
	</div>
</div>
<fieldset disabled={get_visit?.billing?.status !== 'checking'}>
	<Form
		onchange={(e) => e.currentTarget.requestSubmit()}
		reset={false}
		bind:loading
		action="?/create_laboratory_request"
		method="post"
		class="row"
	>
		{#each get_laboratory_group as item (item.id)}
			<div class="col-md-3 pb-2">
				<div class="card bg-light h-100">
					<div class="card-header fs-5 text-center">
						<span>{item.laboratory_group}</span>
					</div>
					<div class="card-body">
						{#each item.product as iitem (iitem.id)}
							<div class="alert alert-primary py-1">
								<div class="form-check">
									<input
										name="product_id"
										class="form-check-input"
										type="checkbox"
										checked={get_visit?.laboratoryRequest.some((e) => e.product_id === iitem.id)}
										id={iitem.id.toString()}
										value={iitem.id}
									/>
									<label for={iitem.id.toString()} class="custom-control-label"
										>{iitem.products}
									</label>
									<Currency symbol={get_currency?.currency} amount={iitem.price} />
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
		{/each}
	</Form>

	<div class="card-footer row p-2 bg-light sticky-bottom">
		<div class="col text-end">
			<button type="button" class="btn btn-warning"
				>Total Laboratory <Currency
					class="fs-6"
					symbol={get_currency?.currency}
					amount={total_laboratory}
				/></button
			>
		</div>
	</div>
</fieldset>
