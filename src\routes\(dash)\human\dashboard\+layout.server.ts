import type { LayoutServerLoad } from './$types';
import { db } from '$lib/server/db';
export const load = (async ({ url }) => {
    const get_salaries = await db.query.salary.findMany({
    });
    const get_currency = await db.query.currency.findFirst({});
    const get_staffs = await db.query.staff.findMany({
        with: {
            user: true,
        },
    });
    return {
        get_staffs, get_salaries, get_currency
    };
}) satisfies LayoutServerLoad;