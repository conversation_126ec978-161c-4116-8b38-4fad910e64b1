<script lang="ts">
	import { dobToAge } from '$lib/helper';
	interface Props {
		dob: Date | string | null | undefined;
		date: Date | string | null;
		gender: string | null | undefined;
	}
	let { dob, gender, date }: Props = $props();
</script>

<span
	class="btn btn-sm my-0 py-0 btn-outline-light"
	style={gender?.toLowerCase().includes('female')
		? 'background-color: orchid;color: white'
		: 'background-color: #08a1ff;color: white;'}
>
	{dobToAge(dob, date)}
	{#if gender?.toLowerCase().includes('female')}
		<i class="fa-solid fa-venus"></i>
	{:else if gender?.toLowerCase().includes('male')}
		<i class="fa-solid fa-mars"></i>
	{:else}
		<i class="fa-solid fa-genderless"></i>
	{/if}
</span>
