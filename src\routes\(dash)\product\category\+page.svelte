<script lang="ts">
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import Form from '$lib/coms-form/Form.svelte';
	import { store } from '$lib/store/store.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import SelectMultiiple from '$lib/coms-form/SelectMultiiple.svelte';
	import CreateUnit from '$lib/coms-cu/CreateUnit.svelte';
	interface Props {
		data: PageServerData;
	}
	let group_id: number | undefined = $state(undefined);
	let unit_id: number | undefined = $state(undefined);
	let loading = $state(false);
	let { data }: Props = $props();
	let { get_categories, get_units } = $derived(data);
	let category_id: number | undefined = $state(undefined);
	let select_category_id: number | string = $state('');
	let select_category = $derived(
		select_category_id ? get_categories?.filter((e) => e.id === select_category_id) : get_categories
	);
	let find_category = $derived(get_categories.find((e) => e.id === category_id));
	let find_group = $derived(find_category?.group.find((e) => e.id === group_id));
	let find_unit = $derived(get_units?.find((e) => e.id === unit_id));
	let delete_modal: string = $state('unit');
	let units = $derived(find_group?.unitsToGroups.map((e) => e.unit));
</script>

<CreateUnit {get_units} />

<div class="modal fade" id="create_unit" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<Form
			action="?/add_unit_to_group"
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => {
				document.getElementById('close_create_unit')?.click();
			}}
		>
			<input type="hidden" name="group_id" value={group_id} />
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('unit')}</h4>
				<button
					onclick={() => {
						group_id = undefined;
						category_id = undefined;
					}}
					id="close_create_unit"
					type="button"
					class="btn-close"
					data-bs-toggle="modal"
					data-bs-target="#create-unit"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body pt-0">
				<div class="card-body">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3 mt-3">
								<!-- {#if form?.descriptionErr}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if} -->
								<SelectMultiiple
									name="unit_id"
									placeholder={locale.T('select')}
									items={get_units?.map((e) => ({ id: e?.id, name: e?.unit }))}
									value={units?.map((e) => ({ id: e?.id, name: e?.unit }))}
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
<div class="modal fade" id="create_group" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<Form
			action={find_group?.id ? '?/update_group' : '?/create_group'}
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => {
				group_id = undefined;
				document.getElementById('close_create_group')?.click();
			}}
		>
			<input type="hidden" name="category_id" value={category_id} />
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('product_group')}</h4>
				<button
					onclick={() => (group_id = undefined)}
					id="close_create_group"
					type="button"
					class="btn-close"
					data-bs-toggle="modal"
					data-bs-target="#create-group"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body pt-0">
				<div class="card-body">
					<input type="hidden" name="id" value={find_group?.id} />
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<label for="group_">{locale.T('name')}</label>
								<input
									value={find_group?.name ?? ''}
									name="group_"
									type="text"
									class="form-control"
									id="group_"
								/>
								<!-- {#if form?.descriptionErr}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if} -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>

<DeleteModal id={find_group?.id} delete_modal="modal_delete_group" action="?/delete_group" />

<DeleteModal id={find_unit?.id} delete_modal="modal_delete_unit" action="?/delete_unit" />

<div class="modal fade" id="create_category" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<Form
			action={find_category?.id ? '?/update_category' : '?/create_category'}
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => {
				category_id = undefined;
				document.getElementById('close_create_category')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('category')}</h4>
				<button
					onclick={() => (category_id = undefined)}
					id="close_create_category"
					type="button"
					class="btn-close"
					data-bs-toggle="modal"
					data-bs-target="#create-group"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body pt-0">
				<div class="card-body">
					<input type="hidden" name="category_id" value={find_category?.id} />
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<label for="category_">{locale.T('name')}</label>
								<input
									value={find_category?.name ?? ''}
									name="category_"
									type="text"
									class="form-control"
									id="category_"
								/>
								<!-- {#if form?.descriptionErr}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if} -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('list_category')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/category" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('category')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="row">
	<div class="col-12">
		<div class="card">
			<div class="card-header">
				<!-- <h3 class="card-title">Fixed Header Table</h3> -->
				<div class="row gap-0">
					<div class="col-3">
						<SelectParam
							bind:value={select_category_id}
							placeholder={locale.T('category')}
							items={get_categories?.map((e) => ({ id: e.id, name: e.name }))}
						/>
					</div>
					<div class="col text-end">
						<button
							onclick={() => (category_id = 0)}
							type="button"
							class="btn btn-success"
							data-bs-toggle="modal"
							data-bs-target="#create_new_unit"
							><i class="fa-solid fa-bars-staggered"></i>
							{locale.T('list_units')}
						</button>
					</div>
				</div>
			</div>
			<div class="card-body table-responsive bg-light">
				<table class="table table-sm table-bordered">
					<tbody>
						{#each select_category as item, index (item.id)}
							<tr class="border border-3">
								<td
									style="width: 20%;"
									class="position-relative text-bg-primary ps-4 justify-content-center"
								>
									<div>
										<button class="btn btn-primary btn-sm fs-3">{item.name} </button>
										<button
											aria-label="createward"
											onclick={() => {
												category_id = undefined;
												category_id = item.id;
											}}
											data-bs-toggle="modal"
											data-bs-target="#create_group"
											type="button"
											class="btn btn-primary btn-sm fs-3 float-end"
										>
											{locale.T('group')} <i class="fa-solid fa-plus"></i>
										</button>
									</div>
								</td>
								<td style="width: 80%;" class="text-start p-0 m-0">
									{#each item?.group || [] as item_1}
										{@const unit_to_group = item_1?.unitsToGroups.map((e) => e.unit)}
										<table class="table table-sm my-2">
											<thead>
												<tr>
													<td
														style="width: 50%;"
														class="justify-content-center align-content-center text-bg-success text-start fs-5 m-0"
													>
														&nbsp;&nbsp;
														{item_1.name}
														<button
															aria-label="update"
															onclick={() => {
																category_id = undefined;
																category_id = item_1?.category_id || 0;
																group_id = undefined;
																group_id = item_1.id;
															}}
															data-bs-toggle="modal"
															data-bs-target="#create_unit"
															type="button"
															class="btn btn-link text-white float-end py-0 my-0"
														>
															{locale.T('unit')} <i class="fa-solid fa-plus"></i>
														</button>
														<button
															aria-label="update"
															onclick={() => {
																category_id = undefined;
																category_id = item_1?.category_id || 0;
																group_id = undefined;
																group_id = item_1.id;
															}}
															data-bs-toggle="modal"
															data-bs-target="#create_group"
															type="button"
															class="btn btn-link text-white float-end py-0 my-0"
														>
															<i class="fa-solid fa-file-pen"></i>
														</button>
														<button
															aria-label="delete"
															onclick={() => {
																delete_modal = 'group';
																category_id = undefined;
																category_id = item_1?.category_id || 0;
																group_id = undefined;
																group_id = item_1.id;
															}}
															data-bs-toggle="modal"
															data-bs-target="#modal_delete_group"
															type="button"
															class="btn btn-danger rounded-0 float-end py-0 my-0"
														>
															<i class="fa-regular fa-trash-can"></i>
														</button>
													</td>
													<td style="width: 50%;">
														{#each unit_to_group as item_2}
															<div class="btn-group me-1 my-1">
																<button type="button" class="btn rounded-0 btn-secondary active">
																	{item_2?.unit ?? ''}</button
																>
															</div>
														{/each}
													</td>
												</tr>
											</thead>
										</table>
									{/each}
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
