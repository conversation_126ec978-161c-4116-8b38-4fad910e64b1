<script lang="ts">
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { TSubUnit, TUnit } from '$lib/type';
	interface ICurrency {
		id: number;
		currency: string;
		currency_rate: number;
		exchang_to: string;
		exchang_rate: number;
	}
	interface SubUnit extends TSubUnit {
		unit: TUnit;
	}
	interface Props {
		product_order_name: string | null;
		charge_id: number;
		product_order_id: number;
		discount: string | null;
		qty: number | null;
		product_order_price: number | null;
		product_price: number | null;
		currency: ICurrency | undefined;
		unit: TUnit | null;
		unit_id: number | null;
		sub_units: SubUnit[] | null;
	}

	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
	let {
		product_order_name,
		product_order_price,
		product_price,
		charge_id,
		product_order_id,
		discount,
		qty,
		unit,
		currency,
		sub_units,
		unit_id
	}: Props = $props();
	let loading = $state(false);
	let price_ = $state(product_order_price);
	function handleUnitChange() {
		const found_unit = sub_units?.find((e) => e.unit_id === unit_id);
		if (found_unit) {
			price_ = found_unit.price;
		} else {
			price_ = product_price;
		}
	}
</script>

<!-- Button trigger modal -->
<button
	type="button"
	class="btn btn-link text-decoration-none fs-6 py-0 my-0 text-wrap text-start"
	data-bs-toggle="modal"
	data-bs-target="#{id}"
>
	{product_order_name ?? ''}
</button>

<!-- Modal -->
<div
	class="modal fade"
	{id}
	tabindex="-1"
	aria-labelledby={id.concat('exampleModalLabel')}
	aria-hidden="true"
>
	<Form
		class="modal-dialog modal-md"
		reset={false}
		bind:loading
		method="post"
		fnSuccess={() => document.getElementById(id.concat('dismiss'))?.click()}
		action="?/discount_product_order"
	>
		<div class="modal-content rounded-3 shadow">
			<div class="modal-header text-bg-warning">
				<h1 class="modal-title fs-6">{product_order_name ?? ''}</h1>
				<button id={id.concat('dismiss')} data-bs-dismiss="modal" type="button" class="d-none">
					x
				</button>
			</div>
			<div class="modal-body">
				<input type="hidden" name="charge_id" value={charge_id} />
				<input type="hidden" name="product_order_id" value={product_order_id} />
				<div class="input-group pb-2">
					<label style="width: 45%;" class="input-group-text" for=""
						>{locale.T('discount_value_or_percent')}</label
					>
					<input
						autocomplete="off"
						value={discount}
						class="form-control"
						type="text"
						pattern="[0-9]+%?"
						name="disc"
						oninput={(e) => {
							const value = e.currentTarget.value;
							if (isNaN(+value)) {
								if (!value.includes('%')) {
									e.currentTarget.value = '';
									alert(locale.T('none_data'));
								}
								if (isNaN(Number(value.replace('%', '')))) {
									alert(locale.T('none_data'));
									e.currentTarget.value = '';
								}
								if (Number(value.replace('%', '')) > 100) {
									e.currentTarget.value = '';
									alert(locale.T('none_data'));
								}
							} else {
								if (Number(value) > Number(price_)) {
									alert(locale.T('none_data'));
									e.currentTarget.value = '';
								}
							}
						}}
					/>
				</div>
				<div class="input-group mb-2">
					<label style="width: 45%;" class="input-group-text" for="qty">{locale.T('qty')}</label>
					<input
						min="1"
						autocomplete="off"
						value={qty}
						class="form-control"
						type="number"
						name="qty"
						oninput={(e) => {
							if (Number(e.currentTarget.value) <= 0 || isNaN(Number(e.currentTarget.value))) {
								alert(locale.T('none_data'));
								e.currentTarget.value = '1';
							}
						}}
					/>
					<select
						onchange={handleUnitChange}
						bind:value={unit_id}
						name="unit_id"
						id="unit_id"
						class="form-control"
					>
						<option value={unit?.id}>{unit?.unit ?? ''}</option>
						{#each sub_units || [] as item}
							<option value={item.unit_id}>{item.unit.unit ?? ''}</option>
						{/each}
					</select>
				</div>
				<CurrencyInput
					style="width: 45%;"
					class="input-group mb-1"
					bind:amount={price_}
					symbol={currency?.currency}
					name="price"
				/>

				<!-- <div class="input-group">
						<label class="input-group-text" for="price">{locale.T('price')}</label>
						<input
							autocomplete="off"
							value={price}
							class="form-control"
							type="number"
							step="any"
							name="price"
						/>
					</div> -->
			</div>
			<div class="modal-footer flex-nowrap p-0">
				<button
					disabled={loading}
					id="close_delete_modal"
					type="button"
					class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
					data-bs-dismiss="modal">{locale.T('no')}</button
				>
				<button
					disabled={loading}
					type="submit"
					class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
				>
					<strong>{locale.T('save')}</strong>
				</button>
			</div>
		</div>
	</Form>
</div>
