import { db } from '$lib/server/db';
import {
	operationProtocol,
	product,
	category,
	progressNote,
	service,
	visit,
	billing
} from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { eq, like } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';
import { billingService, checkout, setChargePrice, updateProductOrder } from '$lib/server/models';
export const load = (async ({ params }) => {
	const { progress_note_id } = params;

	const get_category = await db.query.category.findFirst({
		where: like(category.name, 'Service')
	});
	const get_product_as_service = await db.query.product.findMany({
		where: eq(product.category_id, get_category?.id || 0)
	});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +progress_note_id),
		with: {
			billing: true,
			activeDepartment: {
				with: {
					department: true
				}
			},
			visit: {
				where: eq(visit.checkin_type, 'SERVICE'),
				with: {
					billing: {
						with: {
							payment: true,
							charge: {
								with: {
									billing: true,
									productOrder: {
										with: {
											product: true
										}
									}
								}
							}
						}
					}
				}
			},
			service: {
				with: {
					product: true,
					operationProtocol: true
				}
			}
		}
	});
	const visit_for_service = get_progress_note?.visit.filter((e) => e.checkin_type === 'SERVICE');
	const billing_for_service = visit_for_service?.flatMap((e) => {
		return e.billing?.charge?.find((e) => e.charge_on === 'service');
	});
	const get_currency = await db.query.currency.findFirst({});
	return {
		get_product_as_service,
		get_progress_note: get_progress_note,
		get_currency: get_currency,
		billing_for_service
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_service_operation: async ({ request, params, url }) => {
		const { progress_note_id } = params;
		const body = await request.formData();
		const { product_id } = Object.fromEntries(body) as Record<string, string>;
		if (!product_id) return fail(400, { errId: true });
		const get_product = await db.query.product.findFirst({ where: eq(product.id, +product_id) });
		const get_progress_note = await db.query.progressNote.findFirst({
			where: eq(progressNote.id, +progress_note_id),
			with: {
				visit: {
					where: eq(visit.checkin_type, 'SERVICE'),
					with: {
						billing: {
							with: {
								payment: true,
								charge: {
									with: {
										productOrder: true
									}
								}
							}
						}
					}
				},
				service: true
			}
		});
		const visit_for_service = get_progress_note?.visit.find((e) => e.checkin_type === 'SERVICE');
		if (get_progress_note?.service.find((e) => e.product_id === +product_id)) {
			return fail(400, { errId: true });
		}
		const charge_on_service = visit_for_service?.billing?.charge.find(
			(e) => e.charge_on === 'service'
		);
		if (charge_on_service && get_product) {
			await db.insert(service).values({
				product_id: get_product?.id,
				progress_note_id: get_progress_note!.id
			});
			await billingService({
				progress_id: +progress_note_id,
				product_id: get_product?.id,
				patient_id: Number(get_progress_note?.patient_id),
				body: body,
				url: url
			});
		}
	},
	delete_service: async ({ request, url }) => {
		const body = await request.formData();
		const { id, billing_id } = Object.fromEntries(body) as Record<string, string>;
		if (!id || !billing_id) return fail(400, { errId: true });
		const get_billing = await db.query.billing.findFirst({
			where: eq(billing.id, +billing_id)
		});
		if (get_billing?.visit_id) {
			await db
				.delete(visit)
				.where(eq(visit.id, +get_billing?.visit_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
			await db
				.delete(service)
				.where(eq(service.id, +id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	create_protocol: async ({ request, url }) => {
		const body = await request.formData();
		const {
			surgeon,
			assistant_surgeon,
			anesthetist,
			assistant_anesthetist,
			scrub_nurse,
			cirulating_nurse_block,
			midwife,
			date,
			start_time,
			finish_time,
			pre_diagnosis,
			post_diagnosis,
			type_anesthesia,
			opertive_technique,
			blood_less,
			notes,
			service_id
		} = Object.fromEntries(body) as Record<string, string>;
		const get_operation_protocol = await db.query.operationProtocol.findFirst({
			where: eq(operationProtocol.service_id, +service_id)
		});

		if (get_operation_protocol) {
			await db
				.update(operationProtocol)
				.set({
					anesthetist: anesthetist,
					assistant_anesthetist: assistant_anesthetist,
					assistant_surgeon: assistant_surgeon,
					blood_less: blood_less,
					cirulating_nurse_block: cirulating_nurse_block,
					date: date,
					finish_time: finish_time,
					midwife: midwife,
					notes: notes,
					opertive_technique: opertive_technique,
					post_diagnosis: post_diagnosis,
					pre_diagnosis: pre_diagnosis,
					scrub_nurse: scrub_nurse,
					start_time: start_time,
					surgeon: surgeon,
					type_anesthesia: type_anesthesia
				})
				.where(eq(operationProtocol.service_id, +service_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			await db
				.insert(operationProtocol)
				.values({
					anesthetist: anesthetist,
					assistant_anesthetist: assistant_anesthetist,
					assistant_surgeon: assistant_surgeon,
					blood_less: blood_less,
					cirulating_nurse_block: cirulating_nurse_block,
					date: date,
					finish_time: finish_time,
					midwife: midwife,
					notes: notes,
					opertive_technique: opertive_technique,
					post_diagnosis: post_diagnosis,
					pre_diagnosis: pre_diagnosis,
					scrub_nurse: scrub_nurse,
					service_id: +service_id,
					start_time: start_time,
					surgeon: surgeon,
					type_anesthesia: type_anesthesia
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	update_total_service: async ({ request, params, url }) => {
		const { progress_note_id } = params;
		const body = await request.formData();
		const { total_service } = Object.fromEntries(body) as Record<string, string>;
		const get_progress_note = await db.query.progressNote.findFirst({
			where: eq(progressNote.id, +progress_note_id),
			with: {
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				},
				service: true
			}
		});
		const charge_on_service = get_progress_note?.billing?.charge.find(
			(e) => e.charge_on === 'service'
		);
		if (charge_on_service) {
			await setChargePrice(charge_on_service.id, +total_service);
		}
	},
	set_price_service: async ({ request, params, url }) => {
		const { progress_note_id } = params;
		const body = await request.formData();
		const { product_id, price } = Object.fromEntries(body) as Record<string, string>;
		const get_progress_note = await db.query.progressNote.findFirst({
			where: eq(progressNote.id, +progress_note_id),
			with: {
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				},
				service: true
			}
		});
		const charge_on_service = get_progress_note?.billing?.charge.find(
			(e) => e.charge_on === 'service'
		);
		const find_product_order = charge_on_service?.productOrder.find(
			(e) => e.product_id === +product_id
		);
		if (find_product_order) {
			await updateProductOrder({
				disc: '',
				price: +price,
				product_order_id: find_product_order.id,
				qty: 1,
				body: body,
				url: url
			});
		}
	},
	discount_product_order: async (e) => {
		await checkout.discount_product_order(e);
	}
};
