<script lang="ts">
	import type { PageServerData } from './$types';
	import CreateVitalSign from '$lib/coms-cu/CreateVitalSign.svelte';
	import Athtml from '$lib/coms/Athtml.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Examinations from '$lib/coms-form/Examinations.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_exams, get_visit, get_words, get_physicals } = $derived(data);
	let mean_arterial_pressure = $derived(
		(1 / 3) * Number(get_visit?.vitalSign?.sbp) + (2 / 3) * Number(get_visit?.vitalSign?.dbp)
	);
</script>

<CreateVitalSign
	data={{
		get_visit: get_visit
	}}
/>
<!-- VitalSign  -->

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col fs-5">
				<span># Vital Sign</span>
			</div>
			<div class="col-auto">
				<button
					type="button"
					class="btn btn-success btn-sm"
					data-bs-toggle="modal"
					data-bs-target="#create_vital_sign"
					><i class="fa-solid fa-square-plus"></i>
					Vital Sign
				</button>
			</div>
		</div>
	</div>
	<div class="card-body table-responsive p-0">
		<table class="table p-0 m-0 text-nowrap table-light">
			<thead class="table-active">
				<tr>
					<th class="text-center">BP(mmHg)</th>
					<th class="text-center">MAP</th>
					<th class="text-center">Pulse (min)</th>
					<th class="text-center">Temperature <sup>o</sup>C </th>
					<th class="text-center">RR (min)</th>
					<th class="text-center">SpO2 (%)</th>
					<th class="text-center">Height (cm)</th>
					<th class="text-center">Weight (kg)</th>
					<th class="text-center">BMI</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td class="text-center"
						>{get_visit?.vitalSign?.sbp?.toFixed(0).concat(' /') ?? ''}
						{get_visit?.vitalSign?.dbp?.toFixed(0).concat(' mmHg') ?? ''}
					</td>
					<td class="text-center"
						>{mean_arterial_pressure ? mean_arterial_pressure?.toFixed(0).concat(' mmHg') : ''}</td
					>
					<td class="text-center"
						>{get_visit?.vitalSign?.pulse?.toFixed(0).concat(' /min') ?? ''}</td
					>
					<td class="text-center"
						><Athtml html={get_visit?.vitalSign?.t?.toFixed(1).concat(' &deg;C') ?? ''} /></td
					>
					<td class="text-center">{get_visit?.vitalSign?.rr?.toFixed(0).concat(' /min') ?? ''}</td>
					<td class="text-center">{get_visit?.vitalSign?.sp02?.toFixed(0).concat(' %') ?? ''}</td>
					<td class="text-center">{get_visit?.vitalSign?.height?.toFixed(0).concat(' cm') ?? ''}</td
					>
					<td class="text-center">{get_visit?.vitalSign?.weight?.toFixed(0).concat(' kg') ?? ''}</td
					>
					<td class="text-center">{get_visit?.vitalSign?.bmi?.toFixed(1).concat(' kg/m2') ?? ''}</td
					>
				</tr>
			</tbody>
		</table>
	</div>
</div>

<!-- physical  -->
<br />
<div class="card bg-light-subtle">
	<div class="card-header fs-5">
		<span># {locale.T('physical_exam')}</span>

		<!-- Default dropleft button -->
	</div>

	<div class="card-body">
		<Examinations data={{ get_exams, get_words, get_visit, get_physicals }} />
	</div>
</div>
