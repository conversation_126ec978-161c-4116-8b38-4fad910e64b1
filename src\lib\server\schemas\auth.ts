import { relations } from 'drizzle-orm';
import { int, mysqlTable, text, timestamp, varchar } from 'drizzle-orm/mysql-core';
import { staff } from './staff';
export const user = mysqlTable('user', {
	id: varchar({ length: 255 }).primaryKey(),
	username: varchar({ length: 50 }).notNull().unique(),
	password_hash: text(),
	staff_id: int().references(() => staff.id, { onDelete: 'cascade', onUpdate: 'cascade' })
});
export const session = mysqlTable('session', {
	id: varchar({ length: 255 }).primaryKey(),
	user_id: varchar({ length: 255 })
		.notNull()
		.references(() => user.id, { onDelete: 'cascade' }),
	expires_at: timestamp().notNull()
});
export const userRelations = relations(user, ({ one }) => ({
	staff: one(staff, {
		fields: [user.staff_id],
		references: [staff.id]
	})
}));
export type Session = typeof session.$inferSelect;

export type User = typeof user.$inferSelect;
