<script lang="ts">
	import InputDocument from '$lib/coms-form/InputDocument.svelte';
	import { khmerDate } from '$lib/helper';
	import type { TAddress, TDocumentSetting, TFields } from '$lib/type';
	import KhDateInput from '$lib/coms-document/KhDateInput.svelte';
	import Header from '$lib/coms-document/Header.svelte';
	interface Prop {
		occupation_list: string[];
		p_name: string;
		p_nation: string;
		p_contact: string;
		p_date_checkup: string;
		p_department: string;
		fields: TFields[];
		address?: TAddress;
		title_khm: string;
		get_document_setting?: TDocumentSetting;
		title_eng: string;
		logo: string;
		nations_list: string[];
	}
	let {
		p_name,
		p_nation,
		occupation_list,
		get_document_setting,
		address,
		fields,
		p_contact,
		p_date_checkup,
		p_department,
		title_eng,
		title_khm,
		nations_list,
		logo
	}: Prop = $props();
	let default_address = $derived(
		`${address?.village?.type ?? ''} ${address?.village?.name_khmer ?? ''} ${address?.commune?.type ?? ''} ${address?.commune?.name_khmer ?? ''} ${address?.district?.type ?? ''} ${address?.district?.name_khmer ?? ''} ${address?.provice?.type ?? ''} ${address?.provice?.name_khmer ?? ''}`
	);
	let name = $derived(fields.find((e) => e.name === 'name')?.result ?? '');
	let gender = $derived(fields.find((e) => e.name === 'gender')?.result ?? '');
	let age = $derived(fields.find((e) => e.name === 'age')?.result ?? '');
	let address_ = $derived(
		fields.find((e) => e.name === 'address')?.result
			? fields.find((e) => e.name === 'address')?.result
			: default_address
	);
	let occupation = $derived(fields.find((e) => e.name === 'occupation')?.result ?? '');
	let relationship = $derived(fields.find((e) => e.name === 'relationship')?.result ?? '');
	let contact = $derived(fields.find((e) => e.name === 'contact')?.result ?? '');
	let nation = $derived(fields.find((e) => e.name === 'nation')?.result ?? '');
	let organization = $derived(fields.find((e) => e.name === 'organization')?.result ?? '');
	let resone = $derived(fields.find((e) => e.name === 'resone')?.result ?? '');
	let n = $derived(fields.find((e) => e.name === 'n')?.result ?? '');
	let date_1 = $derived(fields.find((e) => e.name === 'date_1')?.result ?? '');
	let date_2 = $derived(fields.find((e) => e.name === 'date_2')?.result ?? '');
</script>

<input type="hidden" name="title" value="request_discharge" />
<main style="max-width: 1200px;">
	<Header {get_document_setting} {logo} {n} {title_eng} {title_khm} />
	<div class="text-center">
		<h4
			style="color: {get_document_setting?.title_color}"
			class="kh_font_muol_light text-decoration-underline"
		>
			លិខិតស្នើសុំចេញពីពេទ្យ
		</h4>
	</div>
	<br />
	<div style="color: {get_document_setting?.text_body_color}" class="section fs-5">
		<div style="color: {get_document_setting?.text_body_color};margin-left: 5em;" class="mb-2">
			ខ្ញុំបាទ/នាងខ្ញុំឈ្មោះ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={name}
				name="name"
				width="400px"
				type="text"
			/>
			ភេទ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={gender}
				name="gender"
				width="140px"
				type="text"
				data_list={['ប្រុស', 'ស្រី']}
			/>
			អាយុ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={age}
				name="age"
				width="110px"
				type="text"
			/> ឆ្នាំ
		</div>
		<div class="mb-2">
			សញ្ជាតិ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				data_list={nations_list}
				name="nation"
				value={nation}
				width="340px"
				type="text"
			/>
			{`លេខទូរស័ព្ទ`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="contact"
				value={contact}
				width="530px"
				type="text"
			/>
		</div>
		<div class="mb-2">
			{`មុខរបរ`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				data_list={occupation_list}
				name="occupation"
				value={occupation}
				width="490px"
				type="text"
			/>
			{`អង្គភាព/ទីកន្លែងការងារ`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="organization"
				value={organization}
				width="300px"
				type="text"
			/>
		</div>
		<div class="mb-2">
			{`អាសយដ្ឋាន ៖`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="address"
				value={address_ ?? ''}
				width="930px"
				type="text"
			/>
		</div>
		<div class="mb-2">
			{`ត្រូវជា`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="relationship"
				value={relationship}
				width="995px"
				type="text"
			/>
		</div>
		<div class="mb-2">
			{`របស់អ្នកជំងឺឈ្មេាះ`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={p_name}
				width="895px"
				type="text"
			/>
		</div>
		<div class="mb-2">
			{`សញ្ជាតិ`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={p_nation}
				width="340px"
				type="text"
			/>
			{`លេខទូរស័ព្ទ`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={p_contact}
				width="530px"
				type="text"
			/>
		</div>

		<div class="mb-2">
			{`ដែលបានចូលសម្រាកព្យាបាលនៅថ្ងៃទី`}
			<InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={khmerDate(p_date_checkup)}
				width="740px"
				type="text"
			/>
		</div>
		<div class="mb-2">
			ផ្នែក <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={p_department}
				width="1000px"
				type="text"
			/>
		</div>
		<div class="mb-2">
			នៃ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				value={title_khm}
				width="630px"
				type="text"
			/>
			ស្នើរសុំចេញពីពេទ្យមុនការកំណត់របស់គ្រូពេទ្យ។
		</div>
		<div class="mb-2">
			ដោយមូលហេតុ <InputDocument
				style="color: {get_document_setting?.text_input_color}"
				name="resone"
				value={resone}
				width="920px"
				type="text"
			/>
		</div>
	</div>
	<br />

	<div style="color: {get_document_setting?.text_body_color}" class="fs-5 mb-2 text-center">
		បើមានបញ្ហាអ្វីកើតឡើងខ្ញុំនឹងទទួលខុសត្រូវចំពោះមុខច្បាប់ដោយមិនប្តឹងរករឿងគ្រូពេទ្យឡើយ។
	</div>
	<br />
	<div class="text-end fs-5" style="margin-right: 5em;">
		<div>ហត្ថលេខា ឬ ស្នាមមេដៃ</div>
		<div style="padding-right: 30px;font-weight: bold;">សាមីខ្លួនសុំចេញ</div>
	</div>
	<div class="text-center fs-5">
		<KhDateInput date={date_1} name="date_1" />
		<div style="font-weight: bold;">គ្រូពេទ្យព្យាបាល</div>
	</div>
	<div style="margin-left: 5em;" class="fs-5">
		<KhDateInput date={date_2} name="date_3" />
		<div style="padding-left: 45px;">បានឃើញ និងឯកភាព</div>
		<div style="padding-left: 20px;font-weight: bold;">ប្រធានគ្លីនិក/ប្រធានមន្ទីរពេទ្យ</div>
	</div>
</main>
