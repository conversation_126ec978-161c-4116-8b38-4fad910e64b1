import { between, gte } from 'drizzle-orm';
import type { MySqlColumn } from 'drizzle-orm/mysql-core';
import { progressNote, visit } from '../schemas';
import { db } from '../db';
import { YYYYMMDD_Format } from '../../helper/index';
export * from '../../helper/index';
export * from './logError';


export function pagination(url: URL) {
	const page = Number(url.searchParams.get('page')) || 1;
	const limit = Number(url.searchParams.get('limit')) || 10;
	const currenctPage = page || 1;
	const offset = limit * (currenctPage - 1);
	return {
		limit,
		offset
	};
}
export function betweenHelper(url: URL, datetimeColumn: MySqlColumn) {
	const start = url.searchParams.get('start');
	const end = url.searchParams.get('end');

	if (start && end) {
		return between(datetimeColumn, start, end);
	}
	return undefined;
}

export async function genVisitID(type = 'OPD' as 'IPD' | 'OPD') {
	const today = new Date();
	const yy = String(today.getFullYear()).slice(-2); // Get last two digits of year
	const mm = String(today.getMonth() + 1).padStart(2, '0'); // Month (01-12)
	// const dd = String(today.getDate()).padStart(2, '0'); // Day (01-31)
	const datePrefix = `${yy}${mm}`; // Format YYMMDD
	const dateFind = `${yy}${mm}0000`;
	if (type === 'OPD') {
		const lastRecord = await db.query.visit.findMany({
			where: gte(visit.id, +dateFind)
		});
		const maxNumber = Math.max(...lastRecord.map((obj) => obj.id));
		const nextIndex =
			lastRecord.length > 0
				? parseInt(String(maxNumber).slice(-4)) + 1 // Get the last two digits and increment
				: 1;

		return `${datePrefix}${String(nextIndex).padStart(4, '0')}`; // Concatenate and format the next ID
	} else {
		const lastRecord = await db.query.progressNote.findMany({
			where: gte(progressNote.id, +dateFind)
		});
		const maxNumber = Math.max(...lastRecord.map((obj) => obj.id));
		const nextIndex =
			lastRecord.length > 0
				? parseInt(String(maxNumber).slice(-4)) + 1 // Get the last two digits and increment
				: 1;

		return `${datePrefix}${String(nextIndex).padStart(4, '0')}`; // Concatenate and format the next ID
	}
}
export function getDaysInMonth(m: number, year: number) {
	const month = m - 1
	// Validate month input (0-11)
	if (month < 0 || month > 11) {
		throw new Error("Month must be between 0 (January) and 11 (December)");
	}
	const lastDay = new Date(year, month + 1, 0); // Last day of the month
	const daysArray = [];
	const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
	for (let date = 1; date <= lastDay.getDate(); date++) {
		const currentDate = new Date(year, month, date);
		daysArray.push({
			date: date.toString(),
			full_date: YYYYMMDD_Format.date(currentDate),
			day: dayNames[currentDate.getDay()],

		});
	}
	const blank_arr = (n: number) => {
		const arr = []
		for (let i = 0; i < n; i++) {
			arr.push({ date: '', full_date: '', day: '' })
		}
		return arr
	}
	const firstDay = daysArray[0].day
	if (firstDay === 'monday') {
		daysArray.unshift(...blank_arr(1))
	}
	if (firstDay === 'tuesday') {
		daysArray.unshift(...blank_arr(2))
	}
	if (firstDay === 'wednesday') {
		daysArray.unshift(...blank_arr(3))
	}
	if (firstDay === 'thursday') {
		daysArray.unshift(...blank_arr(4))
	}
	if (firstDay === 'friday') {
		daysArray.unshift(...blank_arr(5))
	}
	if (firstDay === 'saturday') {
		daysArray.unshift(...blank_arr(6))
	}
	const minus = 42 - daysArray.length
	if (minus > 0) {
		daysArray.push(...blank_arr(minus))
	}
	const daysInWeek = []
	for (let i = 0; i < daysArray.length; i += 7) {
		daysInWeek.push(daysArray.slice(i, i + 7))

	}
	if (!daysInWeek[5].some((obj) => obj.date !== '')) {
		daysInWeek.pop()
	}
	return {
		days_in_week: daysInWeek,
		all_days: dayNames
	}
}