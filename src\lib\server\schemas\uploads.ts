import { int, mysqlTable, varchar } from 'drizzle-orm/mysql-core';
export const uploads = mysqlTable('uploads', {
	id: int().primaryKey().autoincrement(),
	filename: varchar({ length: 255 }),
	mimeType: varchar({ length: 255 }),
	related_id: int(),
	related_type: varchar({ length: 50 }).$type<related_type>()
});
export type related_type =
	| 'clinicinfo'
	| 'patient'
	| 'imagerieRequest'
	| 'laboratory'
	| 'paymentType'
	| 'billing'
	| 'payment'
	| 'document'
	| 'product'
	| 'exspend'
	| 'staff'
	| 'staffSign'
	| 'paymentService';
