<script lang="ts">
	import type { PageServerData } from '../../routes/(dash)/settup/physical-exam/$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	type Data = Pick<PageServerData, 'get_examas'>;
	interface Props {
		data: Data;
		exam_id?: number;
	}
	let { data, exam_id = $bindable() }: Props = $props();
	let { get_examas } = $derived(data);
	let find_exam = $derived(get_examas[0]);
	let loading = $state(false);
</script>

<!-- @_Add_MedicineType -->
<div class="modal fade" id="create-physical" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action="?/create_physical"
			method="post"
			bind:loading
			class="modal-content"
			fnSuccess={() => {
				exam_id = 0.1;
				document.getElementById('close_create_physical')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('physical')}</h4>
				<button
					id="close_create_physical"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={find_exam?.id} type="hidden" name="exam_id" />
								<label for="physical_name">{locale.T('physical')}</label>
								<input name="physical_name" type="text" class="form-control" id="physical_name" />
								<!-- {#if form?.examination}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if} -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
