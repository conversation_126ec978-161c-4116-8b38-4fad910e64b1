<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	let loading = $state(false);
	interface Props {
		visit_id: number;
		active_department_id?: number;
		class?: string;
		action: string;
		children?: import('svelte').Snippet;
	}

	let {
		visit_id,
		class: className = 'btn btn-primary',
		children,
		action,
		active_department_id
	}: Props = $props();
	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
</script>

<button
	type="button"
	data-bs-toggle="modal"
	data-bs-target={'#'.concat(id?.toString() ?? '')}
	class={className}
>
	{@render children?.()}
</button>

<div class="modal fade" tabindex="-1" role="dialog" {id} data-bs-backdrop="static">
	<div class="modal-dialog" role="document">
		<div class="modal-content rounded-3 shadow">
			<Form {action} bind:loading method="post">
				<input type="hidden" name="id" value={visit_id} />
				<input type="hidden" name="active_department_id" value={active_department_id} />
				<div class="modal-header justify-content-center text-bg-warning">
					<span class="fs-3">
						<i class="fa-solid fa-stethoscope"></i>
						Go to consultant
					</span>
				</div>

				<div class="modal-footer flex-nowrap p-0">
					<button
						id="close_confirm_submit"
						type="button"
						class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
						data-bs-dismiss="modal">{locale.T('no')}</button
					>
					<button
						disabled={loading}
						type="submit"
						data-bs-dismiss="modal"
						class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0
						rounded-0"
					>
						<strong>{locale.T('yes')}</strong>
					</button>
				</div>
			</Form>
		</div>
	</div>
</div>
