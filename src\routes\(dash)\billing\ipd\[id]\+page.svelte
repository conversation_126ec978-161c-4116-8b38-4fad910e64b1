<script lang="ts">
	import type { PageServerData } from './$types';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import ChargePrescription from '$lib/coms-billing/ChargePrescription.svelte';
	import ChargeService from '$lib/coms-billing/ChargeService.svelte';
	import ChargeBed from '$lib/coms-billing/ChargeBed.svelte';
	import ChargeGeneral from '$lib/coms-billing/ChargeGeneral.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import ChargeImagerie from '$lib/coms-billing/ChargeImagerie.svelte';
	import ChargeLaboratory from '$lib/coms-billing/ChargeLaboratory.svelte';
	import ChargeVaccine from '$lib/coms-billing/ChargeVaccine.svelte';
	import { calculateDifference } from '$lib/helper';
	import { store } from '$lib/store/store.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import SearchProductSubmit from '$lib/coms-form/SearchProductSubmit.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let {
		get_products,
		get_billings_due,
		get_progress_note,
		get_currency,
		all_money,
		get_active_beds
	} = $derived(data);
	let main_billing = $derived(get_progress_note?.billing);
	let main_charge_on_bed_ = $derived(main_billing?.charge.find((e) => e.charge_on === 'bed'));
	let main_charge_on_general_ = $derived(
		main_billing?.charge.find((e) => e.charge_on === 'general')
	);
	let main_charge_on_prescription_ = $derived(
		main_billing?.charge.find((e) => e.charge_on === 'prescription')
	);
	let main_charge_on_service_ = $derived(
		main_billing?.charge.find((e) => e.charge_on === 'service')
	);
	let getDayStay = $derived(
		calculateDifference(get_progress_note?.date_checkup, get_progress_note?.date_checkout)
	);
	let inerHight: string = $derived(
		(Number(store.inerHight.replace('px', '')) - 110).toString().concat('px')
	);
</script>

<div class="row">
	<div class="col-sm-8">
		<h4>
			<span>{locale.T('patient_name')}</span>
			<span class="text-primary">
				@{get_progress_note?.patient?.name_khmer},
				{get_progress_note?.patient?.name_latin}
			</span>
			<span>
				<DDMMYYYYFormat date={get_progress_note?.date_checkup} />
			</span>
		</h4>
	</div>
	<div class="col-sm-4">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-money-bills"></i>
					{locale.T('billing')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-stethoscope"></i>
					{locale.T('ipd')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="card rounded-0 bg-light">
			<div class="card-header">
				<div class=" row">
					<SearchProductSubmit
						placeholder="ស្វែករកតាមរយៈផលិតផល"
						q_name="q"
						items={get_products.map((e) => ({ id: e.id, name: e.products, price: e.price }))}
						action="?/create_product_order"
						billing_id={get_progress_note?.billing?.id}
					>
						<input type="hidden" value={get_progress_note?.billing?.id} name="billing_id" />
					</SearchProductSubmit>
				</div>
			</div>
			<div style="height: {inerHight}; " class=" overflow-y-auto table-responsive">
				<table class="table table-bordered table-sm text-nowrap table-light">
					<thead class="sticky-top top-0 bg-light table-active">
						<tr class="text-center">
							<th style="width: 45%;">{locale.T('products')}</th>
							<th style="width: 15%;">{locale.T('price')}</th>
							<th style="width: 10%;">{locale.T('qty')}</th>
							<th style="width: 15%;">{locale.T('total')} </th>
							<th style="width: 5%;">X</th>
						</tr>
					</thead>
					<tbody>
						<tr class="text-bg-success">
							<td colspan="6">
								<i class="fa-regular fa-calendar"></i>
								{locale.T('from_date')}
								<span class="text-decoration-underline text-primary">
									<DDMMYYYYFormat date={get_progress_note?.date_checkup} />
								</span>
								{locale.T('to_date')}
								<span class="text-decoration-underline text-primary">
									<DDMMYYYYFormat date={get_progress_note?.date_checkout} />
								</span>
								<span class="text-bg-warning px-2 rounded-1">
									{locale.T('duration_of_treatment')}
									{#if getDayStay?.days}
										{getDayStay?.days}
										<span>{locale.T('day')}</span>
									{/if}
									{#if getDayStay?.hours}
										{getDayStay?.hours}
										<span>{locale.T('hour')}</span>
									{/if}
								</span>
							</td>
						</tr>
						<ChargeBed
							data={{
								charge_on_bed: main_charge_on_bed_,
								get_currency: get_currency
							}}
							activeBed={{
								get_active_beds: get_active_beds
							}}
						/>
						<ChargeService
							data={{
								charge_on_service: main_charge_on_service_,
								get_currency: get_currency
							}}
						/>
						<ChargePrescription
							data={{
								charge_on_prescription: main_charge_on_prescription_,
								get_currency: get_currency,
								get_billing: main_billing || undefined
							}}
						/>
						<ChargeGeneral
							data={{
								charge_on_general: main_charge_on_general_,
								get_currency: get_currency
							}}
						/>
					</tbody>

					{#each get_progress_note?.visit || [] as item}
						{@const charge_on_general = item.billing?.charge.find((e) => e.charge_on === 'general')}
						{@const charge_on_service = item.billing?.charge.find((e) => e.charge_on === 'service')}
						{@const charge_on_prescription = item.billing?.charge.find(
							(e) => e.charge_on === 'prescription'
						)}

						{@const charge_on_laboratory = item.billing?.charge.find(
							(e) => e.charge_on === 'laboratory'
						)}
						{@const charge_on_vaccine = item.billing?.charge.find((e) => e.charge_on === 'vaccine')}
						{@const charge_on_imagerie = item.billing?.charge.find(
							(e) => e.charge_on === 'imagerie'
						)}
						<tbody class="">
							<tr class="text-bg-secondary">
								<td colspan="6">
									<span style="color: red;" class="fs-6 text-decoration-underline">
										#<DDMMYYYYFormat date={item.date_checkup} />
									</span>
									<span class="text-danger pb-0">
										{#if item.billing?.status !== 'checking'}
											#{locale.T('already_charged')}
										{/if}
									</span>
								</td>
							</tr>
							<ChargeService
								data={{
									charge_on_service: charge_on_service,
									get_currency: get_currency
								}}
							/>
							<ChargeImagerie
								data={{
									charge_on_imagerie: charge_on_imagerie,
									get_currency: get_currency
								}}
							/>
							<ChargeLaboratory
								data={{
									charge_on_laboratory: charge_on_laboratory,
									get_currency: get_currency
								}}
							/>
							<ChargeVaccine
								data={{
									charge_on_vaccine: charge_on_vaccine,
									get_currency: get_currency
								}}
							/>
							<ChargePrescription
								data={{
									charge_on_prescription: charge_on_prescription,
									get_currency: get_currency,
									get_billing: item.billing ?? undefined
								}}
							/>
							<ChargeGeneral
								data={{
									charge_on_general: charge_on_general,
									get_currency: get_currency
								}}
							/>
						</tbody>
					{/each}
				</table>
			</div>
			<div class="">
				<div class="row g-1">
					<div class="col-md-4">
						<div class=" bg-primary-subtle fs-6 px-2 py-1">
							<div class="row justify-content-between">
								<div class="col-auto">
									{locale.T('total')}
									{get_currency?.exchang_to}
								</div>
								<div class="col-auto">
									<span>
										<Currency
											class=""
											{get_currency}
											amount={all_money?.amount_ipd_opd}
											symbol={get_currency?.exchang_to}
										/>
									</span>
								</div>
							</div>
						</div>
					</div>
					<div class="col-md-4">
						<div class=" bg-primary-subtle fs-6 px-2 py-1">
							<div class="row justify-content-between">
								<div class="col-auto">
									{locale.T('paid')}
								</div>
								<div class="col-auto">
									<span>
										<Currency
											class=""
											{get_currency}
											amount={all_money?.opd_paid}
											symbol={get_currency?.exchang_to}
										/>
									</span>
								</div>
							</div>
						</div>
					</div>
					<div class="col-md-4">
						<div class=" bg-primary-subtle fs-6 px-2 py-1">
							<div class="row justify-content-between">
								<div class="col-auto">
									{locale.T('not_yet_paid')}
								</div>
								<div class="col-auto">
									<span>
										<Currency
											class="text-danger"
											{get_currency}
											amount={all_money?.total_ipd_opd}
											symbol={get_currency?.exchang_to}
										/>
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="row g-1 pt-1 pb-1">
					<div class="col-md-4">
						<div class=" bg-primary-subtle fs-6 px-2 py-1">
							<div class="row justify-content-between">
								<div class="col-auto">
									{locale.T('total')}
									{get_currency?.currency}
								</div>
								<div class="col-auto">
									<span>
										<Currency
											class=""
											amount={all_money?.amount_ipd_opd}
											symbol={get_currency?.currency}
										/>
									</span>
								</div>
							</div>
						</div>
					</div>
					<div class="col-md-4">
						<div class=" bg-primary-subtle fs-6 px-2 py-1">
							<div class="row justify-content-between">
								<div class="col-auto">
									{locale.T('paid')}
								</div>
								<div class="col-auto">
									<span>
										<Currency
											class=""
											amount={all_money?.opd_paid}
											symbol={get_currency?.currency}
										/>
									</span>
								</div>
							</div>
						</div>
					</div>
					<div class="col-md-4">
						<div class=" bg-primary-subtle fs-6 px-2 py-1">
							<div class="row justify-content-between">
								<div class="col-auto">
									{locale.T('not_yet_paid')}
								</div>
								<div class="col-auto">
									<span>
										<Currency
											class="text-danger"
											amount={all_money?.total_ipd_opd}
											symbol={get_currency?.currency}
										/>
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>

				{#each get_billings_due as item (item.id)}
					<div class=" border-0 bg-primary-subtle py-1 fs-6 mb-1">
						<div class="row">
							<div class="col text-start mx-2">
								{locale.T('previous_debt')}
								<DDMMYYYYFormat date={item.created_at} style="date" />
							</div>
							<div class="col text-end mx-2">
								<a href="/billing/repay?billing_id={item.id}">
									<Currency class="fs-6" amount={item.balance} symbol={get_currency?.currency} /></a
								>
							</div>
						</div>
					</div>
				{/each}
				<div class="row g-0">
					<div class="col">
						<a href="/billing/report" class="btn rounded-0 btn-primary btn-lg w-100">
							<i class="fa-solid fa-rotate-left"></i>{locale.T('back')}</a
						>
					</div>
					<div class="col">
						<a
							href="/billing/ipd/{get_progress_note?.id}/checkout"
							class="btn rounded-0 btn-success btn-lg w-100"
						>
							<i class="fa-solid fa-comments-dollar"></i> {locale.T('billing')}</a
						>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
