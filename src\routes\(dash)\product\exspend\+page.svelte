<script lang="ts">
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData, ActionData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}
	let { data, form }: Props = $props();
	let { get_exspends, get_currency, get_exspend, items } = $derived(data);
	let exspend_id: number | null = $state(null);
	let image = $state('');
	let n: number = $state(1);
	let total_exspend = $derived(get_exspends.reduce((s, e) => s + Number(e.amount), 0));
</script>

<DeleteModal action="?/delete_exspend" id={+exspend_id!} />
<!-- @_Add_Exspend -->
<div class="modal fade" id="view_refrence" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<div class="modal-content">
			<input type="hidden" name="exspend_id" value={get_exspend?.id ?? ''} />
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('references')}</h4>
				<button
					id="close_exspend"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<img style="width: 100%;" src={`${image}`} class="img-thumbnail" alt="" />
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('exspend')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/exspend" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-percent"></i>
					{locale.T('exspend')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row gap-1">
			<div class="col">
				<HeaderQuery>
					<div class="col-sm-3">
						<div class="input-group">
							<span class="input-group-text">{locale.T('start')}</span>
							<input type="date" name="start" class="form-control" />
						</div>
					</div>
					<div class="col-sm-3">
						<div class="input-group">
							<span class="input-group-text">{locale.T('end')}</span>
							<input type="date" name="end" class="form-control" />
						</div>
					</div>
					<div class="col-sm-3">
						<HandleQ />
					</div>
				</HeaderQuery>
			</div>
			<div class="col-auto text-end">
				<a href="/product/exspend/create" type="button" class="btn btn-success"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('exspend')}
				</a>
			</div>
		</div>
	</div>

	<div style="height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
		<table class="table table-hover table-bordered table-light">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th class="text-center">{locale.T('n')}</th>
					<th>{locale.T('date')}</th>
					<th>{locale.T('invoice_no')}</th>
					<th>{locale.T('amount')}</th>
					<th>{locale.T('exspend_type')}</th>
					<th>{locale.T('description')}</th>
					<th>{locale.T('references')}</th>
					<td></td>
				</tr></thead
			>
			<tbody>
				{#each get_exspends as item, index}
					<tr>
						<td class="text-center">
							{index + n}
						</td>
						<td class="text-center">
							<DDMMYYYYFormat date={item.datetime_invoice} />
						</td>
						<td>
							{item?.invoice_no || locale.T('none')}
						</td>
						<td>
							<Currency amount={item?.amount} symbol={get_currency?.currency} />
						</td>
						<td>
							{item.exspendType?.type ?? ''}
						</td>
						<td>
							{item?.description || locale.T('none')}
						</td>
						<td class="text-center">
							{#if item.uploads}
								<button
									onclick={() => (image = item.uploads?.filename ?? '')}
									data-bs-toggle="modal"
									data-bs-target="#view_refrence"
									class="btn btn-link p-0 p-0"
								>
									<i class="fa-solid fa-receipt"></i>
									{locale.T('references')}
								</button>
							{:else}
								<button class="btn btn-link p-0 p-0 disabled">
									{locale.T('none')}
								</button>
							{/if}
						</td>
						<td>
							<div class=" m-0 p-0">
								{#if item.uploads}
									<button
										aria-label="sowingimage"
										onclick={() => (image = item.uploads?.filename ?? '')}
										data-bs-toggle="modal"
										data-bs-target="#view_refrence"
										class="btn btn-primary btn-sm"
									>
										<i class="fa-solid fa-receipt"></i>
									</button>
								{/if}
								<a
									href="/product/exspend/create?exspend_id={item.id}"
									class="btn btn-primary btn-sm"
									aria-label="create_exspend"
									><i class="fa-solid fa-file-pen"></i>
								</a>
								<button
									onclick={() => (exspend_id = item.id)}
									aria-label="deletemodal"
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
				<tr class="table table-light">
					<td class="text-end" colspan="3">{locale.T('total')}</td>
					<td>
						{#if total_exspend <= 0}
							{locale.T('none')}
						{:else}
							<Currency amount={total_exspend} symbol={get_currency?.currency} />
						{/if}
					</td>
					<td colspan="4"></td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations {items} bind:n />
	</div>
</div>
