import { relations } from 'drizzle-orm';
import { date, int, mysqlTable, text, time, varchar } from 'drizzle-orm/mysql-core';
import { service } from './service';

export const operationProtocol = mysqlTable('operation_protocol', {
	id: int().primary<PERSON>ey().autoincrement(),
	surgeon: varchar({ length: 255 }),
	assistant_surgeon: varchar({ length: 255 }),
	anesthetist: var<PERSON><PERSON>({ length: 255 }),
	assistant_anesthetist: varchar({ length: 255 }),
	scrub_nurse: varchar({ length: 255 }),
	cirulating_nurse_block: varchar({ length: 255 }),
	midwife: varchar({ length: 255 }),
	pre_diagnosis: varchar({ length: 255 }),
	post_diagnosis: varchar({ length: 255 }),
	type_anesthesia: varchar({ length: 255 }),
	opertive_technique: text(),
	blood_less: varchar({ length: 255 }),
	notes: text(),
	date: date({ mode: 'string' }),
	start_time: time(),
	finish_time: time(),
	service_id: int().references(() => service.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	})
});

export const operationProtocolRelations = relations(operationProtocol, ({ one }) => ({
	service: one(service, {
		fields: [operationProtocol.service_id],
		references: [service.id]
	})
}));
