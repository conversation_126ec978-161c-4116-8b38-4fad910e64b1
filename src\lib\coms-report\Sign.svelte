<script lang="ts">
	import Athtml from '$lib/coms/Athtml.svelte';
	import GenQRcode from '$lib/coms/GenQRcode.svelte';
	let {
		qr,
		left,
		right
	}: {
		qr?: string | null | undefined;
		left?: {
			date?: string | null | undefined;
			img?: string | null | undefined;
			name?: string | null | undefined;
			title?: string | null | undefined;
			role?: string | null | undefined;
		};
		right?: {
			date?: string | null | undefined;
			img?: string | null | undefined;
			name?: string | null | undefined;
			title?: string | null | undefined;
			role?: string | null | undefined;
		};
	} = $props();

	function con_date(date: string | null | undefined) {
		if (date) {
			return (
				'ថ្ងៃទី ' +
				new Intl.DateTimeFormat('en-GB', { dateStyle: 'short' })
					.format(new Date(date))
					.replace('/', ' ខែ ')
					.replace('/', ' ឆ្នាំ ')
			);
		} else {
			return 'ថ្ងៃទី.........ខែ..........ឆ្នាំ20.........';
		}
	}
</script>

<div class="en_font_times_new_roman row justify-content-between">
	{#if left}
		<div class={!qr && !right ? 'col-12 fs-5 text-center pb-0' : 'col-4 fs-5 text-center pb-0'}>
			<Athtml html={left.title} />
			<span style="font-size: 100%;" class="kh_font_battambang">
				{con_date(left.date)} <br />
				{left.role}<br />
			</span>
			{#if left.img}
				<div style="min-height: 170px;">
					<img class="my-0 py-0" style="height: 170px;" src={left.img} alt="" />
				</div>
			{:else}
				<br />
				<br />
				<br />
				<br />
			{/if}
			<p>{left?.name ?? ''}</p>
		</div>
	{/if}
	{#if qr}
		<div style="width: 180px;" class={left ? 'col-4 text-center' : 'col-8'}>
			<GenQRcode data={{ text: qr }} />
		</div>
	{/if}
	{#if right}
		<div class="col-4 text-center fs-5 pb-0">
			<Athtml html={right.title} />
			<span style="font-size: 100%;" class="kh_font_battambang">
				{con_date(right.date)} <br />
				{right.role} <br />
			</span>
			{#if right.img}
				<div style="min-height: 170px;">
					<img class="my-0 py-0" style="height: 170px;" src={right.img} alt="" />
				</div>
			{:else}
				<br />
				<br />
				<br />
				<br />
			{/if}
			<p>{right?.name ?? ''}</p>
		</div>
	{/if}
</div>
