
import { db } from '$lib/server/db';
import { between<PERSON><PERSON><PERSON>, getDaysIn<PERSON>onth, YYYYMMDD_Format } from '$lib/server/utils';
import { and, between, desc, eq, gt, isNull, ne } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { billing, exspend, payment, payroll } from '$lib/server/schemas';

export const load = (async ({ url }) => {
  const year_month = url.searchParams.get('year_month') || YYYYMMDD_Format.date(new Date()).slice(0, 7);
  const get_currency = await db.query.currency.findFirst()
  const year = year_month.split('-')[0];
  const month = year_month.split('-')[1];
  const { all_days, days_in_week } = getDaysInMonth(Number(month), Number(year))
  const yyyymmdd = YYYYMMDD_Format.date(year_month)
  const get_billings = await db.query.billing.findMany({
    where: and(
      ne(billing.status, 'checking'),
      ne(billing.status, 'paying'),
      gt(billing.amount, 0),
      between(billing.created_at, yyyymmdd, year_month.concat('-31'))
    ),
    orderBy: desc(billing.created_at)
  });
  const get_exspends = await db.query.exspend.findMany({
    where: between(exspend.datetime_invoice, yyyymmdd, year_month.concat('-31')),
  })
  const get_payrolls = await db.query.payroll.findMany({
    where: between(payroll.payment_date, yyyymmdd, year_month.concat('-31')),
  })
  const get_payments = await db.query.payment.findMany({
    where: and(
      between(payment.datetime, yyyymmdd, year_month.concat('-31')),
      isNull(payment.exspend_id)
    ),
  })
  return {
    get_currency,
    year_month,
    all_days, days_in_week,
    get_billings,
    get_exspends,
    get_payrolls,
    get_payments

  };
}) satisfies PageServerLoad;