import * as schema from '../schemas';
import { drizzle } from 'drizzle-orm/mysql2';
// import { env } from '$env/dynamic/private';
// import mysql from 'mysql2/promise';
import 'dotenv/config';
// export const client = await mysql.createConnection(process.env.DATABASE_URL);
const { DB_HOST, DB_USER, DB_PORT, DB_NAME, DB_PASSWORD } = process.env;
if (!DB_HOST || !DB_HOST || !DB_USER || !DB_PASSWORD || !DB_PORT)
	throw new Error('DATABASE_URL is not set');
const db_connection = `mysql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}`;
export const db = drizzle({
	mode: 'default',
	schema: schema,
	connection: {
		uri: db_connection
	}
});
