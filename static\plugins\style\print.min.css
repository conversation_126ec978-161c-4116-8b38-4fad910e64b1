@media print {
   *,
   *::before,
   *::after {
     text-shadow: none !important;
     box-shadow: none !important;
   }
   a:not(.btn) {
     text-decoration: underline;
   }
   abbr[title]::after {
     content: " (" attr(title) ")";
   }
   pre {
     white-space: pre-wrap !important;
   }
   pre,
   blockquote {
     border: 1px solid #adb5bd;
     page-break-inside: avoid;
   }
   tr,
   img {
     page-break-inside: avoid;
   }
   p,
   h2,
   h3 {
     orphans: 3;
     widows: 3;
   }
   h2,
   h3 {
     page-break-after: avoid;
   }
   @page {
     size: a3;
   }
   body {
     min-width: 992px !important;
   }
   .container {
     min-width: 992px !important;
   }
   .badge {
     border: 1px solid #000;
   }
   .table {
     border-collapse: collapse !important;
   }
   .table td,
   .table th {
     background-color: #fff !important;
   }
   .table-bordered th,
   .table-bordered td {
     border: 1px solid #dee2e6 !important;
   }
   .table-dark {
     color: inherit;
   }
   .table-dark th,
   .table-dark td,
   .table-dark thead th,
   .table-dark tbody + tbody {
     border-color: #dee2e6;
   }
 }