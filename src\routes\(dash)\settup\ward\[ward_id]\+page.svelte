<script lang="ts">
	import type { PageServerData } from './$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { page } from '$app/state';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let room_id: number | undefined = $state();
	let ward_id: number | undefined = $state();
	let bed_id: number | undefined = $state();
	let loading = $state(false);
	let { get_ward, get_products, get_wards, get_departments } = $derived(data);
	let find_room = $derived(get_ward?.room.find((e) => e.id === room_id));
	let find_rooms = $derived(get_ward?.room.filter((e) => e.id === ward_id));
	let find_bed = $derived(find_room?.bed.find((e) => e.id === bed_id));
</script>

<!-- @_Add_bed-->
<div class="modal fade" id="create_bed" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action={find_bed?.id ? '?/update_bed' : '?/create_bed'}
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => document.getElementById('close_create_bed')?.click()}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('bed')}</h4>
				<button
					onclick={() => {
						bed_id = 0;
						room_id = 0;
					}}
					id="close_create_bed"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={page.params.ward_id} type="hidden" name="ward_id" />
								<input value={room_id} type="hidden" name="room_id" />
								<input value={find_bed?.id ?? ''} type="hidden" name="bed_id" />
								<label for="bed">{locale.T('bed')}</label>
								<input
									value={find_bed?.bed ?? ''}
									name="bed"
									type="text"
									class="form-control"
									id="bed"
								/>
								<!-- {#if form?.ward}
									<p class="text-danger">{locale.T('input_data')}</p>
								{/if} -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<div
				class={find_bed
					? 'modal-footer justify-content-between'
					: 'modal-footer justify-content-end'}
			>
				{#if find_bed}
					<button
						type="button"
						data-bs-toggle="modal"
						class="btn btn-danger"
						data-bs-target="#delete_bed"
					>
						<i class="fa-solid fa-trash-can"></i> {locale.T('delete_')}</button
					>
				{/if}
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
<!-- @_Add_Room-->
<div class="modal fade" id="create_room" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action="?/create_room"
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => document.getElementById('close_room')?.click()}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('room')}</h4>
				<button
					onclick={() => {
						bed_id = 0;
						room_id = 0;
					}}
					id="close_room"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={page.params.ward_id} type="hidden" name="ward_id" />
								<label for="ward">{locale.T('room')}</label>
								<input name="room" type="text" class="form-control" id="room" />
								<!-- {#if form?.ward}
									<p class="text-danger">{locale.T('input_data')}</p>
								{/if} -->
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="department_id">{locale.T('department')}</label>
								<select class="form-control" name="department_id" id="department_id">
									{#each get_departments as { products, id }}
										<option value={id}>{products}</option>
									{/each}
								</select>
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="product_id">{locale.T('type')}</label>
								<select class="form-control" name="product_id" id="product_id">
									{#each get_products as { products, id }}
										<option value={id}>{products}</option>
									{/each}
								</select>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
<!-- @_Update_Room-->
<div class="modal fade" id="update_room" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action="?/update_room"
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => {
				room_id = 0;
				document.getElementById('close_update_room')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('room')}</h4>
				<button
					onclick={() => {
						bed_id = 0;
						room_id = 0;
					}}
					id="close_update_room"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input type="hidden" name="room_id" value={find_room?.id ?? ''} />
								<label for="ward">{locale.T('room')}</label>
								<input
									value={find_room?.room ?? ''}
									name="room"
									type="text"
									class="form-control"
									id="room"
								/>
								<!-- {#if form?.ward}
									<p class="text-danger">{locale.T('input_data')}</p>
								{/if} -->
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="department_id">{locale.T('department')}</label>
								<select class="form-control" name="department_id" id="department_id">
									{#each get_departments as { products, id }}
										<option selected={id === find_room?.department_id} value={id}>{products}</option
										>
									{/each}
								</select>
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="ward">{locale.T('type')}</label>
								<select class="form-control" name="product_id" id="product_id">
									{#each get_products as { products, id }}
										<option selected={find_room?.product_id === id} value={id}>{products}</option>
									{/each}
								</select>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div
				class={find_room
					? 'modal-footer justify-content-between'
					: 'modal-footer justify-content-end'}
			>
				{#if find_room}
					<button
						type="button"
						data-bs-toggle="modal"
						class="btn btn-danger"
						data-bs-target="#delete_room"
					>
						<i class="fa-solid fa-trash-can"></i> {locale.T('delete_')}</button
					>
				{/if}
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<a href="/settup/ward" class="btn btn-link p-0 text-secondary"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/ward" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-hotel"></i>
					{locale.T('ward')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header fs-4">
		<div class="row">
			<div class="col">
				<span><i class="fa-solid fa-hospital"></i> {get_ward?.ward ?? ''} </span>
			</div>
			<div class="col-auto">
				<button
					type="button"
					class="btn btn-success"
					data-bs-toggle="modal"
					data-bs-target="#create_room"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('room')}
				</button>
			</div>
		</div>
	</div>
</div>

<DeleteModal delete_modal="delete_room" action="?/delete_room" id={room_id} />
<DeleteModal delete_modal="delete_bed" action="?/delete_bed" id={bed_id} />
<br />
<div class="table-responsive">
	<table class="table table-bordered table-light">
		<tbody>
			{#each get_ward?.room || [] as item}
				{@const beds = item.bed}
				{@const active = item.id === room_id ? true : false}
				<tr>
					<td class="text-bg-success" style="width: 30%;">
						<button
							data-bs-toggle="modal"
							data-bs-target="#update_room"
							onclick={() => (room_id = item.id)}
							type="button"
							class={active ? `btn btn-light ` : 'btn btn-outline-light'}
						>
							<i class="fa-regular fa-window-maximize"></i>
							{item.room ?? ''}
							{item.product?.products ?? ''}

							<span class="badge text-bg-primary">
								#{item.department?.products ?? ''}
							</span>
						</button>
					</td>
					<td>
						<div class="row w-100">
							<div class="col-auto">
								{#each beds as iitem}
									<button
										onclick={() => {
											bed_id = 0;
											bed_id = iitem.id;
											room_id = 0;
											room_id = iitem?.room_id ?? 0;
										}}
										data-bs-toggle="modal"
										data-bs-target="#create_bed"
										class="btn btn-info me-2 my-2 rounded-0"
									>
										<i class="fa-solid fa-bed"></i> {iitem.bed}</button
									>
								{/each}
							</div>
							<div class="col text-end">
								<button
									aria-label="deletebed"
									onclick={() => {
										room_id = 0;
										room_id = item.id;
									}}
									data-bs-toggle="modal"
									data-bs-target="#create_bed"
									type="button"
									class="btn btn-success btn-sm"
									><i class="fa-solid fa-square-plus"></i>
								</button>
							</div>
						</div>
					</td>
				</tr>
			{/each}
		</tbody>
	</table>
</div>
