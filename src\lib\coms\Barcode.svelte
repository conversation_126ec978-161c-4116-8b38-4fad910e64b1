<script lang="ts">
	import JsBarcode from 'jsbarcode';

	let { text }: { text: string | null } = $props();

	let barcode = $state('');
	$effect(() => {
		if (text && text?.length < 15) {
			const canvas = document.createElement('canvas');
			const scaleFactor = 3;
			canvas.width = 300 * scaleFactor;
			canvas.height = 100 * scaleFactor;
			const ctx = canvas.getContext('2d');
			if (ctx) ctx.scale(scaleFactor, scaleFactor); // scale canvas context
			JsBarcode(canvas, text || '', {
				height: 70,
				width: 5,
				displayValue: false,
				textMargin: 0,
				margin: 0,
				fontSize: 10
			});
			barcode = canvas.toDataURL('image/png');
		}
	});
</script>

<img class="img-thumbnail p-1" src={barcode} alt="" />
