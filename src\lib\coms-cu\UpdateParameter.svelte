<script lang="ts">
	import type {
		ActionData,
		PageServerData
	} from '../../routes/(dash)/settup/parameter/[id]/$types';
	type Data = Pick<PageServerData, 'get_para_units' | 'get_parameters'>;
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	interface Props {
		form: ActionData;
		data: Data;
		parameter_id: number;
	}

	let { form, data, parameter_id = $bindable() }: Props = $props();
	let loading = $state(false);
	let { get_para_units, get_parameters } = $derived(data);
	let find_parameter = $derived(get_parameters[0]);
</script>

<!-- @_Add_Parameter -->
<div class="modal fade" id="update_parameter" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			enctype="multipart/form-data"
			action="?/update_parameter"
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => {
				parameter_id = 0;
				document.getElementById('close_update_parameter')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('parameter')}</h4>
				<button
					id="close_update_parameter"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input type="hidden" name="parameter_id" value={find_parameter?.id} />
								<label for="parameter">{locale.T('parameter')}</label>
								<input
									value={find_parameter?.parameter}
									name="parameter_"
									type="text"
									class="form-control"
									id="parameter"
								/>
								{#if form?.parameter_}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="description">{locale.T('description')}</label>

								<textarea
									value={find_parameter?.description}
									rows="3"
									class="form-control"
									name="description"
									id="description"
								></textarea>

								{#if form?.description}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<div class="row">
								<div class="col-6">
									<div class=" pb-3">
										<label for="unit">{locale.T('unit')}</label>
										<div class="input-group">
											<SelectParam
												value={find_parameter?.para_unit_id}
												height="200"
												name="para_unit_id"
												items={get_para_units.map((e) => ({ id: e.id, name: e.unit }))}
											/>
										</div>

										{#if form?.para_unit_id}
											<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
										{/if}
									</div>
								</div>
								<div class="col-6">
									<div class=" pb-3">
										<label for="gender">{locale.T('gender')}</label>
										<select
											value={find_parameter?.gender}
											name="gender"
											class="form-control"
											id="gender"
										>
											<option value="Other">{locale.T('none')}</option>
											<option value="Male">{locale.T('male')}</option>
											<option value="Female">{locale.T('female')}</option>
										</select>
										{#if form?.gender}
											<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
										{/if}
									</div>
								</div>
							</div>
						</div>
						<div class="col-12">
							<div class="row">
								<div class="col-5">
									<div class=" pb-3">
										<label for="mini">{locale.T('mini')}</label>
										<input
											value={find_parameter?.mini === 0 ? '' : find_parameter?.mini}
											name="mini"
											type="number"
											step="0.01"
											class="form-control"
											id="mini"
										/>
										{#if form?.mini}
											<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
										{/if}
									</div>
								</div>
								<div class="col-2">
									<div class=" text-center">
										<label for="sign">{locale.T('sign')}</label>
										<select
											value={find_parameter?.sign ?? ''}
											class="form-control text-center"
											name="sign"
											id="sign"
										>
											<option value=""></option>
											<option value="-">{'-'}</option>
											<option value=">">{'>'}</option>
											<option value="<">{'<'}</option>
										</select>
									</div>
								</div>
								<div class="col-5">
									<div class=" pb-3">
										<label for="Maxi">{locale.T('maxi')}</label>
										<input
											value={find_parameter?.maxi === 0 ? '' : find_parameter?.maxi}
											name="maxi"
											step="0.01"
											type="number"
											class="form-control"
											id="Maxi"
										/>
										{#if form?.maxi}
											<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
										{/if}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
