import { db } from '$lib/server/db';
import { fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { inventory, exspend } from '$lib/server/schemas';
import { eq, isNull } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';

export const load = (async ({ params }) => {
	const { id: suppler_id } = params;
	const get_supplier = await db.query.supplier.findFirst({
		with: {
			inventory: {
				where: isNull(inventory.exspend_id),
				with: {
					product: {
						with: {
							unit: true,
							subUnit: {
								with: {
									unit: true
								}
							}
						}
					}
				}
			}
		}
	});
	const get_invoices = await db.query.exspend.findMany({
		where: eq(exspend.supplier_id, +suppler_id)
	});
	const get_currency = await db.query.currency.findFirst({});
	return { get_invoices, get_currency, get_supplier };
}) satisfies PageServerLoad;
export const actions: Actions = {
	create_invoice_supplier: async ({ request, params, url }) => {
		const { id: supplier_id } = params;
		const body = await request.formData();
		const { datetime_invoice, invoice_no, invoice_id, amount, paid } = Object.fromEntries(
			body
		) as Record<string, string>;
		const validErr = {
			datetime_invoice: false,
			invoice_no: false,
			supplier_id: false
		};
		if (!datetime_invoice.trim()) validErr.datetime_invoice = true;
		if (!invoice_no.trim()) validErr.invoice_no = true;
		if (!supplier_id.trim()) validErr.supplier_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (invoice_id) {
			await db
				.update(exspend)
				.set({
					amount: +amount,
					credit: +amount - +paid,
					invoice_no: invoice_no,
					supplier_id: +supplier_id,
					datetime_invoice: datetime_invoice,
					paid: +paid
				})
				.where(eq(exspend.id, +invoice_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (!invoice_id) {
			await db
				.insert(exspend)
				.values({
					amount: +amount,
					credit: +amount - +paid,
					invoice_no: invoice_no,
					supplier_id: +supplier_id,
					datetime_invoice: datetime_invoice,
					paid: +paid
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	delete_invoice_supplier: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (isNaN(+id)) return fail(400, { id: true });
		await db
			.delete(exspend)
			.where(eq(exspend.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
