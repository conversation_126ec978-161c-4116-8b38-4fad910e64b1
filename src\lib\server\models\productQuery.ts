import { and, asc, eq, inArray, like, or } from 'drizzle-orm';
import { db } from '../db';
import { inventory, product, uploads } from '../schemas';
import { pagination } from '../utils';
interface ProductQuery {
	url: URL;
	page?: boolean;
}
// Product all
export async function productQuery({ url, page }: ProductQuery) {
	const q = url.searchParams.get('q') || '';
	const category_id = url.searchParams.get('category_id') || '';
	const group_id = url.searchParams.get('group_id') || '';
	const barcode = url.searchParams.get('barcode') || '';
	let get_products;
	if (page) {
		get_products = await db.query.product.findMany({
			where: and(
				+category_id ? eq(product.category_id, +category_id) : undefined,
				+group_id ? eq(product.group_id, +group_id) : undefined,
				barcode ? eq(product.barcode, barcode) : undefined,
				or(like(product.products, `%${q}%`), like(product.barcode, `%${q}%`))
			),
			with: {
				subUnit: {
					with: {
						unit: true
					}
				},
				unit: true,
				group: true,
				parameter: true,
				category: true,
				inventory: {
					with: {
						costUnit: true,
						exspend: true
					}
				},
				productOrder: {
					columns: {
						qty_adjustment: true
					}
				}
			},
			orderBy: asc(product.products),
			...pagination(url)
		});
	} else {
		get_products = await db.query.product.findMany({
			where: and(
				+category_id ? eq(product.category_id, +category_id) : undefined,
				+group_id ? eq(product.group_id, +group_id) : undefined,
				barcode ? eq(product.barcode, barcode) : undefined,
				or(like(product.products, `%${q}%`), like(product.barcode, `%${q}%`))
			),
			with: {
				subUnit: {
					with: {
						unit: true
					}
				},
				unit: true,
				group: true,
				parameter: true,
				category: true,
				inventory: {
					where: and(eq(inventory.is_outstock, false), eq(inventory.is_close_inventory, false)),
					with: {
						costUnit: true,
						exspend: true
					}
				},
				productOrder: {
					columns: {
						qty_adjustment: true
					}
				}
			},
			orderBy: asc(product.products),
			limit: 200
		});
	}
	const count = await db.$count(
		product,
		and(
			+category_id ? eq(product.category_id, +category_id) : undefined,
			+group_id ? eq(product.group_id, +group_id) : undefined,
			barcode ? eq(product.barcode, barcode) : undefined,
			or(like(product.products, `%${q}%`))
		)
	);
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'product'),
			inArray(
				uploads.related_id,
				get_products.map((e) => e.id)
			)
		)
	});
	const filter_produsts = get_products.map((e) => {
		const is_not_count_stock = e.inventory.some((ee) => ee.is_count_stock === false);
		const qty_order = e.productOrder.reduce((acc, cur) => acc + cur.qty_adjustment, 0);
		const is_not_inventory = e.inventory.length === 0;
		const qty_adjustment = e.inventory.reduce(
			(acc, cur) => (cur.is_count_stock ? acc + cur.qty_adjustment : acc + 0),
			0
		);
		const qty_expire = e.inventory.reduce((acc, cur) => acc + cur.qty_expire, 0);
		const qty_available = is_not_inventory ? 0 : qty_adjustment - (qty_order + qty_expire);
		const last_datetime_outstock = e.inventory.reduce<string | null>((latest, item) => {
			if (!item.datetime_outstock) return latest; // Skip if datetime is null/undefined
			if (!latest || new Date(item.datetime_outstock) > new Date(latest)) {
				return item.datetime_outstock;
			}
			return latest;
		}, null);
		return {
			...e,
			uploads: get_uploads.find((ee) => ee.related_id === e.id),
			qty_order: parseFloat(qty_order.toFixed(2)),
			qty_expire: parseFloat(qty_expire.toFixed(2)),
			qty_adjustment: parseFloat(qty_adjustment.toFixed(2)),
			qty_available: parseFloat(qty_available.toFixed(2))
		};
	});
	return {
		get_products: filter_produsts,
		items: count
	};
}

// Product Exspire
export async function productExspire({ url }: ProductQuery) {
	const q = url.searchParams.get('q') || '';
	const category_id = url.searchParams.get('category_id') || '';
	const group_id = url.searchParams.get('group_id') || '';
	const barcode = url.searchParams.get('barcode') || '';
	const get_products = await db.query.product.findMany({
		where: and(
			+category_id ? eq(product.category_id, +category_id) : undefined,
			+group_id ? eq(product.group_id, +group_id) : undefined,
			barcode ? eq(product.barcode, barcode) : undefined,
			or(like(product.products, `%${q}%`)),
			eq(product.id, 2)
		),
		with: {
			subUnit: {
				with: {
					unit: true
				}
			},
			unit: true,
			group: true,
			parameter: true,
			category: true,
			inventory: {
				with: {
					costUnit: true,
					exspend: true
				}
			},
			productOrder: {
				columns: {
					qty_adjustment: true
				}
			}
		},
		orderBy: asc(product.products),
		...pagination(url)
	});
	const count = await db.$count(
		product,
		and(
			+category_id ? eq(product.category_id, +category_id) : undefined,
			+group_id ? eq(product.group_id, +group_id) : undefined,
			barcode ? eq(product.barcode, barcode) : undefined,
			or(like(product.products, `%${q}%`))
		)
	);
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'product'),
			inArray(
				uploads.related_id,
				get_products.map((e) => e.id)
			)
		)
	});
	const filter_produsts = get_products.map((e) => {
		const is_not_count_stock = e.inventory.some((ee) => ee.is_count_stock === false);
		const qty_order = e.productOrder.reduce((acc, cur) => acc + cur.qty_adjustment, 0);
		const qty_adjustment = e.inventory.reduce(
			(acc, cur) => (cur.is_count_stock ? acc + cur.qty_adjustment : acc + 0),
			0
		);
		const qty_available = qty_adjustment - qty_order;
		const inventories = e.inventory || [];
		const last_inventory = inventories.sort((a, b) => {
			if (!a.datetime_buy) return 1;
			if (!b.datetime_buy) return -1;
			return new Date(a.datetime_buy).getTime() - new Date(b.datetime_buy).getTime();
		})[0];
		const sort_inventory = inventories.sort((a, b) => {
			if (!a.datetime_buy) return 1;
			if (!b.datetime_buy) return -1;
			return new Date(a.datetime_buy).getTime() - new Date(b.datetime_buy).getTime();
		});
		// const last_datetime_outstock = e.inventory.reduce<string | null>((latest, item) => {
		//    if (!item.datetime_outstock) return latest;
		//    if (!latest || new Date(item.datetime_outstock) > new Date(latest)) {
		//       return item.datetime_outstock;
		//    }
		//    return latest;
		// }, null);
		console.log(last_inventory);
		return {
			...e,
			uploads: get_uploads.find((ee) => ee.related_id === e.id),
			qty_order,
			qty_adjustment,
			qty_available
		};
	});
	return {
		get_products: filter_produsts,
		items: count
	};
}
