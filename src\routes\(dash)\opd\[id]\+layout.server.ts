import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';
import { db } from '$lib/server/db';
import { laboratoryResult, parameter, progressNote, uploads, visit } from '$lib/server/schemas';
import { and, asc, desc, eq, isNull, or } from 'drizzle-orm';
export const load: LayoutServerLoad = async ({ params, parent, url }) => {
	await parent();
	const visit_id = params.id;
	const old_visit_id = url.searchParams.get('old_visit_id') ?? '';
	if (isNaN(+visit_id)) redirect(303, '/patient/all');
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, Number(visit_id)),
		with: {
			laboratory: true,
			billing: true,
			vaccine: {
				with: {
					product: true
				}
			},
			adviceTeaching: true,
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},

			department: true,
			staff: true,
			progressNote: {
				with: {
					activeBed: {
						with: {
							bed: {
								with: {
									ward: true,
									room: {
										with: {
											product: true
										}
									}
								}
							}
						}
					},
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					}
				}
			}
		}
	});

	if (!get_visit) redirect(303, '/patient/all');
	const get_visits = await db.query.visit.findMany({
		where: and(
			isNull(visit.progress_note_id),
			or(eq(visit.checkin_type, 'OPD')),
			eq(visit.patient_id, get_visit.patient_id)
		)
	});
	const find_old_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +old_visit_id),
		with: {
			laboratory: true,
			appointment: true,
			vaccine: {
				with: {
					product: true
				}
			},
			adviceTeaching: true,

			service: {
				with: {
					operationProtocol: true,
					product: true
				}
			},
			accessment: true,
			presrciption: {
				with: {
					product: {
						with: {
							unit: true
						}
					}
				}
			},
			laboratoryRequest: {
				with: {
					product: {
						with: {
							parameter: {
								with: {
									paraUnit: true,
									laboratoryResult: true
								},
								orderBy: asc(parameter.id)
							}
						}
					},
					laboratoryResult: true
				},
				orderBy: desc(laboratoryResult.id)
			},
			imagerieRequest: {
				with: {
					product: true
				}
			},
			subjective: true,
			physicalExam: {
				with: {
					physical: {
						with: {
							exam: true
						}
					}
				}
			},
			vitalSign: true
		}
	});
	const get_exams = await db.query.exam.findMany({
		with: {
			physical: true
		}
	});
	const get_progress_notes = await db.query.progressNote.findMany({
		where: eq(progressNote.patient_id, get_visit.patient_id)
	});
	let patient_info;
	if (get_visit) {
		patient_info = {
			...get_visit?.patient,
			date_checkup: get_visit?.date_checkup
		};
	}
	if (get_visit?.progressNote) {
		patient_info = {
			...get_visit?.progressNote?.patient,
			date_checkup: get_visit?.progressNote?.date_checkup
		};
	}
	const get_clinic_info = await db.query.clinicinfo.findFirst();
	const get_logo = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_id, get_clinic_info?.id ?? 0),
			eq(uploads.related_type, 'clinicinfo'),
			eq(uploads.mimeType, 'logo0')
		)
	});
	return {
		get_visit,
		get_visits,
		get_progress_notes,
		get_exams,
		find_old_visit,
		patient_info,
		get_logo
	};
};
