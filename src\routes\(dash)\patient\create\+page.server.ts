import { db } from '$lib/server/db';
import { uploads, patient, village, commune, district, words } from '$lib/server/schemas';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { and, desc, eq } from 'drizzle-orm';
import { YYYYMMDD_Format } from '$lib/server/utils';
import logError from '$lib/server/utils/logError';
import { fileHandle } from '$lib/server/upload';
export const load = (async ({ parent, url }) => {
	await parent();
	const patient_id = url.searchParams.get('patient_id') || '';
	const province_id = url.searchParams.get('province_id') ?? '';
	const district_id = url.searchParams.get('district_id') ?? '';
	const commune_id = url.searchParams.get('commune_id') ?? '';
	const get_provinces = await db.query.provice.findMany({});
	const get_districts = await db.query.district.findMany({
		where: eq(district.province_id, Number(province_id))
	});
	const get_conmunies = await db.query.commune.findMany({
		where: eq(commune.district_id, Number(district_id))
	});
	const get_vilages = await db.query.village.findMany({
		where: eq(village.commune_id, Number(commune_id))
	});
	const get_patient = await db.query.patient.findFirst({
		where: eq(patient.id, +patient_id),
		orderBy: desc(patient.id),
		with: {
			provice: true,
			district: true,
			commune: true,
			village: true
		}
	});
	const get_words = await db.query.words.findMany({
		where: eq(words.category, 'patient')
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'patient'), eq(uploads.related_id, +patient_id))
	});
	return {
		get_patient: {
			...get_patient,
			uploads: get_upload
		},
		get_provinces,
		get_districts,
		get_conmunies,
		get_vilages,
		get_words
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_patient: async ({ request, url }) => {
		const body = await request.formData();
		const image = body.get('image') as File;
		const {
			name_khmer,
			name_latin,
			province_id,
			district_id,
			blood_group,
			dob,
			education,
			nation,
			material_status,
			work_place,
			occupation,
			commune_id,
			village_id,
			other,
			telephone,
			gender,
			f_name_khmer,
			f_name_latin,
			f_telephone,
			f_occupation,
			m_name_khmer,
			m_name_latin,
			m_telephone,
			m_occupation,
			c_name_khmer,
			c_name_latin,
			c_telephone,
			c_occupation
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			name_khmer: false,
			name_latin: false,
			province_id: false,
			district_id: false,
			age: false,
			dob: false,
			commune_id: false,
			village_id: false,
			other: false,
			telephone: false,
			gender: false
		};
		if (!name_khmer.trim()) validErr.name_khmer = true;
		if (!name_latin.trim()) validErr.name_latin = true;
		if (!dob.trim()) validErr.dob = true;
		if (!gender.trim()) validErr.gender = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const created_at = YYYYMMDD_Format.datetime(new Date());
		try {
			const create_patient: { id: number }[] = await db
				.insert(patient)
				.values({
					dob: dob,
					gender: gender,
					name_khmer: name_khmer,
					blood_group: blood_group,
					education: education,
					nation: nation,
					material_status: material_status,
					work_place: work_place,
					occupation: occupation,
					name_latin: name_latin,
					telephone: telephone,
					province_id: Number(province_id) || null,
					district_id: Number(district_id) || null,
					commune_id: Number(commune_id) || null,
					village_id: Number(village_id) || null,
					other: other,
					f_name_khmer: f_name_khmer,
					f_name_latin: f_name_latin,
					f_telephone: f_telephone,
					f_occupation: f_occupation,
					m_name_khmer: m_name_khmer,
					m_name_latin: m_name_latin,
					m_telephone: m_telephone,
					m_occupation: m_occupation,
					c_name_khmer: c_name_khmer,
					c_name_latin: c_name_latin,
					c_telephone: c_telephone,
					c_occupation: c_occupation,
					created_at: created_at
				})
				.$returningId();
			if (image.size && create_patient[0].id) {
				await fileHandle.insert(image, create_patient[0].id, 'patient');
			}
		} catch (e) {
			logError({ url, body, err: e });
		}
		redirect(300, '/patient/all');
	},
	update_patient: async ({ request, url }) => {
		const body = await request.formData();
		const {
			patient_id,
			name_khmer,
			name_latin,
			province_id,
			district_id,
			blood_group,
			education,
			nation,
			material_status,
			work_place,
			occupation,
			dob,
			commune_id,
			village_id,
			other,
			telephone,
			gender,
			f_name_khmer,
			f_name_latin,
			f_telephone,
			f_occupation,
			m_name_khmer,
			m_name_latin,
			m_telephone,
			m_occupation,
			c_name_khmer,
			c_name_latin,
			c_telephone,
			c_occupation
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			name_khmer: false,
			name_latin: false,
			province_id: false,
			district_id: false,
			blood_group: blood_group,
			dob: false,
			commune_id: false,
			village_id: false,
			other: false,
			telephone: false,
			patient_id: false,
			gender: false
		};
		if (!name_khmer.trim()) validErr.name_khmer = true;
		if (!name_latin.trim()) validErr.name_latin = true;
		if (!dob.trim()) validErr.dob = true;
		if (!gender.trim()) validErr.gender = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		try {
			await db
				.update(patient)
				.set({
					blood_group: blood_group,
					education: education,
					nation: nation,
					material_status: material_status,
					work_place: work_place,
					occupation: occupation,
					dob: dob,
					gender: gender,
					name_khmer: name_khmer,
					name_latin: name_latin,
					telephone: telephone,
					province_id: Number(province_id),
					district_id: Number(district_id),
					commune_id: Number(commune_id),
					village_id: Number(village_id),
					other: other,
					f_name_khmer: f_name_khmer,
					f_name_latin: f_name_latin,
					f_telephone: f_telephone,
					f_occupation: f_occupation,
					m_name_khmer: m_name_khmer,
					m_name_latin: m_name_latin,
					m_telephone: m_telephone,
					m_occupation: m_occupation,
					c_name_khmer: c_name_khmer,
					c_name_latin: c_name_latin,
					c_telephone: c_telephone,
					c_occupation: c_occupation
				})
				.where(eq(patient.id, +patient_id));
			await fileHandle.auto(body);
		} catch (e) {
			logError({ url, body, err: e });
		}
		redirect(300, '/patient/all');
	}
};
