<script lang="ts">
	import InputDocument from '$lib/coms-form/InputDocument.svelte';
	import type { TAddress, TDocumentSetting, TFields } from '$lib/type';
	import { dobToAge, khmerDate } from '$lib/helper';
	import KhDateInput from '$lib/coms-document/KhDateInput.svelte';
	import Header from '$lib/coms-document/Header.svelte';
	interface Prop {
		p_address?: TAddress;
		p_name_khmer: string;
		p_dob: string;
		p_contact: string;
		p_nation: string;
		p_date_checkup: string;
		fields_: TFields[];
		nations_list: string[];
		title_khm: string;
		title_eng: string;
		get_document_setting?: TDocumentSetting;
		logo: string;
		birth_certificate: string;
		fields_1?: TFields[];
	}
	let {
		p_address: address,
		p_name_khmer,
		p_dob,
		p_contact,
		p_nation,
		nations_list,
		birth_certificate,
		fields_,
		logo,
		get_document_setting,
		p_date_checkup,
		title_eng,
		title_khm,
		fields_1
	}: Prop = $props();
	let fields = $state(fields_.length ? fields_ : (fields_1 ?? []));
	let p_address = $derived(
		`${address?.village?.type ?? ''} ${address?.village?.name_khmer ?? ''} ${address?.commune?.type ?? ''} ${address?.commune?.name_khmer ?? ''} ${address?.district?.type ?? ''} ${address?.district?.name_khmer ?? ''} ${address?.provice?.type ?? ''} ${address?.provice?.name_khmer ?? ''}`
	);
	let n = $derived(fields.find((e) => e.name === 'n')?.result ?? '');
	let date_1 = $derived(
		fields.find((e) => e.name === 'date_1')?.result ??
			fields.find((e) => e.name === 'date_1')?.result ??
			''
	);
	let date_2 = $derived(
		fields.find((e) => e.name === 'date_2')?.result ??
			fields.find((e) => e.name === 'date_2')?.result ??
			''
	);
	let date_of_arrival = $derived(fields.find((e) => e.name === 'date_of_arrival')?.result ?? '');
	let date_of_leave = $derived(fields.find((e) => e.name === 'date_of_leave')?.result ?? '');
	let transfer = $derived(fields_.find((e) => e.name === 'transfer')?.result ?? '');
	let grade = $derived(fields_.find((e) => e.name === 'grade')?.result ?? '');
	let baby_birth_weight = $derived(
		fields_.find((e) => e.name === 'baby_birth_weight')?.result ?? ''
	);
	let father_name = $derived(fields.find((e) => e.name === 'father_name')?.result ?? '');
	let father_dob = $state(fields.find((e) => e.name === 'father_dob')?.result ?? '');
	let father_nation = $derived(fields.find((e) => e.name === 'father_nation')?.result ?? '');
	let mother_age = $derived(dobToAge(p_dob, p_date_checkup));
	let father_age = $derived(dobToAge(father_dob, p_date_checkup));
	let child_name = $derived(fields_.find((e) => e.name === 'child_name')?.result ?? '');
	let child_gender = $derived(fields_.find((e) => e.name === 'child_gender')?.result ?? '');
	let child_dob = $state(fields.find((e) => e.name === 'child_dob')?.result ?? '');
	let child_nation = $derived(fields.find((e) => e.name === 'child_nation')?.result ?? '');
	let contact = $derived(fields.find((e) => e.name === 'contact')?.result ?? '');
</script>

<input type="hidden" name="title" value={birth_certificate} />
<main style="max-width: 1200px;">
	<Header {get_document_setting} {logo} {n} {title_eng} {title_khm} />
	<div class="text-center pb-2 pt-0">
		<h4
			style="color: {get_document_setting?.title_color}"
			class="kh_font_muol_light text-decoration-underline"
		>
			លិខិតបញ្ជាក់ពីការសម្រាលកូន
		</h4>
	</div>

	<div class="section fs-5">
		<div class="mb-2">
			<table class="table table-bordered" border="1" cellspacing="0" cellpadding="5">
				<tbody>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>នាម និងគោត្តនាម <br /> Name and first name</td
						>
						<td class="text-start" style="width:250px;">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								value={child_name}
								class="border-0"
								name="child_name"
								width="80%"
								type="text"
							/>
						</td>
						<td
							style="color: {get_document_setting?.text_body_color}"
							colspan="2"
							class="text-center">ភេទ/Sex</td
						>
						<td class="text-center" style="width:250px;">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								value={child_gender}
								class="border-0"
								name="child_gender"
								width="100%"
								type="text"
								data_list={['ប្រុស', 'ស្រី']}
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">សញ្ជាតិ/Nationality</td>
						<td class="text-center" colspan="4">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								data_list={nations_list}
								value={child_nation}
								class="border-0"
								name="child_nation"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>ទីកន្លែងសម្រាល/Place of birth</td
						>
						<td
							style="color: {get_document_setting?.text_body_color}"
							class="text-center"
							colspan="4"
						>
							{title_khm}
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>ថ្ងៃខែឆ្នាំកំណើត/Date of birth</td
						>
						<td class="text-center" colspan="4">
							<KhDateInput
								style="color: {get_document_setting?.text_input_color}"
								date={child_dob}
								name="child_dob"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>ថ្ងៃចូល <br /> Date of arrival</td
						>
						<td>
							<KhDateInput
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								date={date_of_arrival}
								name="date_of_arrival"
							/>
						</td>
						<td
							style="color: {get_document_setting?.text_body_color}"
							class="text-center"
							colspan="2">ថ្ងៃចេញ <br /> Date of leave</td
						>
						<td>
							<KhDateInput
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								date={date_of_leave}
								name="date_of_leave"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>ប្រភេទនៃការសម្រាល <br /> Type of Delivery</td
						>
						<td colspan="4">
							<div class="row justify-content-between mx-2">
								<div class="col-auto">
									<input
										checked={fields.some((e) => e.result === 'normal')}
										class="form-check-input"
										type="checkbox"
										id="normal"
										name="normal"
										value="normal"
									/>
									<label for="normal">ធម្មតា/Normal</label>
								</div>

								<div class="col-auto">
									<input
										checked={fields.some((e) => e.result === 'section')}
										class="form-check-input"
										type="checkbox"
										id="section"
										name="section"
										value="section"
									/>
									<label for="section">វះកាត់/Section</label>
								</div>
								<div class="col-auto">
									<input
										checked={fields.some((e) => e.result === 'difficulty')}
										class="form-check-input"
										type="checkbox"
										id="difficulty"
										name="difficulty"
										value="difficulty"
									/>
									<label for="difficulty">សម្រាលដោយលំបាក/Difficulty</label>
								</div>
							</div>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>សុខភាពទារកក្រោយសម្រាល <br /> Baby's health</td
						>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center"
							>ពិន្ទុ/Grade <InputDocument
								style="color: {get_document_setting?.text_input_color}"
								value={grade}
								class="border-0"
								name="grade"
								width="100%"
								type="text"
							/></td
						>
						<td
							style="color: {get_document_setting?.text_body_color}"
							colspan="2"
							class="text-center">បញ្ជូនទារក <br /> Transfer:</td
						>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								value={transfer}
								class="border-0"
								name="transfer"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>ទម្ងន់ទារកពេលសម្រាល <br /> Baby's birth width</td
						>
						<td style="color: {get_document_setting?.text_body_color}" colspan="4">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								value={baby_birth_weight}
								class="border-0 text-end"
								name="baby_birth_weight"
								width="300px"
								type="text"
							/> គីឡូក្រាម
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">
							<p>ឪពុក/ម្តាយ/Father/Mother</p>
						</td>
						<td
							style="color: {get_document_setting?.text_body_color}"
							class="text-center"
							colspan="2"
						>
							ឪពុក/Father
						</td>
						<td
							style="color: {get_document_setting?.text_body_color}"
							class="text-center"
							colspan="2"
						>
							ម្តាយ/Mother
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>នាម និងគោត្តនាម <br /> Name and first name</td
						>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								value={father_name}
								class="border-0"
								name="father_name"
								width="100%"
								type="text"
							/>
						</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={p_name_khmer}
								class="border-0"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>ថ្ងៃខែឆ្នាំកំណើត/Date of birth</td
						>
						<td style="width: 10px;" class="text-center" colspan="2">
							<KhDateInput
								style="color: {get_document_setting?.text_input_color}"
								bind:date={father_dob}
								name="father_dob"
							/>
						</td>
						<td
							style="color: {get_document_setting?.text_input_color}"
							class="text-center"
							colspan="2"
						>
							{khmerDate(p_dob, 'date')}
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">សញ្ជាតិ/Nationality</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								data_list={nations_list}
								value={father_nation}
								class="border-0"
								name="father_nation"
								width="100%"
								type="text"
							/>
						</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={p_nation}
								class="border-0"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">អាយុ/Age</td>
						<td
							style="color: {get_document_setting?.text_input_color}"
							class="text-center"
							colspan="2"
						>
							{father_age}
						</td>
						<td
							style="color: {get_document_setting?.text_input_color}"
							class="text-center"
							colspan="2"
						>
							{mother_age}
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>អាសយដ្ឋានបច្ចុប្បន្ន/Address</td
						>
						<td colspan="4">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={p_address}
								class="border-0"
								width="100%"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}"
							>លេខទូរស័ព្ទទំនាក់ទំនង/Contact</td
						>
						<td colspan="4">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								value={contact || p_contact}
								class="border-0"
								width="100%"
								type="text"
								name="contact"
							/>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<br />
	<div class="text-end fs-5" style="margin-right: 5em;">
		<KhDateInput date={date_1} name="date_1" />
		<div style="padding-right: 55px;font-weight: bold;">គ្រូពេទ្យព្យាបាល</div>
	</div>
	<div style="margin-left: 5em;" class="fs-5">
		<KhDateInput date={date_2} name="date_2" />
		<div style="padding-left: 45px;">បានឃើញ និងឯកភាព</div>
		<div style="padding-left: 20px;font-weight: bold;">ប្រធានគ្លីនិក/ប្រធានមន្ទីរពេទ្យ</div>
	</div>
</main>

<style>
</style>
