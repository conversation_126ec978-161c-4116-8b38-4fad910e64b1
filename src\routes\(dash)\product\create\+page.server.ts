import { db } from '$lib/server/db';
import { product, subUnit, uploads } from '$lib/server/schemas';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { and, eq } from 'drizzle-orm';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { pushProductToExspend } from '$lib/server/models';
import { fileHandle } from '$lib/server/upload';
import logError from '$lib/server/utils/logError';
export const load = (async ({ url, parent }) => {
	await parent();
	const get_currency = await db.query.currency.findFirst({});
	const get_groups = await db.query.group.findMany({
		with: {
			unitsToGroups: {
				with: {
					unit: true
				}
			}
		}
	});
	const product_id = url.searchParams.get('product_id') || '';
	const get_categories = await db.query.category.findMany({});
	const get_units = await db.query.unit.findMany({});
	const get_product = await db.query.product.findFirst({
		where: eq(product.id, +product_id),
		with: {
			subUnit: {
				with: {
					unit: true
				}
			},
			unit: true,
			group: {
				with: {
					unitsToGroups: {
						with: {
							unit: true
						}
					}
				}
			},
			parameter: true,
			category: true
		}
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_id, +product_id), eq(uploads.related_type, 'product'))
	});
	return {
		get_product: {
			...get_product,
			uploads: get_upload
		},
		get_categories,
		get_units,
		get_currency,
		get_groups
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_product: async ({ request, url }) => {
		const body = await request.formData();
		const unit_idd = body.getAll('unit_id_forSubUnit');
		const sub_unit_price = body.getAll('sub_unit_price');
		const sub_unit_qty = body.getAll('sub_unit_qty');
		const {
			name_product,
			price,
			unit_id,
			category_id,
			generic_name,
			group_id,
			exspend_id,
			barcode
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			name_product: false,
			price: false,
			unit_id: false,
			category_id: false
		};
		if (!name_product.trim()) validErr.name_product = true;
		if (isNaN(+price) || !price.trim()) validErr.price = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const datetime = YYYYMMDD_Format.datetime(new Date());
		const create_product: { id: number }[] = await db
			.insert(product)
			.values({
				price: Number(price),
				products: name_product,
				unit_id: +unit_id || undefined,
				category_id: +category_id || undefined,
				group_id: +group_id || undefined,
				generic_name: generic_name,
				barcode: barcode,
				create_at: datetime
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});
		const product_id = create_product.at(0)?.id;
		for (let index = 0; index < unit_idd.length; index++) {
			const sub_unit_id_ = unit_idd[index];
			const sub_unit_price_ = sub_unit_price[index];
			const sub_unit_qty_ = sub_unit_qty[index];
			await db.insert(subUnit).values({
				unit_id: +sub_unit_id_,
				price: +sub_unit_price_,
				qty_per_unit: +sub_unit_qty_,
				product_id: product_id
			});
		}
		// push product to exspend
		if (product_id) {
			await pushProductToExspend({
				product_id: product_id,
				exspend_id: +exspend_id,
				body: body,
				url: url
			});
		}
		await fileHandle.auto(body);
	},
	update_product: async ({ request, url }) => {
		const body = await request.formData();
		const sub_unit_id = body.getAll('sub_unit_id');
		const sub_unit_price = body.getAll('sub_unit_price');
		const unit_idd = body.getAll('unit_id_forSubUnit');
		const sub_unit_qty = body.getAll('sub_unit_qty');
		const {
			name_product,
			price,
			product_id,
			unit_id,
			category_id,
			generic_name,
			group_id,
			from_url,
			barcode
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			name_product: false,
			product_id: false,
			price: false
		};
		if (!name_product.trim()) validErr.name_product = true;
		if (isNaN(+price) || !price.trim()) validErr.price = true;
		if (isNaN(+product_id)) validErr.product_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const get_product = await db.query.product.findFirst({
			where: eq(product.id, +product_id),
			with: {
				subUnit: true
			}
		});

		await db
			.update(product)
			.set({
				price: Number(price),
				products: name_product,
				category_id: +category_id || null,
				unit_id: +unit_id || null,
				group_id: +group_id || null,
				generic_name: generic_name,
				barcode: barcode
			})
			.where(eq(product.id, Number(product_id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await fileHandle.auto(body);
		for (let index = 0; index < sub_unit_id.length; index++) {
			const sub_unit_id_ = sub_unit_id[index];
			const sub_unit_price_ = sub_unit_price[index];
			const sub_unit_qty_ = sub_unit_qty[index];
			const unit_id_ = unit_idd[index];
			const match_sub_unit = get_product?.subUnit.some((e) => e.id === +sub_unit_id_);
			if (match_sub_unit) {
				await db
					.update(subUnit)
					.set({
						unit_id: +unit_id_,
						price: +sub_unit_price_,
						qty_per_unit: +sub_unit_qty_,
						product_id: +product_id
					})
					.where(eq(subUnit.id, +sub_unit_id_))
					.catch((e) => logError({ url, body, err: e }));
			}
			if (!match_sub_unit) {
				await db
					.insert(subUnit)
					.values({
						unit_id: +unit_id_,
						price: +sub_unit_price_,
						qty_per_unit: +sub_unit_qty_,
						product_id: +product_id
					})
					.catch((e) => logError({ url, body, err: e }));
			}
			// delete if sub unit
		}
		if (get_product?.subUnit.length) {
			for (const e of get_product.subUnit) {
				const match_unit = sub_unit_id.includes(String(e.id));
				if (!match_unit) {
					await db
						.delete(subUnit)
						.where(eq(subUnit.id, e.id))
						.catch((e) => logError({ url, body, err: e }));
				}
			}
		}
		redirect(300, `${from_url}`);
	}
};
