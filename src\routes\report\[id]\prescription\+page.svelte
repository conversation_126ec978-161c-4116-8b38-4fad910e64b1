<script lang="ts">
	import { page } from '$app/state';
	import ClinichInfo from '$lib/coms-report/ClinichInfo.svelte';
	import Sign from '$lib/coms-report/Sign.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { dobToAge } from '$lib/helper';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_clinic_info, get_visit, get_upload } = $derived(data);
	let age_p_visit = $derived(dobToAge(get_visit?.patient.dob ?? '', get_visit?.date_checkup ?? ''));
</script>

<div class="header">
	<ClinichInfo data={{ get_clinic_info, get_upload }} />
	<div class="border p-2 pb-0">
		<table class=" table table-sm table-borderless">
			<thead style="font-size: 120%;">
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Khmer Name</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.patient?.name_khmer}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Gender</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.patient?.gender ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">ID</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						<span>PT{get_visit?.patient_id}</span>
						<span>PR{get_visit?.id}</span>
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Latin Name</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.patient?.name_latin}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Age</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						{age_p_visit}
					</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">CheckIn Date</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						<DDMMYYYYFormat date={get_visit?.date_checkup} />
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Consultation</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.staff?.name_latin ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Symptoms</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.etiology ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Phone</td>
					<td class="p-0 m-0"> : </td>
					<td class="en_font_times_new_roman p-0 m-0">{get_visit?.patient?.telephone ?? ''}</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Visit</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_visit?.checkin_type ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Address</td>
					<td class="p-0 m-0"> : </td>
					<td colspan="7" class="kh_font_battambang p-0 m-0">
						{get_visit?.patient?.village?.type ?? ''}
						{get_visit?.patient?.village?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.commune?.type ?? ''}
						{get_visit?.patient?.commune?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.district?.type ?? ''}
						{get_visit?.patient?.district?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.provice?.type ?? ''}
						{get_visit?.patient?.provice?.name_khmer ?? ''}
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Diagnosis (Dx)</td>
					<td class="p-0 m-0"> : </td>
					<td colspan="7" class="kh_font_battambang p-0 m-0"
						>{get_visit?.accessment?.diagnosis_or_problem}</td
					>
				</tr>
			</thead>
		</table>
	</div>
</div>

<table class="w-100 table-light">
	<thead>
		<tr>
			<td>
				<div class="header-space">&nbsp;</div>
			</td>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
				<h4 class="kh_font_muol_light text-center pt-1">{'វេជ្ជបញ្ជា'}</h4>
				<h4 class="en_font_times_new_roman text-center pt-1">Prescription {'(Rx:)'}</h4>
			</td>
		</tr>
		<tr>
			<td>
				<div class="card-body mt-0 pt-0 zoom table-responsive p-0">
					<table class="table mb-0 table-bordered table-hover text-wrap text-break table-light">
						<thead class="">
							<tr class="text-center table-active">
								<th>
									{'ល.រ'}
								</th>
								<th>
									{'ប្រភេទឱសថ'}
								</th>
								<th>
									{'ឈ្មោះឱសថ'}
								</th>
								<th>
									{'របៀបប្រើ'}
								</th>
								<th>
									{'ពេលវេលាប្រើ'}
								</th>
								<th>
									{'រយៈពេល'}
								</th>
								<th>
									{'ចំនួន'}
								</th>
							</tr>
						</thead>
						<tbody>
							{#each get_visit?.presrciption || [] as item, i}
								<tr class="text-center">
									<td>{i + 1}</td>
									<td>{item?.product?.group?.name ?? ''}</td>
									<td class="text-start">
										<span>{item.product?.products ?? ''}</span>
										{#if item.product?.generic_name}
											<br />
											<span class="">
												{`(${item.product?.generic_name ?? ''})`}
											</span>
										{/if}
									</td>
									<td>{item?.use}</td>
									<td class="text-start">
										<div>
											<span class="">
												{#if item.morning !== 0}
													ពេលព្រឹក {item.morning},
												{/if}
											</span>
											<span class="">
												{#if item.noon !== 0}
													ពេលថ្ងៃត្រង់ {item.noon},
												{/if}
											</span>
											<span class="">
												{#if item.afternoon !== 0}
													ពេលរសៀល {item.afternoon},
												{/if}
											</span>
											<span class="">
												{#if item.evening !== 0}
													ពេលល្ងាច {item.evening},
												{/if}
											</span>
											<span class="">
												{#if item.night !== 0}
													ពេលយប់ {item.night}
												{/if}
											</span>
										</div>
									</td>
									<td>{item?.duration}</td>
									<td>
										{item?.amount}
										{item.product?.unit?.unit ?? ''}
									</td>
								</tr>
							{/each}
							<tr class="text-center table-active">
								<td colspan="7">
									{'ឱសថសរុបចំនួន'}
									{get_visit?.presrciption?.length ?? 0}
									{'មុខ។'}
								</td>
							</tr>
						</tbody>
					</table>
					<br />
					{#if get_visit?.adviceTeaching}
						<h5 class="text-break">
							Physician's advice or teaching : <br />
							{get_visit?.adviceTeaching?.description}
						</h5>
					{/if}
					{#if get_visit?.appointment}
						<hr />
						<h5>
							Appointment {'(ណាត់ជួប)'}
							<i class="text-decoration-underline">
								<DDMMYYYYFormat date={get_visit?.appointment?.datetime} />
							</i>
							{get_visit?.appointment?.description}
						</h5>
					{/if}
				</div>
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td>
				<div class="footer-space">&nbsp;</div>
			</td>
		</tr>
	</tfoot>
</table>

<div class="footer">
	<Sign
		qr={page.url.href}
		right={{
			date: get_visit?.date_checkup,
			img: '/sign.png',
			name: get_visit?.staff?.name_khmer,
			role: `Physician'sign`
		}}
	/>

	<div>
		<hr />
		<h6 style="color:#0000FF" class="text-center">
			{get_clinic_info?.address ?? ''}
		</h6>
	</div>
</div>

<style>
	@media print {
		.footer,
		.footer-space {
			height: 330px;
		}
	}
</style>
