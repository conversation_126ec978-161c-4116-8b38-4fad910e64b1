<script lang="ts">
	import { invalidateAll } from '$app/navigation';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	let loading = $state(false);
	interface Props {
		header?: string;
		action: string;
		modal_id: string;
		children?: import('svelte').Snippet;
		body: { name: string; value: string }[];
		method?: 'POST' | 'DELETE' | 'PUT' | 'GET';
	}

	let { action, modal_id, children, header, body, method = 'POST' }: Props = $props();
	let uid = $derived(Math.random().toString(36).substring(2, 9));
	async function onclick() {
		store.globalLoading = true;
		const formData = new FormData();
		for (const item of body) {
			if (item.name && item.value) {
				formData.append(item.name, item.value);
			}
		}
		await fetch(action, {
			method: method,
			body: formData
		});
		await invalidateAll();
		loading = false;
		store.globalLoading = false;
		document.getElementById('id'.concat(uid))?.click();
	}
</script>

<div class="modal fade" id={modal_id} data-bs-backdrop="static">
	<div class="modal-dialog modal-md">
		<div class="modal-content rounded-3 shadow">
			<div class="modal-header py-2 justify-content-center text-bg-danger">
				{#if children}
					<span class="fs-3">
						{header}
					</span>
				{:else}
					<span class="fs-3">
						<i class="fa-solid fa-trash-can"></i>
						{locale.T('delete_')}
					</span>
				{/if}
			</div>
			<div class="modal-body">
				{#if children}
					{@render children?.()}
				{:else}
					<ul class="list-group">
						<li class="list-group-item">
							<i class="fa-solid fa-triangle-exclamation"></i>
							{locale.T('confirm_delete')}
						</li>
					</ul>
				{/if}
			</div>
			<div class="modal-footer flex-nowrap p-0">
				<button
					id={'id' + uid}
					type="button"
					class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
					data-bs-dismiss="modal">{locale.T('no')}</button
				>
				<button
					{onclick}
					disabled={loading}
					type="button"
					class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
				>
					<strong>{locale.T('yes')}</strong>
				</button>
			</div>
		</div>
	</div>
</div>
