<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_currency, get_setting, get_document_setting } = $derived(data);
	let loading = $state(false);
</script>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('setting')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/setting" class="btn btn-link p-0 text-secondary">
					<i class="fa-solid fa-gear"></i>
					{locale.T('setting')}
				</a>
			</li>
		</ol>
	</div>
</div>
<div class="card bg-light border border-2 border-primary-subtle">
	<div class="card-header bg-primary-subtle">
		<span class="fs-5"><i class="fa-solid fa-dollar-sign"></i> {locale.T('currency')}</span>
	</div>
	<Form method="post" reset={false} bind:loading class="card-body" action="?/create_currency">
		<input value={get_currency?.id ?? ''} type="hidden" name="currency_id" />
		<div class="row">
			<div class="col-sm-2">
				<label class="form-label" for="currency">{locale.T('currency')}</label>
				<input
					value={get_currency?.currency ?? ''}
					class="form-control"
					type="text"
					name="currency"
					id="currency"
				/>
			</div>
			<div class="col-sm-2">
				<label class="form-label" for="currency_rate">{locale.T('currency_rate')}</label>
				<input
					value={get_currency?.currency_rate ?? ''}
					class="form-control"
					type="text"
					name="currency_rate"
					id="currency_rate"
				/>
			</div>
			<div class="col-sm-2">
				<label class="form-label" for="exchang_to">{locale.T('exchange_to')}</label>
				<input
					value={get_currency?.exchang_to ?? ''}
					class="form-control"
					type="text"
					name="exchang_to"
					id="exchang_to"
				/>
			</div>
			<div class="col-sm-2">
				<label class="form-label" for="exchang_rate">{locale.T('exchange_rate')}</label>
				<input
					value={get_currency?.exchang_rate ?? ''}
					class="form-control"
					type="text"
					name="exchang_rate"
					id="exchang_rate"
				/>
			</div>
			<div class="col-sm-4">
				<label class="form-label text-success" for="exchang_rate">{locale.T('note')}</label>
				<div class="input-group">
					<span class="input-group-text w-100"
						>{get_currency?.exchang_rate ?? ''}
						{get_currency?.exchang_to ?? ''}
						=
						{get_currency?.currency_rate ?? ''}
						{get_currency?.currency ?? ''}
					</span>
				</div>
			</div>
		</div>
		<div class=" text-end mt-3">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
<br />
<!-- <div class="card bg-light border border-2 border-primary-subtle">
	<div class="card-header bg-primary-subtle">
		<span class="fs-5"><i class="fa-solid fa-gears"></i> {locale.T('setting')}</span>
	</div>
	<Form
		onchange={(e) => {
			e.currentTarget.requestSubmit();
		}}
		method="post"
		reset={false}
		bind:loading
		class="card-body"
		action="?/setting"
	>
		<input value={get_setting?.id ?? ''} type="hidden" name="setting_id" />
		<div class="form-check form-switch">
			<input
				checked={get_setting?.print_bill}
				class="form-check-input"
				name="print_bill"
				type="checkbox"
				role="switch"
				id="printbilling"
			/>
			<label class="form-check-label" for="printbilling">Print after billing</label>
		</div>

		<div class="text-end pt-2">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
<br /> -->
<div class="card bg-light border border-2 border-primary-subtle">
	<div class="card-header bg-primary-subtle fs-5">
		<i class="fa-solid fa-file-word"></i>
		{locale.T('document_setting')}
	</div>
	<Form
		onchange={(e) => {
			e.currentTarget.requestSubmit();
		}}
		method="post"
		reset={false}
		bind:loading
		class="card-body"
		action="?/setting_document"
	>
		<input value={get_document_setting?.id ?? ''} type="hidden" name="document_setting_id" />
		<div class="row">
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="logo_size">{locale.T('logo_size')}</label>
				<div class="input-group">
					<input
						value={get_document_setting?.logo_size ?? ''}
						class="form-control"
						type="text"
						name="logo_size"
						id="logo_size"
					/>
					<span class="input-group-text">px</span>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="header_size">{locale.T('header_size')}</label>
				<div class="input-group">
					<input
						value={get_document_setting?.header_size ?? ''}
						class="form-control"
						type="text"
						name="header_size"
						id="header_size"
					/>
					<span class="input-group-text">px</span>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="header_color">{locale.T('header_color')}</label>
				<div class="input-group">
					<input
						value={get_document_setting?.header_color ?? ''}
						class="form-control form-control-color"
						type="color"
						name="header_color"
						id="header_color"
					/>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="title_size">{locale.T('title_size')}</label>
				<div class="input-group">
					<input
						value={get_document_setting?.title_size ?? ''}
						class="form-control"
						type="text"
						name="title_size"
						id="title_size"
					/>
					<span class="input-group-text">px</span>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="title_color">{locale.T('title_color')}</label>
				<div class="input-group">
					<input
						value={get_document_setting?.title_color ?? ''}
						class="form-control form-control-color"
						type="color"
						name="title_color"
						id="title_color"
					/>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="clinic_title_kh_size"
					>{locale.T('clinic_title_kh_size')}</label
				>
				<div class="input-group">
					<input
						value={get_document_setting?.clinic_title_kh_size ?? ''}
						class="form-control"
						type="text"
						name="clinic_title_kh_size"
						id="clinic_title_kh_size"
					/>
					<span class="input-group-text">px</span>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="clinic_title_kh_color"
					>{locale.T('clinic_title_kh_color')}</label
				>
				<div class="input-group">
					<input
						value={get_document_setting?.clinic_title_kh_color ?? ''}
						class="form-control form-control-color"
						type="color"
						name="clinic_title_kh_color"
						id="clinic_title_kh_color"
					/>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="clinic_title_en_size"
					>{locale.T('clinic_title_en_size')}</label
				>
				<div class="input-group">
					<input
						value={get_document_setting?.clinic_title_en_size ?? ''}
						class="form-control"
						type="text"
						name="clinic_title_en_size"
						id="clinic_title_en_size"
					/>
					<span class="input-group-text">px</span>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="clinic_title_en_color">
					{locale.T('clinic_title_en_color')}
				</label>
				<div class="input-group">
					<input
						value={get_document_setting?.clinic_title_en_color ?? ''}
						class="form-control form-control-color"
						type="color"
						name="clinic_title_en_color"
						id="clinic_title_en_color"
					/>
				</div>
			</div>

			<div class="col-sm-3 pb-2">
				<label class="form-label" for="text_body_color">{locale.T('text_color')}</label>
				<div class="input-group">
					<input
						value={get_document_setting?.text_body_color ?? ''}
						class="form-control form-control-color"
						type="color"
						name="text_body_color"
						id="text_body_color"
					/>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="text_input_color">{locale.T('text_input_color')}</label>
				<div class="input-group">
					<input
						value={get_document_setting?.text_input_color ?? ''}
						class="form-control form-control-color"
						type="color"
						name="text_input_color"
						id="text_input_color"
					/>
				</div>
			</div>
			<div class="col-sm-3 pb-2">
				<label class="form-label" for="footer_color">{locale.T('footer_color')}</label>
				<div class="input-group">
					<input
						value={get_document_setting?.footer_color ?? ''}
						class="form-control form-control-color"
						type="color"
						name="footer_color"
						id="footer_color"
					/>
				</div>
			</div>
		</div>
		<div class="text-end pt-2">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
<br />
