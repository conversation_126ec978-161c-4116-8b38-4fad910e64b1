import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { hash, verify } from '@node-rs/argon2';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import {
	commune,
	district,
	uploads,
	product,
	staff,
	staffToDemartment,
	staffToRole,
	user,
	village
} from '$lib/server/schemas';
import logError from '$lib/server/utils/logError';
import { and, eq } from 'drizzle-orm';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { deleteFile } from '$lib/server/upload/fileHandle';
import { permision } from '$lib/server/auth/permision';
import { fileHandle } from '$lib/server/upload';
export const load: PageServerLoad = async ({ parent, url, locals }) => {
	await parent();
	const staff_id = url.searchParams.get('staff_id') ?? '';
	const province_id = url.searchParams.get('province_id') ?? '';
	const district_id = url.searchParams.get('district_id') ?? '';
	const commune_id = url.searchParams.get('commune_id') ?? '';
	permision.go({
		locals,
		staff_id: +staff_id,
		redirect_: '/staff'
	});
	const get_provinces = await db.query.provice.findMany({});
	const get_districts = await db.query.district.findMany({
		where: eq(district.province_id, Number(province_id))
	});
	const get_conmunies = await db.query.commune.findMany({
		where: eq(commune.district_id, Number(district_id))
	});
	const get_vilages = await db.query.village.findMany({
		where: eq(village.commune_id, Number(commune_id))
	});
	const get_staff = await db.query.staff.findFirst({
		where: eq(staff.id, +staff_id),
		with: {
			user: true,
			commune: true,
			district: true,
			title: true,
			village: true,
			provice: true,
			staffToRole: {
				with: {
					role: true
				}
			},
			staffToDemartment: {
				with: {
					department: true
				}
			}
		}
	});
	const get_roles = await db.query.role.findMany({});
	const get_products_department = await db.query.product.findMany({
		where: eq(product.category_id, 11)
	});
	const get_titles = await db.query.title.findMany({});
	const get_designations = await db.query.designation.findMany({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staff'), eq(uploads.related_id, +staff_id))
	});
	const get_upload_sign = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staffSign'), eq(uploads.related_id, +staff_id))
	});
	return {
		get_provinces,
		get_districts,
		get_conmunies,
		get_vilages,
		get_titles,
		get_staff: {
			...get_staff,
			uploads: get_upload,
			sign: get_upload_sign
		},
		get_roles,
		get_products_department,
		locals: locals,
		get_designations
	};
};

export const actions: Actions = {
	create_staff: async ({ request, url }) => {
		const body = await request.formData();
		const {
			staff_id,
			datetime_stop,
			id_staff,
			designation_id,
			name_khmer,
			name_latin,
			blood_group,
			title_id,
			telephone,
			specialist,
			gender,
			dob,
			datetime_start,
			province_id,
			district_id,
			commune_id,
			village_id
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			name_khmer: false,
			name_latin: false,
			telephone: false,
			specialist: false,
			gender: false,
			dob: false,
			datetime_start: false,
			province_id: false,
			district_id: false,
			commune_id: false,
			village_id: false,
			designation_id: false,
			title_id: false
		};
		const role_id = body.getAll('role_id');
		const department_id = body.getAll('department_id');
		if (!name_khmer.trim()) validErr.name_khmer = true;
		if (!name_latin.trim()) validErr.name_latin = true;
		if (!title_id.trim()) validErr.title_id = true;
		if (!designation_id.trim()) validErr.designation_id = true;
		if (!telephone.trim()) validErr.telephone = true;
		if (!specialist.trim()) validErr.specialist = true;
		if (!gender.trim()) validErr.gender = true;
		if (!dob.trim()) validErr.dob = true;
		if (!datetime_start.trim()) validErr.datetime_start = true;
		if (!province_id.trim()) validErr.province_id = true;
		if (!district_id.trim()) validErr.district_id = true;
		if (!commune_id.trim()) validErr.commune_id = true;
		if (!village_id.trim()) validErr.village_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		let staff_id_ = staff_id;
		if (!staff_id_) {
			const create_staff: { id: number }[] = await db
				.insert(staff)
				.values({
					id_staff: id_staff,
					name_khmer: name_khmer,
					name_latin: name_latin,
					telephone: telephone,
					specialist: specialist,
					gender: gender,
					designation_id: +designation_id,
					blood_group: blood_group,
					dob: dob,
					title_id: +title_id,
					datetime_start: YYYYMMDD_Format.datetime(datetime_start),
					province_id: Number(province_id),
					district_id: Number(district_id),
					commune_id: Number(commune_id),
					village_id: Number(village_id)
				})
				.$returningId();
			staff_id_ = create_staff[0].id.toString();
			redirect(
				303,
				`?staff_id=${staff_id_}&province_id=${province_id}&district_id=${district_id}&commune_id=${commune_id}`
			);
		} else {
			await db
				.update(staff)
				.set({
					id_staff: id_staff,
					name_khmer: name_khmer,
					name_latin: name_latin,
					title_id: +title_id,
					telephone: telephone,
					datetime_stop: datetime_stop ? YYYYMMDD_Format.datetime(datetime_stop) : null,
					specialist: specialist,
					gender: gender,
					designation_id: +designation_id,
					blood_group: blood_group,
					dob: dob,
					datetime_start: YYYYMMDD_Format.datetime(datetime_start),
					province_id: Number(province_id),
					district_id: Number(district_id),
					commune_id: Number(commune_id),
					village_id: Number(village_id)
				})
				.where(eq(staff.id, +staff_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		const get_staff = await db.query.staff.findFirst({
			where: eq(staff.id, +staff_id_),
			with: {
				staffToRole: true,
				staffToDemartment: true
			}
		});
		//emplement staff to role
		for (const e of role_id || []) {
			if (!get_staff?.staffToRole.some((ee) => ee.role_id === +e)) {
				await db.insert(staffToRole).values({
					staff_id: +staff_id_,
					role_id: +e
				});
			}
		}
		for (const e of get_staff?.staffToRole || []) {
			if (!role_id.some((ee) => +ee === e.role_id)) {
				if (e.role_id && e.staff_id) {
					await db
						.delete(staffToRole)
						.where(and(eq(staffToRole.staff_id, +staff_id_), eq(staffToRole.role_id, +e.role_id)));
				}
			}
		}
		// emplement staff to departments
		for (const e of department_id || []) {
			if (!get_staff?.staffToDemartment.some((ee) => ee.department_id === +e)) {
				await db.insert(staffToDemartment).values({
					staff_id: +staff_id_,
					department_id: +e
				});
			}
		}
		for (const e of get_staff?.staffToDemartment || []) {
			if (!department_id?.some((ee) => +ee === e.department_id)) {
				if (e?.department_id && e.staff_id) {
					await db
						.delete(staffToDemartment)
						.where(
							and(
								eq(staffToDemartment.staff_id, +staff_id_),
								eq(staffToDemartment.department_id, +e.department_id)
							)
						);
				}
			}
		}
		await fileHandle.auto(body);
		// if (image.size) {
		// 	if (old_image) {
		// 		await fileHandle.update(image, old_image, +staff_id_, 'staff');
		// 	} else {
		// 		await fileHandle.insert(image, +staff_id_, 'staff');
		// 	}
		// }
	},
	register: async ({ request, url }) => {
		const body = await request.formData();
		const username = body.get('username') as Record<string, string>['username'];
		const password = body.get('password');
		const staff_id = body.get('staff_id');
		const find_user = await db.query.user.findFirst({
			where: eq(user.username, username)
		});
		if (!validateUsername(username) || find_user) {
			return fail(400, { message: 'Invalid username' });
		}
		if (!validatePassword(password)) {
			return fail(400, { message: 'Invalid password' });
		}
		if (!staff_id) {
			return fail(400, { message: 'Invalid role or staff' });
		}
		// if (locals.user?.staff !== 'ADMIN' && locals.user?.staff_id !== +staff_id)
		// 	return fail(400, { message: 'Permission denied' });
		const userId = generateUserId();
		const passwordHash = await hash(password, {
			// recommended minimum parameters
			memoryCost: 19456,
			timeCost: 2,
			outputLen: 32,
			parallelism: 1
		});
		try {
			await db.insert(user).values({
				id: userId,
				username,
				password_hash: passwordHash,
				staff_id: +staff_id
			});
			// const sessionToken = auth.generateSessionToken();
			// const session = await auth.createSession(sessionToken, userId);
			// auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
		} catch (e) {
			logError({ url, body, err: e });
			return fail(500, { message: 'An error has occurred' });
		}
		redirect(303, '?');
	},
	update_user: async ({ request, locals, url }) => {
		const body = await request.formData();
		const username = body.get('username')?.toString();
		const password = body.get('password')?.toString() ?? '';
		const confirm_password = body.get('confirm_password')?.toString() ?? '';
		const user_id = body.get('user_id')?.toString() ?? '';
		const find_user = await db.query.user.findFirst({
			where: eq(user.id, user_id)
		});
		const password_hash = find_user?.password_hash || '';
		if (!validateUsername(username)) {
			return fail(400, { username: 'Invalid username' });
		}
		if (validatePassword(password)) {
			return fail(400, { password: 'Invalid password' });
		}
		if (!locals?.roles?.some((e) => e.role?.toLowerCase()?.includes('admin'))) {
			const validPassword = await verify(password_hash, confirm_password, {
				memoryCost: 19456,
				timeCost: 2,
				outputLen: 32,
				parallelism: 1
			});
			if (!validPassword) {
				return fail(400, { confirm_password: 'wrong password' });
			}
		}
		// if (locals.user?.staff !== 'ADMIN' && locals.user?.staff_id !== +staff_id)
		// 	return fail(400, { message: 'Permission denied' });
		const passwordHash = await hash(password, {
			// recommended minimum parameters
			memoryCost: 19456,
			timeCost: 2,
			outputLen: 32,
			parallelism: 1
		});
		try {
			await db
				.update(user)
				.set({
					username,
					password_hash: passwordHash
				})
				.where(eq(user.id, user_id));
			// const sessionToken = auth.generateSessionToken();
			// const session = await auth.createSession(sessionToken, userId);
			// auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
		} catch (e) {
			logError({ url, body, err: e });
			return fail(500, { message: 'An error has occurred' });
		}
	},
	delete_staff: async ({ request, url }) => {
		const body = await request.formData();
		const { id, image } = Object.fromEntries(body) as Record<string, string>;
		try {
			await db.delete(staff).where(eq(staff.id, Number(id)));
			await deleteFile(image);
		} catch (e) {
			logError({ url, body, err: e });
			return fail(500, { message: 'An error has occurred' });
		}
		redirect(303, '/staff');
	}
};

function generateUserId() {
	// ID with 120 bits of entropy, or about the same as UUID v4.
	const bytes = crypto.getRandomValues(new Uint8Array(15));
	const id = encodeBase32LowerCase(bytes);
	return id;
}

function validateUsername(username: unknown): username is string {
	return (
		typeof username === 'string' &&
		username.length >= 3 &&
		username.length <= 31 &&
		/^[a-z0-9_-]+$/.test(username)
	);
}

function validatePassword(password: unknown): password is string {
	return typeof password === 'string' && password.length >= 6 && password.length <= 255;
}
