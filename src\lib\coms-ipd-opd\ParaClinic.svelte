<script lang="ts">
	import type { PageServerData } from '../../routes/(dash)/ipd/[progress_note_id]/progress-note/$types';
	import Athtml from '$lib/coms/Athtml.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	type V = PageServerData['get_progress_note']['visit'];
	type VV = NonNullable<V>[number];
	interface Props {
		find_old_visit: VV;
	}

	let { find_old_visit }: Props = $props();
	let sort_laboraytor = $derived(
		find_old_visit?.laboratoryRequest.sort((a) => {
			if (a.product?.products.includes('CBC')) return -1;
			else return 1;
		})
	);
</script>

<div>
	{#if sort_laboraytor?.length}
		<button class="btn btn-success btn-sm mb-2 py-0">{locale.T('laboratory')}</button>
	{/if}
	{#each sort_laboraytor || [] as laboratory_request (laboratory_request.id)}
		{@const laboratory_results = laboratory_request.laboratoryResult}
		{@const parameters = laboratory_request.product?.parameter}
		<div class="border rounded border-1 p-2 mb-2">
			<span class="fs-6 text-decoration-underline text-primary"
				>{laboratory_request.product?.products ?? ''}</span
			>
			<table class="table-sm table table-light">
				<thead>
					{#each parameters || [] as parameter (parameter.id)}
						<tr>
							<td style="width: 40%;"> {parameter.parameter ?? ''}</td>
							<td style="width: 5%;">:</td>
							{#each laboratory_results as laboratory_result (laboratory_result.id)}
								{#if laboratory_result.parameter_id === parameter.id}
									<td style="width: 20%;">
										{#if laboratory_result.result === 'Positive' || laboratory_result.result === '1/160' || laboratory_result.result === '1/320' || laboratory_result.result === '+' || laboratory_result.result === '++' || laboratory_result.result === '+++' || laboratory_result.result === '++++'}
											<span style="color: #FF0000;">
												{laboratory_result.result}
											</span>
										{:else if Number(laboratory_result.result) >= Number(parameter?.mini) && Number(laboratory_result.result) <= Number(parameter?.maxi)}
											<span>{laboratory_result.result}</span>
										{:else if Number(laboratory_result.result) < Number(parameter?.mini)}
											<span style="color: #0000FF;">{laboratory_result.result} L</span>
										{:else if Number(laboratory_result.result) > Number(parameter?.maxi)}
											<span style="color: #FF0000;">{laboratory_result.result} H</span>
										{:else}
											<span style="color: #0000FF;">
												{laboratory_result.result ?? ''}
											</span>
										{/if}
									</td>
								{/if}
							{/each}

							<td style="width: 15%;">
								<Athtml html={parameter.paraUnit?.unit ?? ''} />
							</td>

							<td style="width: 20%;">
								{parameter.mini === 0 ? '' : `( ${parameter.mini}`}
								{parameter.sign ?? ''}
								{parameter.maxi === 0 ? '' : `${parameter.maxi} )`}
							</td>
						</tr>
					{/each}
				</thead>
			</table>
		</div>
	{/each}
	{#if find_old_visit?.imagerieRequest.length}
		<div class="border rounded border-1 p-2 mb-2">
			<span class="btn btn-success btn-sm mb-2 py-0">{locale.T('imagerie')}</span>
			<table class="table table-sm table-light">
				<thead>
					{#each find_old_visit?.imagerieRequest as imagerie_request (imagerie_request.id)}
						<tr>
							<td style="width: 40%;">
								<span>{imagerie_request.product?.products ?? ''}</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;">
								<a target="_blank" href="/report/{imagerie_request.id}/imagerie"
									>{locale.T('view')}</a
								>
							</td>
						</tr>
					{/each}
				</thead>
			</table>
		</div>
	{/if}
</div>
