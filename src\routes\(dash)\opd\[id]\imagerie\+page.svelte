<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let loading = $state(false);
	let { get_imageerie_groups, get_visit, get_currency } = $derived(data);
	let total_imagerie = $derived(
		get_visit?.billing?.charge.find((e) => e.charge_on === 'imagerie')?.price
	);
</script>

<hr />
<div class="row pb-2">
	<div class="col">
		<a
			target="_blank"
			aria-label="nersing_process"
			href="/print/{get_visit?.id}/imagrie-req"
			class="btn btn-success btn-sm float-end"
			><i class="fa-solid fa-print"></i>
		</a>
	</div>
</div>
<fieldset disabled={get_visit?.billing?.status !== 'checking'}>
	<Form
		onchange={(e) => e.currentTarget.requestSubmit()}
		reset={false}
		bind:loading
		action="?/create_imagerie_request"
		method="post"
		class="row"
	>
		{#each get_imageerie_groups as item}
			{@const products = item.product}
			<div class="col-md-3 pb-2">
				<div class="card bg-light h-100">
					<div class="card-header fs-5 text-center">
						<span>{item.name}</span>
					</div>
					<div class="card-body">
						{#each products as iitem}
							<div class="alert alert-primary py-1">
								<div class="form-check">
									<input
										class="form-check-input"
										type="checkbox"
										name="product_id"
										checked={get_visit?.imagerieRequest.some((e) => e.product_id === iitem.id)}
										id={iitem.id.toString()}
										value={iitem.id}
									/>
									<label for={iitem.id.toString()} class="text-break">{iitem.products}</label>
									<Currency amount={iitem.price} symbol={get_currency?.currency} />
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>
		{/each}
		<div class="card-footer row bg-light p-2 sticky-bottom">
			<div class="col text-end">
				<button type="button" class="btn btn-warning"
					>Total Imagerie
					<Currency class="fs-6" amount={total_imagerie || 0} symbol={get_currency?.currency} />
				</button>
			</div>
		</div>
	</Form>
</fieldset>
