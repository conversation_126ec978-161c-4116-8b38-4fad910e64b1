<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import Confirm from '$lib/coms/Confirm.svelte';

	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_billing, get_payment_types, get_currency, patient_info } = $derived(data);
	let loading = $state(false);
	let payment_id = $state<number | undefined>(undefined);
	let payment_id_delete = $state<number | undefined>(undefined);
	let balance = $derived(get_billing?.balance);
	let pay_1 = $state(0);
	let get_payment = $derived(get_billing?.payment?.find((e) => e.id === payment_id));
	let pay: number = $derived.by(() => {
		if (get_payment) {
			return Number(get_payment?.value);
		} else if (balance) {
			return Number(balance);
		} else {
			return 0;
		}
	});
	// $effect(() => {
	// 	pay = Number(balance);
	// });

	let total_pay = $derived(
		pay + (pay_1 * Number(get_currency?.currency_rate)) / Number(get_currency?.exchang_rate)
	);
	// $effect(() => {
	// 	if (get_payment) {
	// 		pay = Number(get_payment?.value);
	// 	} else {
	// 		pay = Number(balance);
	// 	}
	// });
</script>

<div class="row">
	<div class="col-sm-6">
		<a href="/billing/report" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/billing/opd" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-money-bills"></i>
					{locale.T('billing')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-stethoscope"></i>
					{locale.T('opd')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-stethoscope"></i>
					{locale.T('payment')}
				</a>
			</li>
		</ol>
	</div>
</div>

<PatientInfo {patient_info} />

<div class="card bg-light mt-2 border-2 border-primary">
	<div class="row card-body">
		<div class="col-5">
			<fieldset disabled={payment_id ? false : Number(balance) <= 0 ? true : false}>
				<Form
					fnSuccess={() => {
						payment_id = undefined;
					}}
					enctype="multipart/form-data"
					action="?/repayment"
					method="post"
					bind:loading
					reset={true}
				>
					<input type="hidden" name="billing_id" value={get_billing?.id} />
					<input type="hidden" name="disc" value={get_billing?.discount} />
					<input type="hidden" name="payment_id" value={payment_id} />
					<input type="hidden" name="tax" value={get_billing?.tax} />

					<div class={payment_id ? 'alert alert-warning' : 'alert alert-primary'}>
						<div class="row">
							<div class="col">
								<input type="hidden" name="value" value={total_pay} />
								<label for="amount">{locale.T('amount')}</label>
								<CurrencyInput
									class="input-group pb-2"
									bind:amount={pay}
									symbol={get_currency?.currency}
								/>
								<CurrencyInput bind:amount={pay_1} symbol={get_currency?.exchang_to} />
							</div>
							<div class="col">
								<label for="payment_type_id">{locale.T('type')} </label>
								<select
									value={get_payment?.payment_type_id || get_payment_types[0]?.id}
									class="form-control"
									name="payment_type_id"
									id="payment_type_id"
								>
									{#each get_payment_types as item}
										<option value={item.id}>{item.by}</option>
									{/each}
								</select>
							</div>
						</div>
					</div>
					<label for="file">{locale.T('documents')}</label>
					<CropImage
						name="file"
						related_id={get_payment?.id}
						related_type_="payment"
						default_image={get_payment?.uploads?.filename}
						aspect_ratio
					/>
					<label class="pt-2" for="note">{locale.T('note')}</label>
					<textarea value={get_payment?.note} class="form-control" rows="4" name="note" id="note"
					></textarea>
					<div class=" text-end pt-2">
						<SubmitButton {loading} />
					</div>
				</Form>
			</fieldset>
		</div>
		<div class="col-7">
			<span class="btn btn-sm btn-info rounded-0">{locale.T('payment_history')}</span>
			<Currency
				class="text-bg-primary mx-2 px-2"
				amount={get_billing?.payment?.reduce((s, e) => s + Number(e.value), 0) || 0}
				symbol={get_currency?.currency}
			/>
			{#if Number(balance) > 0}
				{locale.T('balance')}
				<Currency
					class="text-bg-danger mx-2 px-2"
					amount={balance}
					symbol={get_currency?.currency}
				/>
			{/if}
			{#if get_billing?.payment?.length}
				{#each get_billing?.payment || [] as item, index}
					<div class="row g-0">
						<input type="hidden" name="id" value={item.id} />
						<!-- <input type="hidden" name="after_disc" value={after_disc} /> -->
						<div class="col">
							<div class="alert alert-warning rounded-0 py-1 my-1">
								<div class="row g-1">
									<div class="col-auto">{locale.T('n')} {index + 1}</div>
									<div class="col-auto">
										{locale.T('amount')}
										<Currency amount={item.value} symbol={get_currency?.currency} />
									</div>
									<div class="col-auto">
										{locale.T('date')}
										<DDMMYYYYFormat date={item.datetime} />
									</div>
									<div class="col-auto">
										{item.paymentType?.by}
									</div>
								</div>
							</div>
						</div>
						<div class="col-auto">
							<button type="button" class="alert alert-primary rounded-0 py-1 my-1"
								>{item.staff?.name_khmer}</button
							>
						</div>
						<div class="col-auto">
							<button
								type="button"
								onclick={() => (payment_id = item.id)}
								class="alert alert-success rounded-0 py-1 my-1">{locale.T('edit')}</button
							>
						</div>
						<div class="col-auto">
							<button
								data-bs-toggle="modal"
								data-bs-target="#delete_payment"
								type="button"
								onclick={() => (payment_id_delete = item.id)}
								class="alert alert-danger rounded-0 py-1 my-1">{locale.T('delete_')}</button
							>
						</div>
					</div>
				{/each}
			{/if}
		</div>
	</div>
</div>
<Confirm
	body={[{ name: 'id', value: payment_id_delete?.toString() ?? '' }]}
	method="POST"
	modal_id="delete_payment"
	action={`?/delete_payment&payment_id=${payment_id_delete}`}
/>
