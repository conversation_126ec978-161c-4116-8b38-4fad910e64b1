<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import RichText from '$lib/coms-form/RichText.svelte';
	import CreateTemplate from '$lib/coms-cu/CreateTemplate.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}

	let { data, form }: Props = $props();
	let template_id: number | null = $state(null);
	let { get_templates, get_groups } = $derived(data);
	let find_template = $derived(get_templates.filter((e) => e.id === template_id));
</script>

<CreateTemplate
	data={{
		get_templates: find_template,
		get_groups: get_groups
	}}
	{form}
	{template_id}
/>
<RichText data={{ get_templates: find_template }} bind:template_id />
<DeleteModal action="?/delete_template" id={find_template[0]?.id} />

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('template')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/img-template" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-sticky-note nav-icon"></i>
					{locale.T('template')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col">
				<input
					type="text"
					name="table_search"
					class="form-control float-right"
					placeholder="Search"
				/>
			</div>
			<div class="col-auto">
				<button
					onclick={() => {
						template_id = null;
					}}
					type="button"
					class="btn btn-success"
					data-bs-toggle="modal"
					data-bs-target="#create_template"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add_template')}
				</button>
			</div>
		</div>
	</div>
	<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-light text-nowrap table-hover">
			<thead class="table-active table-light sticky-top">
				<tr>
					<th class="text-center" style="width: 5%;">{locale.T('n')}</th>
					<th style="width: 40%;">{locale.T('diagnosis')}</th>
					<th style="width: 20%;">{locale.T('imagerie_group')}</th>
					<th style="width: 10%;">{locale.T('template')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody>
				{#each get_templates || [] as item, index}
					<tr>
						<td class="text-center">{index + 1}</td>
						<td>
							{item.diagnosis}
						</td>
						<td>
							{item.group?.name ?? ''}
						</td>
						<td>
							<button
								aria-label="templateview"
								onclick={() => {
									template_id = null;
									template_id = item.id;
								}}
								type="button"
								class="btn btn-light btn-sm text-end"
								data-bs-toggle="modal"
								data-bs-target="#template_view"
								><i class="fa-regular fa-folder-open"></i>
							</button>
						</td>
						<td>
							<div>
								<button
									aria-label="createtemplate"
									onclick={() => {
										template_id = null;
										template_id = item.id;
									}}
									type="button"
									class="btn btn-primary btn-sm text-end"
									data-bs-toggle="modal"
									data-bs-target="#create_template"
									><i class="fa-solid fa-file-pen"></i>
								</button>
								<button
									aria-label="deletemodal"
									onclick={() => {
										template_id = null;
										template_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm text-end"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
