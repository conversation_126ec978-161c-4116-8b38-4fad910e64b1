<script lang="ts">
	type QRCodeOptions = {
		size?: number;
		margin?: number;
		darkColor?: string;
		lightColor?: string;
	};

	class QRCodeGenerator {
		public static generate(text: string, options: QRCodeOptions = {}): HTMLCanvasElement {
			const { size = 200, margin = 4, darkColor = '#000000', lightColor = '#ffffff' } = options;

			// Create a simple QR pattern (simplified for demonstration)
			const modules = this.createSimpleQR(text);
			const canvas = document.createElement('canvas');
			const ctx = canvas.getContext('2d');
			if (!ctx) throw new Error('Could not get canvas context');

			canvas.width = size;
			canvas.height = size;

			const moduleCount = modules.length;
			const moduleSize = (size - margin * 2) / moduleCount;

			// Draw light background
			ctx.fillStyle = lightColor;
			ctx.fillRect(0, 0, size, size);

			// Draw dark modules
			ctx.fillStyle = darkColor;
			for (let y = 0; y < moduleCount; y++) {
				for (let x = 0; x < moduleCount; x++) {
					if (modules[y][x]) {
						const px = margin + x * moduleSize;
						const py = margin + y * moduleSize;
						ctx.fillRect(px, py, moduleSize, moduleSize);
					}
				}
			}

			return canvas;
		}

		private static createSimpleQR(text: string): boolean[][] {
			// Simplified QR code pattern (21x21 for version 1)
			const size = 21;
			const modules: boolean[][] = Array(size)
				.fill(null)
				.map(() => Array(size).fill(false));

			// Add position patterns (corners)
			this.addPositionPattern(modules, 0, 0);
			this.addPositionPattern(modules, size - 7, 0);
			this.addPositionPattern(modules, 0, size - 7);

			// Add timing patterns
			for (let i = 8; i < size - 8; i++) {
				modules[6][i] = i % 2 === 0;
				modules[i][6] = i % 2 === 0;
			}

			// Simple data encoding - just fills the available space with a pattern
			// based on the input text (this is a simplified demonstration)
			let dataIndex = 0;
			for (let y = 0; y < size; y++) {
				for (let x = 0; x < size; x++) {
					// Skip position patterns and timing patterns
					if (
						(x < 7 && y < 7) ||
						(x > size - 8 && y < 7) ||
						(x < 7 && y > size - 8) ||
						x === 6 ||
						y === 6
					) {
						continue;
					}

					// Simple pattern based on character codes
					if (text.charCodeAt(dataIndex % text.length) % (x + y + 2) > (x + y) / 2) {
						modules[y][x] = true;
					}
					dataIndex++;
				}
			}

			return modules;
		}

		private static addPositionPattern(modules: boolean[][], x: number, y: number): void {
			for (let i = 0; i < 7; i++) {
				for (let j = 0; j < 7; j++) {
					modules[y + i][x + j] =
						i === 0 || i === 6 || j === 0 || j === 6 || (i >= 2 && i <= 4 && j >= 2 && j <= 4);
				}
			}
		}
	}

	// Component state
	let textInput = '';
	let qrCodeCanvas: HTMLCanvasElement | null = null;

	function generateQRCode() {
		if (!textInput.trim()) {
			alert('Please enter some text');
			return;
		}

		qrCodeCanvas = QRCodeGenerator.generate(textInput, {
			size: 200,
			margin: 4,
			darkColor: '#000000',
			lightColor: '#ffffff'
		});
	}

	function downloadQRCode() {
		if (!qrCodeCanvas) {
			alert('Please generate a QR code first');
			return;
		}

		const link = document.createElement('a');
		link.download = 'qrcode.png';
		link.href = qrCodeCanvas.toDataURL('image/png');
		link.click();
	}
</script>

<div class="qrcode-container">
	<h1>QR Code Generator</h1>

	<div class="input-group">
		<input
			type="text"
			bind:value={textInput}
			placeholder="Enter text or URL"
			on:keydown={(e) => e.key === 'Enter' && generateQRCode()}
		/>
		<div class="button-group">
			<button on:click={generateQRCode}>Generate</button>
			<button on:click={downloadQRCode}>Download</button>
		</div>
	</div>
	{#if qrCodeCanvas}
		<div class="canvas-container">
			{@html qrCodeCanvas.outerHTML}
		</div>
	{/if}
</div>

<style>
	.qrcode-container {
		max-width: 600px;
		margin: 0 auto;
		padding: 20px;
		font-family: Arial, sans-serif;
	}

	h1 {
		text-align: center;
		color: #333;
	}

	.input-group {
		margin-bottom: 20px;
	}

	input {
		width: 100%;
		padding: 10px;
		font-size: 16px;
		margin-bottom: 10px;
		box-sizing: border-box;
	}

	.button-group {
		display: flex;
		gap: 10px;
	}

	button {
		padding: 10px 15px;
		background-color: #4caf50;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 16px;
		flex: 1;
	}

	button:hover {
		background-color: #45a049;
	}

	.canvas-container {
		margin-top: 20px;
		display: flex;
		justify-content: center;
	}
</style>
