<script lang="ts">
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import CreateVisitIpd from '$lib/coms-cu/CreateVisitIPD.svelte';
	import type { PageServerData } from './$types';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import SaopNote from '$lib/coms-ipd-opd/SaopNote.svelte';
	import ParaClinic from '$lib/coms-ipd-opd/ParaClinic.svelte';
	import Treatment from '$lib/coms-ipd-opd/Treatment.svelte';
	import CopyPrescription from '$lib/coms-ipd-opd/CopyPrescription.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Name from '$lib/coms/Name.svelte';
	import FieldsetBilling from '$lib/coms/FieldsetBilling.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let visit_id: number = $state(0);
	let { get_progress_note, removeDuplicateDate, get_exams, get_currency, get_staffs, user } =
		$derived(data);
</script>

<DeleteModal id={visit_id} action="?/delete_visit_ipd" />
<CreateVisitIpd
	data={{
		get_progress_note: get_progress_note,
		get_staffs: get_staffs,
		user: user
	}}
/>

<div class="card bg-lgiht">
	<div class="card-header">
		<div class="row justify-content-between">
			<div class="col-auto">
				<FieldsetBilling status={get_progress_note?.billing?.status}>
					<button
						type="button"
						class="btn btn-primary btn-sm"
						data-bs-toggle="modal"
						data-bs-target="#create_visit_ipd"
						><i class="fa-solid fa-square-plus"></i>
						New Progress note
					</button>
				</FieldsetBilling>
			</div>
			<div class="col-auto">
				<a
					aria-label="print_all_progress_note"
					target="_blank"
					href="/print/{get_progress_note?.id}/progress-note"
					class="btn btn-success btn-sm text-end float-end"
					><i class="fa-solid fa-print"></i>
				</a>
			</div>
		</div>
	</div>
	<div class="card-body table-responsive p-0">
		{#each removeDuplicateDate || [] as { date_checkup, id }}
			{@const f_date = new Intl.DateTimeFormat('en-GB', { dateStyle: 'short' }).format(
				new Date(date_checkup ?? '')
			)}
			<table class="table table-bordered table-light">
				<thead class="table-light table-active">
					<tr class="text-center">
						<td>
							<DDMMYYYYFormat style="date" date={date_checkup} />
						</td>
						<td>Observation Medical or SAOP note</td>
						<td>Para-Clinic</td>
						<td>Treatment </td>
					</tr>
				</thead>
				<tbody>
					{#each get_progress_note?.visit || [] as item (item.id)}
						{@const find_old_visit = item}
						{@const s_date = new Intl.DateTimeFormat('en-GB', {
							dateStyle: 'short'
						}).format(new Date(item.date_checkup ?? ''))}
						{#if f_date === s_date}
							<tr>
								<td class="p-2" style="width:10%;vertical-align:top;">
									<button
										type="button"
										class="btn btn-light btn-sm w-100 mb-2"
										data-bs-toggle="modal"
										data-bs-target="#create-product"
									>
										<DDMMYYYYFormat style="time" date={item.date_checkup} />
									</button>
									<FieldsetBilling status={get_progress_note?.billing?.status}>
										<FieldsetBilling status={find_old_visit?.billing?.status}>
											<a
												href="/opd/{item?.id}/subjective"
												class="btn btn-outline-info btn-sm w-100 mb-2"
												><i class=" fa-solid fa-file-pen"></i> Edit
											</a>
											<button
												type="button"
												onclick={() => (visit_id = item.id)}
												class="btn btn-outline-danger btn-sm w-100 mb-2"
												data-bs-toggle="modal"
												data-bs-target="#delete_modal"
												><i class="fa-solid fa-trash-can"></i> {locale.T('delete_')}
											</button>
										</FieldsetBilling>
									</FieldsetBilling>
									<a
										target="_blank"
										href="/print/{get_progress_note?.id}/progress-note?visit_id={item.id}"
										class="btn btn-outline-success btn-sm w-100 mb-2"
										><i class="fa-solid fa-print"></i> Print
									</a>
									<button
										type="button"
										onclick={() => (visit_id = item.id)}
										class="btn btn-secondary btn-sm w-100 mb-2"
									>
										{item?.department?.products ?? ''}
									</button>
									<button
										type="button"
										class="btn btn-primary btn-sm w-100"
										data-bs-toggle="modal"
										data-bs-target="#"
										><i class="fa-solid fa-user"></i>
										<Name
											name_khmer={item?.staff?.name_khmer}
											name_latin={item?.staff?.name_latin}
										/>
									</button>
								</td>
								<td style="width: 30%;vertical-align:top;">
									<SaopNote {find_old_visit} {get_exams} />
								</td>
								<td style="width: 30%;vertical-align:top;">
									<ParaClinic {find_old_visit} />
								</td>
								<td style="width: 30%;vertical-align:top;">
									<div class="row justify-content-between">
										<div class="col-auto">
											<CopyPrescription
												class="btn btn-warning btn-sm  py-0 mb-2"
												data={item?.presrciption}
											/>
										</div>
										<div class="col-auto">
											{#if find_old_visit?.presrciption.length}
												<FieldsetBilling status={get_progress_note?.billing?.status}>
													<FieldsetBilling status={find_old_visit?.billing?.status}>
														<a
															class="btn btn-primary btn-sm mb-2 float-end py-0"
															href="/opd/{item.id}/prescription"
															>{locale.T('edit')} <i class="fa-solid fa-file-pen"></i></a
														>
													</FieldsetBilling>
												</FieldsetBilling>
											{/if}
										</div>
									</div>
									<FieldsetBilling status={get_progress_note?.billing?.status}>
										<Treatment {get_currency} {find_old_visit} />
									</FieldsetBilling>
								</td>
							</tr>
						{/if}
					{/each}
				</tbody>
			</table>
		{/each}
	</div>
</div>
