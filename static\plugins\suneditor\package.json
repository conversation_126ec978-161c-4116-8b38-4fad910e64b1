{"name": "suneditor", "version": "2.47.3", "description": "Vanilla javascript based WYSIWYG web editor, with no dependencies", "author": "JiHong.Lee", "license": "MIT", "main": "src/suneditor.js", "sideEffects": ["./src/assets/css/*.css"], "scripts": {"dev": "webpack-dev-server --config webpack.dev.js", "start": "npm run dev", "build": "webpack --config webpack.prod.js", "lint": "jshint src", "test": "karma start"}, "bugs": {"url": "https://github.com/JiHong88/SunEditor/issues"}, "homepage": "http://suneditor.com", "repository": {"type": "git", "url": "https://github.com/JiHong88/SunEditor.git"}, "ignore": ["**/.*", "node_modules", "bower_components"], "devDependencies": {"@babel/core": "~7.21.3", "@octokit/rest": "~20.1.0", "@webpack-cli/init": "~1.1.3", "babel-loader": "~8.1.0", "clean-webpack-plugin": "~0.1.19", "codemirror": "~5.61.0", "css-loader": "~1.0.1", "csstype": "~2.6.13", "file-loader": "~2.0.0", "html-webpack-plugin": "~3.2.0", "jasmine": "~2.99.0", "jasmine-core": "~2.99.1", "jshint": "~2.13.4", "karma": "~6.3.19", "karma-chrome-launcher": "~2.2.0", "karma-jasmine": "~1.1.2", "katex": "~0.16.10", "mini-css-extract-plugin": "~0.4.5", "optimize-css-assets-webpack-plugin": "~5.0.4", "url-loader": "~1.1.2", "webpack": "~4.44.1", "webpack-cli": "~3.3.12", "webpack-dev-server": "~3.11.0", "webpack-merge": "~4.2.2"}, "keywords": ["wysiwyg", "wysiwyg editor", "wysiwyg html editor", "rich text", "rich editor", "rich text editor", "html", "text", "editor", "contenteditable", "javascript", "vanilla<PERSON>s"]}