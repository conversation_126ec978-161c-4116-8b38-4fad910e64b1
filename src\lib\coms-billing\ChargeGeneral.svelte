<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import type { PageServerData } from '../../routes/(dash)/billing/opd/[id]/$types';
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import DiscountModal from './DiscountModal.svelte';
	import { page } from '$app/state';
	import type { EventHandler } from 'svelte/elements';
	type Data = Pick<PageServerData, 'charge_on_general' | 'get_currency'>;
	interface Props {
		data: Data;
	}
	let { data }: Props = $props();
	let { charge_on_general, get_currency } = $derived(data);
	let is_pos = $state(page.url.pathname.includes('pos'));
	let timeout: number | NodeJS.Timeout | null = $state(null);
	const handleQ: EventHandler<Event, HTMLInputElement> = ({ currentTarget }) => {
		clearTimeout(timeout!);
		const form = currentTarget?.form;
		if (Number(currentTarget.value) <= 0) {
			alert(locale.T('none_data'));
			currentTarget.value = '1';
		}
		if (!form) return;
		timeout = setTimeout(() => {
			form.requestSubmit();
		}, 400);
	};
</script>

<!-- General  -->
{#if charge_on_general?.productOrder.length}
	{#if !is_pos}
		<tr class="table-active table-warning fs-6">
			<td colspan="6"
				>{locale.T('general')} {charge_on_general.productOrder.length} {locale.T('items')}</td
			>
		</tr>
	{/if}
	{#each charge_on_general.productOrder as item (item.id)}
		<tr class="text-start">
			<td class="text-start">
				<DiscountModal
					currency={get_currency}
					discount={item?.discount}
					product_order_name={item.product?.products}
					charge_id={item.charge_id}
					product_order_id={item.id}
					product_order_price={item.price}
					product_price={item.product?.price}
					qty={item.qty}
					unit_id={item.unit_id}
					unit={item.product.unit}
					sub_units={item.product.subUnit}
				/>
				<br />
			</td>
			<td class="text-center">
				<div>
					<input value={item?.price} type="hidden" name="price" />
					<Currency class="fs-6" amount={item?.price} symbol={get_currency?.currency} />
					<span class="text-success"> / {item.unit?.unit}</span>
				</div>
				<!-- <fieldset>
					<CurrencyInput
						class="input-group input-group-sm"
						name="price"
						amount={item?.price}
						symbol={get_currency?.currency}
					/>
				</fieldset> -->
			</td>
			<td class="text-center">
				<div>
					<Form
						reset={false}
						data_sveltekit_keepfocus
						method="post"
						action="?/discount_product_order"
					>
						<input type="hidden" name="product_order_id" value={item.id} />
						<input type="hidden" name="disc" value={item.discount} />
						<input type="hidden" name="charge_id" value={charge_on_general.id} />
						<input type="hidden" name="price" value={item.price} />
						<input
							style="height: 30px;"
							class="form-control bg-light text-center fs-6"
							type="number"
							min="1"
							step="any"
							oninput={handleQ}
							name="qty"
							value={item?.qty}
						/>
					</Form>
				</div>
			</td>
			<td class="text-center">
				<Currency class="fs-6" amount={item.total || 0} symbol={get_currency?.currency} />
			</td>
			<td class="text-center">
				<fieldset>
					<Form action="?/remove_product_order" method="post">
						<input type="hidden" name="product_order_id" value={item.id} />
						<button aria-label="submit" type="submit" class="btn btn-link text-danger"
							><i class="fa-solid fa-trash"></i></button
						>
					</Form>
				</fieldset>
			</td>
		</tr>
	{/each}
{/if}
