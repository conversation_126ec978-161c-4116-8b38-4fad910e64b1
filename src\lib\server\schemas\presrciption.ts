import { datetime, float, int, mysqlTable, varchar } from 'drizzle-orm/mysql-core';
import { progressNote, visit } from './visit';
import { product, unit } from './product';
import { relations } from 'drizzle-orm';
import { user } from './auth';

export const presrciption = mysqlTable('presrciption', {
	id: int().primaryKey().autoincrement(),
	visit_id: int().references(() => visit.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	product_id: int().references(() => product.id),
	unit_id: int().references(() => unit.id),
	use: varchar({ length: 150 }),
	duration: varchar({ length: 150 }),
	amount: float(),
	morning: float(),
	noon: float(),
	afternoon: float(),
	evening: float(),
	night: float()
});
export type Presrciption = typeof presrciption.$inferSelect;
export const presrciptionRelations = relations(presrciption, ({ one, many }) => ({
	progressNote: one(progressNote, {
		references: [progressNote.id],
		fields: [presrciption.progress_note_id]
	}),
	visit: one(visit, {
		references: [visit.id],
		fields: [presrciption.visit_id]
	}),
	product: one(product, {
		references: [product.id],
		fields: [presrciption.product_id]
	}),
	unit: one(unit, {
		references: [unit.id],
		fields: [presrciption.unit_id]
	}),
	activePresrciption: many(activePresrciption)
}));

export const activePresrciption = mysqlTable('active_presrciption', {
	id: int().primaryKey().autoincrement(),
	active_for: varchar({ length: 100 }).notNull(),
	datetime: datetime({ mode: 'string' }).notNull(),
	presrciption_id: int().references(() => presrciption.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	user_id: varchar('user_id', { length: 255 }).references(() => user.id)
});
export type ActivePresrciption = typeof activePresrciption.$inferSelect;
export const activePresrciptionRelations = relations(activePresrciption, ({ one }) => ({
	presrciption: one(presrciption, {
		references: [presrciption.id],
		fields: [activePresrciption.presrciption_id]
	}),
	user: one(user, {
		references: [user.id],
		fields: [activePresrciption.user_id]
	})
}));
