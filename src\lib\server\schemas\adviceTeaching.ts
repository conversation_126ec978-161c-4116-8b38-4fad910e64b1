import { int, mysqlTable, text } from 'drizzle-orm/mysql-core';
import { relations } from 'drizzle-orm';
import { progressNote, visit } from './visit';

export const adviceTeaching = mysqlTable('advice_teaching', {
	id: int().primaryKey().autoincrement(),
	description: text(),
	visit_id: int().references(() => visit.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	})
});

export const adviceTeachingRelations = relations(adviceTeaching, ({ one }) => ({
	visit: one(visit, {
		references: [visit.id],
		fields: [adviceTeaching.visit_id]
	}),
	progressNote: one(progressNote, {
		references: [progressNote.id],
		fields: [adviceTeaching.progress_note_id]
	})
}));
