<script lang="ts">
	import type {
		ActionData,
		PageServerData
	} from '../../routes/(dash)/settup/parameter/group/$types';
	import { enhance } from '$app/forms';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	type Data = Pick<PageServerData, 'get_lab_groups' | 'get_product_labo' | 'get_currency'>;
	interface Props {
		form: ActionData;
		data: Data;
		product_lab_id?: number;
	}

	let { form, data, product_lab_id = $bindable() }: Props = $props();
	let loading = $state(false);
	let { get_lab_groups, get_product_labo, get_currency } = $derived(data);
	let find_product_labo = $derived(get_product_labo[0]);
</script>

<!-- @_Add_ParameterGrop -->
<div class="modal fade" id="create_parameter_group" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action={find_product_labo?.id ? '?/update_parameter_group' : '?/create_parameter_group'}
			method="post"
			bind:loading
			class="modal-content"
			fnSuccess={() => {
				product_lab_id = 0;
				document.getElementById('close_create_parameter_group')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('group')}</h4>
				<button
					id="close_create_parameter_group"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={find_product_labo?.id} type="hidden" name="product_id" />
								<label for="product_name">{locale.T('parameter')}</label>
								<input
									value={find_product_labo?.products ?? ''}
									name="product_name"
									type="text"
									class="form-control"
									id="product_name"
								/>
								{#if form?.product_name}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="lab_group_id">{locale.T('lab_group')}</label>
								<SelectParam
									value={find_product_labo?.laboratory_group_id}
									name="lab_group_id"
									items={get_lab_groups.map((e) => {
										return { id: e.id, name: e.laboratory_group };
									})}
								/>

								{#if form?.lab_group_id}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="price">{locale.T('price')}</label>
								<CurrencyInput
									name="price"
									amount={find_product_labo?.price}
									symbol={get_currency?.currency}
								/>
								{#if form?.price}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
