<script lang="ts">
	import type { PageServerData } from './$types';
	import { addDays, dobToAge } from '$lib/helper';
	import Renderhtml from '$lib/coms/Renderhtml.svelte';
	import ClinichInfo from '$lib/coms-report/ClinichInfo.svelte';
	import { page } from '$app/state';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Sign from '$lib/coms-report/Sign.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_imagerie_request, get_clinic_info, get_upload } = $derived(data);
	let age_p_visit = $derived(
		dobToAge(
			get_imagerie_request?.visit?.patient?.dob ?? '',
			get_imagerie_request?.visit?.date_checkup ?? ''
		)
	);
	let isPrint = page.url.searchParams.get('print');
	let row = page.url.searchParams.get('row');
	$effect(() => {
		document.addEventListener('keydown', function (event) {
			window.scrollTo({ top: 0, behavior: 'smooth' });
			if (event.ctrlKey && event.key === 'p') {
				// event.preventDefault();
				// alert('View only');
			}
		});

		if (isPrint === 'true') {
			setTimeout(async () => {
				window.print();
				window.close();
			}, 300);
		}
	});
</script>

<div class="header">
	<ClinichInfo data={{ get_clinic_info, get_upload }} />
	<div class="border p-2 pb-0">
		<table class=" table table-sm table-borderless">
			<thead style="font-size: 120%;">
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Khmer Name</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_imagerie_request?.visit?.patient?.name_khmer}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Gender</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_imagerie_request?.visit?.patient?.gender ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">ID</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						<span>PT{get_imagerie_request?.patient_id}</span>
						<span>IM{get_imagerie_request?.id}</span>
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Latin Name</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_imagerie_request?.visit?.patient?.name_latin}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Age</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						{age_p_visit}
					</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Collection Date</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						<DDMMYYYYFormat date={get_imagerie_request?.visit?.date_checkup} />
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Requester</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_imagerie_request?.visit?.staff?.name_latin ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Symptoms</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">{get_imagerie_request?.visit?.etiology ?? ''}</td>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Report Date</td>
					<td class="p-0 m-0"> : </td>
					<td class="p-0 m-0">
						<DDMMYYYYFormat date={get_imagerie_request?.finish_datetime} />
					</td>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Phone</td>
					<td class="p-0 m-0"> : </td>
					<td class="en_font_times_new_roman p-0 m-0"
						>{get_imagerie_request?.visit?.patient?.telephone ?? ''}</td
					>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Visit</td>
					<td class="p-0 m-0"> : </td>
					<td class="en_font_times_new_roman p-0 m-0"
						>{get_imagerie_request?.visit?.checkin_type ?? ''}</td
					>
					<td class="text-bold en_font_times_new_roman p-0 m-0">Request</td>
					<td class="p-0 m-0"> : </td>
					<td class="kh_font_battambang p-0 m-0">{get_imagerie_request?.product?.products ?? ''}</td
					>
				</tr>
				<tr class="p-0 m-0">
					<td class="text-bold en_font_times_new_roman p-0 m-0">Address</td>
					<td class="p-0 m-0"> : </td>
					<td colspan="7" class="kh_font_battambang p-0 m-0">
						{get_imagerie_request?.visit?.patient?.village?.type ?? ''}
						{get_imagerie_request?.visit?.patient?.village?.name_khmer.concat(',') ?? ''}
						{get_imagerie_request?.visit?.patient?.commune?.type ?? ''}
						{get_imagerie_request?.visit?.patient?.commune?.name_khmer.concat(',') ?? ''}
						{get_imagerie_request?.visit?.patient?.district?.type ?? ''}
						{get_imagerie_request?.visit?.patient?.district?.name_khmer.concat(',') ?? ''}
						{get_imagerie_request?.visit?.patient?.provice?.type ?? ''}
						{get_imagerie_request?.visit?.patient?.provice?.name_khmer ?? ''}
					</td>
				</tr>
			</thead>
		</table>
	</div>
</div>
<table class="w-100 ">
	<thead>
		<tr>
			<td>
				<div class="header-space">&nbsp;</div>
			</td>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
				<u>
					<h1 style="font-size: 130%;" class="text-center kh_font_muol">
						លទ្ធផលរូបភាពវេជ្ជសាស្ត្រ
					</h1>
					<h1 style="font-size: 120%;" class="text-center en_font_times_new_roman">
						Imagerie Report
					</h1>
				</u>
			</td>
		</tr>
		<tr>
			<td>
				{#if row === 'true'}
					<div style="width: 100%;">
						<!-- {get_imagerie_request?.is_ob_form} -->
						{#if get_imagerie_request?.is_ob_form}
							<div class="row g-0">
								{#each get_imagerie_request.resultImagerie as item}
									{#if item.result}
										<div class="col-5">
											<div class="row justify-content-between">
												<div class="col-auto">
													<span style="color:#0000FF" class="form-control border-0 fs-5 text-break">
														{item.resultForm?.name ?? ''}
													</span>
												</div>
												<div class="col-2">
													<span style="color:#0000FF" class="text-end form-control border-0 fs-5">
														:
													</span>
												</div>
											</div>
										</div>
										<div class="col-7">
											<span class="form-control border-0 fs-5 text-break">
												{#if item.resultForm?.type === 'datetime-local'}
													<DDMMYYYYFormat date={item.result} style="date" />
												{:else}
													{item.result || 'none'}
												{/if}
												{#if item.resultForm?.options.length === 1}
													<span style="color:#0000FF" class="fs-5 border-0">
														{item.resultForm?.options[0]?.name}
														{#if item.resultForm.name === 'Period'}
															<DDMMYYYYFormat
																style="date"
																date={addDays(new Date(item.result), 280).toString()}
															/>
														{/if}
													</span>
												{/if}
											</span>
										</div>
									{/if}
								{/each}
							</div>
						{:else}
							<Renderhtml setWidth="100%" value={get_imagerie_request?.result ?? ''} />
						{/if}
					</div>
					<div class="row">
						{#each get_imagerie_request?.uploads || [] as item}
							<div class="col-4">
								<img
									class="img-fluid"
									style="width: 100%;height: 100%;"
									src={item.filename}
									alt=""
								/>
							</div>
						{/each}
					</div>
				{:else if row === 'false'}
					<div class="row g-0">
						<div class="col-4">
							{#each get_imagerie_request?.uploads || [] as item}
								<div class="me-2">
									<img
										class="img-fluid"
										style="width: 100%;height: 100%;"
										src={item.filename}
										alt=""
									/>
								</div>
							{/each}
						</div>
						<div style="zoom: 90%;" class="col-8">
							{#if get_imagerie_request?.is_ob_form}
								<div class="row g-0">
									{#each get_imagerie_request.resultImagerie as item}
										{#if item.result}
											<div class="col-5">
												<div class="row justify-content-between">
													<div class="col-auto">
														<span
															style="color:#0000FF"
															class="form-control border-0 fs-5 text-break"
														>
															{item.resultForm?.name ?? ''}
														</span>
													</div>
													<div class="col-2">
														<span style="color:#0000FF" class="text-end form-control border-0 fs-5">
															:
														</span>
													</div>
												</div>
											</div>
											<div class="col-7">
												<span class="form-control border-0 fs-5 text-break">
													{#if item.resultForm?.type === 'datetime-local'}
														<DDMMYYYYFormat date={item.result} style="date" />
													{:else}
														{item.result || 'none'}
													{/if}
													{#if item.resultForm?.options.length === 1}
														<span style="color:#0000FF" class="fs-5 border-0">
															{item.resultForm?.options[0]?.name}
															{#if item.resultForm.name === 'Period'}
																<DDMMYYYYFormat
																	style="date"
																	date={addDays(new Date(item.result), 280).toString()}
																/>
															{/if}
														</span>
													{/if}
												</span>
											</div>
										{/if}
									{/each}
								</div>
							{:else}
								<Renderhtml setWidth="100%" value={get_imagerie_request?.result ?? ''} />
							{/if}
						</div>
					</div>
				{:else}
					<div class="">
						{#each get_imagerie_request?.uploads || [] as item}
							<div class="">
								<img
									class="img-fluid"
									style="width: 100%;height: 100%;"
									src={item.filename}
									alt=""
								/>
							</div>
						{/each}
						<Renderhtml setWidth="100%" value={get_imagerie_request?.result ?? ''} />
					</div>
				{/if}
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td>
				<div class="footer-space">&nbsp;</div>
			</td>
		</tr>
	</tfoot>
</table>
<div class="footer">
	<Sign
		right={{
			date: get_imagerie_request?.finish_datetime,
			img: get_imagerie_request.visit?.staff?.sign?.filename,
			name: get_imagerie_request?.visit?.staff?.name_khmer,
			role: `Physician'sign`
		}}
		qr={page.url.href}
	/>
	<hr />
	<h6 style="color:#0000FF" class="text-center">
		{get_clinic_info?.address ?? ''}
	</h6>
</div>

<style>
	/* @page {
		size: A4;
		margin: 7mm;
		margin-bottom: 3mm;
	} */
	@media print {
		img {
			width: auto;
			max-height: 700px;
		}
		.footer,
		.footer-space {
			height: 330px;
		}
	}
</style>
