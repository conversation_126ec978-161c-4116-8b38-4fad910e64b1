import { db } from '$lib/server/db';
import { and, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { uploads, visit } from '$lib/server/schemas';

export const load = (async ({ params }) => {
	const { id } = params;
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +id),
		with: {
			accessment: true,
			patient: {
				with: {
					village: true,
					district: true,
					commune: true,
					provice: true
				}
			},
			staff: true,
			laboratoryRequest: {
				with: {
					product: {
						with: {
							laboratoryGroup: true
						}
					}
				}
			}
		}
	});

	// const sort_by_group = new Map();
	// get_visit?.laboratoryRequest?.forEach((item) => {
	// 	const group = item?.product?.laboratoryGroup;
	// 	if (!sort_by_group.has(group?.laboratory_group)) {
	// 		sort_by_group.set(group?.laboratory_group, []);
	// 	}
	// 	sort_by_group.get(group?.laboratory_group).push(item);
	// });
	// const groups = Array.from(sort_by_group, ([group, items]) => ({ group, items }));
	const s = get_visit?.laboratoryRequest?.sort((a, b) => {
		const groupIdA = a?.product?.laboratory_group_id as number;
		const groupIdB = b?.product?.laboratory_group_id as number;
		if (+groupIdA < +groupIdB) return -1;
		if (+groupIdA > +groupIdB) return 1;
		return 0;
	});

	return { get_clinic_info, get_visit, sort_by_group: s, get_upload };
}) satisfies PageServerLoad;
