import type { RequestEvent } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { sha256 } from '@oslojs/crypto/sha2';
import { encodeBase32LowerCase, encodeHexLowerCase } from '@oslojs/encoding';
import { db } from '$lib/server/db';
import * as table from '$lib/server/schemas';

const DAY_IN_MS = 1000 * 60 * 60 * 24;

export const sessionCookieName = 'auth-session';

export function generateSessionToken() {
	const bytes = crypto.getRandomValues(new Uint8Array(20));
	const token = encodeBase32LowerCase(bytes);
	return token;
}

export async function createSession(token: string, user_id: string) {
	const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
	const session: table.Session = {
		id: sessionId,
		user_id,
		expires_at: new Date(Date.now() + DAY_IN_MS * 30)
	};
	await db.insert(table.session).values(session);
	return session;
}

export async function validateSessionToken(token: string) {
	const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
	const [result] = await db
		.select({
			// Adjust user table here to tweak returned data
			user: {
				id: table.user.id,
				username: table.user.username,
				staff_id: table.user.staff_id
			},
			session: table.session
		})
		.from(table.session)
		.innerJoin(table.user, eq(table.session.user_id, table.user.id))
		.where(eq(table.session.id, sessionId));

	if (!result) {
		return { session: null, user: null, roles: null, departments: null };
	}
	const get_staff = await db.query.staff.findFirst({
		where: eq(table.staff.id, Number(result?.user.staff_id)),
		with: {
			staffToRole: {
				with: {
					role: true
				}
			},
			staffToDemartment: {
				with: {
					department: true
				}
			}
		}
	});
	const { session, user } = result;

	const sessionExpired = Date.now() >= session.expires_at.getTime();
	if (sessionExpired) {
		await db.delete(table.session).where(eq(table.session.id, session.id));
		return { session: null, user: null };
	}

	const renewSession = Date.now() >= session.expires_at.getTime() - DAY_IN_MS * 15;
	if (renewSession) {
		session.expires_at = new Date(Date.now() + DAY_IN_MS * 30);
		await db
			.update(table.session)
			.set({ expires_at: session.expires_at })
			.where(eq(table.session.id, session.id));
	}
	const roles = get_staff?.staffToRole.map((e) => ({ role: e.role?.role }));
	const departments = get_staff?.staffToDemartment.map((e) => ({
		department: e.department?.products
	}));
	return { session, user, roles, departments };
}

export type SessionValidationResult = Awaited<ReturnType<typeof validateSessionToken>>;

export async function invalidateSession(sessionId: string) {
	await db.delete(table.session).where(eq(table.session.id, sessionId));
}

export function setSessionTokenCookie(event: RequestEvent, token: string, expires_at: Date) {
	event.cookies.set(sessionCookieName, token, {
		expires: expires_at,
		path: '/',
		secure: false
	});
}

export function deleteSessionTokenCookie(event: RequestEvent) {
	// eslint-disable-next-line drizzle/enforce-delete-with-where
	event.cookies.delete(sessionCookieName, {
		path: '/'
	});
}
