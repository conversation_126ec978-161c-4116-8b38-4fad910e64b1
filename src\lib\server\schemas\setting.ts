import { boolean, float, int, mysqlTable, varchar } from 'drizzle-orm/mysql-core';

export const setting = mysqlTable('setting', {
	id: int().primaryKey().autoincrement(),
	print_bill: boolean().default(false).notNull(),
	warring_expire_day: int().notNull().default(14)
});

export const currency = mysqlTable('currency', {
	id: int().primaryKey().autoincrement(),
	currency: varchar({ length: 5 }).notNull().unique(),
	currency_rate: float().notNull().unique(),
	exchang_to: varchar({ length: 5 }).notNull().unique(),
	exchang_rate: float().notNull()
});

export type TCurrency = typeof currency.$inferSelect;

export const documentSetting = mysqlTable('document_setting', {
	id: int().primaryKey().autoincrement(),
	logo_size: varchar({ length: 20 }),
	clinic_title_en_size: varchar({ length: 20 }),
	clinic_title_en_color: varchar({ length: 20 }),
	clinic_title_kh_color: varchar({ length: 20 }),
	clinic_title_kh_size: varchar({ length: 20 }),
	header_size: varchar({ length: 20 }),
	header_color: varchar({ length: 20 }),
	title_size: varchar({ length: 20 }),
	title_color: varchar({ length: 20 }),
	footer_color: varchar({ length: 20 }),
	footer_size: varchar({ length: 20 }),
	text_body_color: varchar({ length: 20 }),
	text_input_color: varchar({ length: 20 })
});
