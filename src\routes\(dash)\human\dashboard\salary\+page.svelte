<script lang="ts">
	import { page } from '$app/state';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { dobToAge, YYYYMMDD_Format } from '$lib/helper';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_currency, get_salary_this_month, get_payment_types } = $derived(data);
	let now_month = $derived(page.url.searchParams.get('effective_date'));
	let total_base_salary = $derived(
		get_salary_this_month.reduce((acc, item) => acc + Number(item.base_salary), 0)
	);
	let total_allowance = $derived(
		get_salary_this_month.reduce((acc, item) => acc + Number(item.allowance), 0)
	);
	let total_bonus = $derived(
		get_salary_this_month.reduce((acc, item) => acc + Number(item.bonus), 0)
	);
	let total_deduction = $derived(
		get_salary_this_month.reduce((acc, item) => acc + Number(item.deduction), 0)
	);
	let total_salary = $derived(
		get_salary_this_month.reduce((acc, item) => acc + Number(item.total_salary), 0)
	);
	let inerHight = $derived(+store.inerHight.replace('px', '') + 50 + 'px');
</script>

<DeleteModal action="?/delete_salary_this_month" id={now_month} />

<div class="card bg-light">
	<div class="card-header">
		<div class="row g-1">
			<div class="col">
				<div class="row g-1">
					<HeaderQuery class="col-auto">
						<label class="visually-hidden" for="effective_date">{locale.T('date')}</label>
						<div class="input-group">
							<div class="input-group-text">{locale.T('date')}</div>
							<input
								name="effective_date"
								bind:value={now_month}
								type="month"
								class="form-control"
								id="effective_date"
							/>
						</div>
					</HeaderQuery>
					<Form method="POST" action="?/create_salary" class="col-auto">
						<input type="hidden" name="effective_date" value={now_month} />
						<button
							disabled={get_salary_this_month?.length ? true : false}
							class="btn btn-primary"
							type="submit"
						>
							{locale.T('payroll_list')}
						</button>
					</Form>
					{#if get_salary_this_month?.length}
						<div class="col">
							<button data-bs-toggle="modal" data-bs-target="#delete_modal" class="btn btn-danger"
								>{locale.T('delete_')}</button
							>
						</div>
					{/if}
				</div>
			</div>
			<div class="col-auto text-end">
				<a href="/human/dashboard" class="btn btn-success"
					><i class="fa-solid fa-user"></i>
					{locale.T('employees')}
				</a>
			</div>
		</div>
	</div>
	<div style="height: {inerHight}; " class="card-body table-responsive p-0 m-0">
		<table class="table table-hover table-bordered table-light">
			<thead class="sticky-top bg-light table-active">
				<tr class="text-center">
					<th class="text-center">{locale.T('n')}</th>
					<th style="width: 150px;">{locale.T('name')}</th>
					<th>{locale.T('gender')}</th>
					<th>{locale.T('age')}</th>
					<th style="width: 100px;">{locale.T('base_salary')}</th>
					<th style="width:150px;">{locale.T('allowance')}</th>
					<th style="width:150px;">{locale.T('bonus')}</th>
					<th style="width:150px;">{locale.T('deduction')}</th>
					<th>{locale.T('total_salary')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_salary_this_month || [] as item, index (item.id)}
					<tr class="text-center">
						<td>
							<input type="hidden" name="id" value={item?.id} />
							<input type="hidden" name="base_salary" value={item?.base_salary} />
							{index + 1}
						</td>
						<td>
							<div class="text-primary">
								{item?.staff?.name_khmer} <br />
								{item?.staff?.name_latin}
							</div>
						</td>
						<td>{item?.staff?.gender}</td>
						<td>{dobToAge(item?.staff?.dob, null)} </td>
						<td>
							<Currency class="" amount={item?.base_salary} symbol={get_currency?.currency} />
						</td>
						<td>
							<div>
								<Form
									reset={false}
									onchange={(e) => e.currentTarget.requestSubmit()}
									method="POST"
									action="?/update_single_salary"
								>
									<input type="hidden" name="id" value={item?.id} />
									<CurrencyInput
										name="allowance"
										amount={item?.allowance}
										symbol={get_currency?.currency}
									/>
								</Form>
							</div>
						</td>
						<td>
							<div>
								<Form
									reset={false}
									onchange={(e) => e.currentTarget.requestSubmit()}
									method="POST"
									action="?/update_single_salary"
								>
									<input type="hidden" name="id" value={item?.id} />
									<CurrencyInput
										name="bonus"
										amount={item?.bonus}
										symbol={get_currency?.currency}
									/>
								</Form>
							</div>
						</td>
						<td>
							<div>
								<Form
									reset={false}
									onchange={(e) => e.currentTarget.requestSubmit()}
									method="POST"
									action="?/update_single_salary"
								>
									<input type="hidden" name="id" value={item?.id} />
									<CurrencyInput
										name="deduction"
										amount={item?.deduction}
										symbol={get_currency?.currency}
									/>
								</Form>
							</div>
						</td>
						<td>
							<Currency
								class={item?.payroll ? 'text-success' : 'text-danger'}
								amount={item?.total_salary}
								symbol={get_currency?.currency}
							/>
						</td>
					</tr>
				{/each}
				{#if get_salary_this_month?.length}
					<tr class="text-end table-active">
						<td class="table-success table-active" colspan="4">
							{locale.T('total')}
						</td>
						<td>
							<Currency class="" amount={total_base_salary} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency class="" amount={total_allowance} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency class="" amount={total_bonus} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency class="" amount={total_deduction} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency class="" amount={total_salary} symbol={get_currency?.currency} />
						</td>
					</tr>
					<tr class="text-end p-0 table-active">
						<td class="table-primary table-active" colspan="4">
							{locale.T('payment')}
						</td>
						<td class="p-0" colspan="5">
							<fieldset disabled={get_salary_this_month.at(0)?.payroll ? true : false}>
								<Form method="POST" action="?/create_payroll" class="p-2">
									<input type="hidden" name="effective_date" value={now_month} />
									<div class="col">
										<div class="input-group">
											<label style="width: 150px;" for="payment_date" class="input-group-text"
												>{locale.T('date')}</label
											>
											<input
												value={get_salary_this_month[0].payroll?.payment_date}
												required
												type="date"
												name="payment_date"
												class="form-control rounded-0"
											/>
										</div>
									</div>
									<div class="col">
										<div class="input-group">
											<label style="width: 150px;" for="payment_type_id" class="input-group-text"
												>{locale.T('type_payment')}</label
											>
											<select
												value={get_salary_this_month[0].payroll?.payment_type_id}
												required
												name="payment_type_id"
												class="form-select rounded-0"
											>
												{#each get_payment_types || [] as item (item.id)}
													<option value={item.id}>{item.by}</option>
												{/each}
											</select>
										</div>
									</div>
									<div class="col pt-2">
										<button type="submit" class="btn btn-primary">
											<i class="fa-solid fa-floppy-disk"></i> {locale.T('save')}</button
										>
									</div>
								</Form>
							</fieldset>
						</td>
					</tr>
				{/if}
			</tbody>
		</table>
	</div>
</div>
