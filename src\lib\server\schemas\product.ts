import { relations } from 'drizzle-orm';
import {
	int,
	mysqlTable,
	text,
	datetime,
	decimal,
	boolean,
	varchar,
	primaryKey
} from 'drizzle-orm/mysql-core';
import { laboratoryGroup } from './laboratory';
import { parameter } from './parameter';
import { laboratoryRequest } from './laboratory';
import { exspend } from './supplier';
import { charge } from './billing';
export const product = mysqlTable('product', {
	id: int().primaryKey().autoincrement(),
	products: text().notNull(),
	generic_name: text(),
	barcode: varchar({ length: 255 }),
	group_id: int().references(() => group.id),
	laboratory_group_id: int().references(() => laboratoryGroup.id),
	price: decimal({ precision: 18, scale: 2 }).$type<number>(),
	brand_id: int().references(() => brand.id, { onDelete: 'set null' }),
	category_id: int().references(() => category.id, { onDelete: 'set null' }),
	unit_id: int().references(() => unit.id),
	create_at: datetime({ mode: 'string' })
});
export const productRelations = relations(product, ({ one, many }) => ({
	unit: one(unit, {
		fields: [product.unit_id],
		references: [unit.id]
	}),
	brand: one(brand, {
		fields: [product.brand_id],
		references: [brand.id]
	}),
	category: one(category, {
		fields: [product.category_id],
		references: [category.id]
	}),
	group: one(group, {
		fields: [product.group_id],
		references: [group.id]
	}),
	laboratoryGroup: one(laboratoryGroup, {
		fields: [product.laboratory_group_id],
		references: [laboratoryGroup.id]
	}),
	parameter: many(parameter),
	laboratoryRequest: many(laboratoryRequest),
	inventory: many(inventory),
	subUnit: many(subUnit),
	productOrder: many(productOrder)
}));

export const inventory = mysqlTable('inventory', {
	id: int().primaryKey().autoincrement(),
	product_id: int()
		.references(() => product.id)
		.notNull(),
	cost_unit_id: int().references(() => unit.id),
	exspend_id: int().references(() => exspend.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	cost: decimal({ precision: 18, scale: 2 }).$type<number>(),
	total_expense: decimal({ precision: 18, scale: 2 }).$type<number>(),
	is_outstock: boolean().default(false).notNull(),
	is_expire: boolean().default(false).notNull(),
	is_close_inventory: boolean().default(false).notNull(),
	qty_bought: int().default(0).notNull(),
	qty_expire: int().default(0).notNull(),
	group_id: int().references(() => group.id),
	qty_adjustment: int().default(0).notNull(),
	is_count_stock: boolean().default(false).notNull(),
	datetime_expire: datetime({ mode: 'string' }),
	datetime_buy: datetime({ mode: 'string' }),
	datetime_outstock: datetime({ mode: 'string' })
});

export const inventoryRelations = relations(inventory, ({ one }) => ({
	product: one(product, {
		fields: [inventory.product_id],
		references: [product.id]
	}),
	costUnit: one(unit, {
		fields: [inventory.cost_unit_id],
		references: [unit.id]
	}),
	exspend: one(exspend, {
		fields: [inventory.exspend_id],
		references: [exspend.id]
	}),
	group: one(group, {
		fields: [inventory.group_id],
		references: [group.id]
	})
}));

export const subUnit = mysqlTable('sub_unit', {
	id: int().primaryKey().autoincrement(),
	qty_per_unit: int().notNull().default(0),
	price: decimal({ precision: 18, scale: 2 }).$type<number>(),
	unit_id: int()
		.references(() => unit.id)
		.notNull(),
	product_id: int().references(() => product.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	})
});
export const subUnitRelations = relations(subUnit, ({ one }) => ({
	unit: one(unit, {
		fields: [subUnit.unit_id],
		references: [unit.id]
	}),
	product: one(product, {
		fields: [subUnit.product_id],
		references: [product.id]
	})
}));

export const brand = mysqlTable('brand', {
	id: int().primaryKey().autoincrement(),
	name: varchar({ length: 255 })
});

export const brandRelations = relations(brand, ({ many }) => ({
	product: many(product)
}));

export const category = mysqlTable('category', {
	id: int().primaryKey().autoincrement(),
	name: varchar({ length: 255 })
});
export const categoryRelations = relations(category, ({ many }) => ({
	product: many(product),
	group: many(group)
}));

export const unit = mysqlTable('unit', {
	id: int().primaryKey().autoincrement(),
	unit: varchar({ length: 255 }),
	vaccine_dose: text()
});
export const unitRelations = relations(unit, ({ many }) => ({
	product: many(product),
	unitsToGroups: many(unitsToGroups)
}));

export const group = mysqlTable('group', {
	id: int().primaryKey().autoincrement(),
	name: varchar({ length: 100 }).notNull(),
	vaccine_dose: text(),
	category_id: int().references(() => category.id)
});
export const groupRelations = relations(group, ({ many, one }) => ({
	product: many(product),
	category: one(category, {
		fields: [group.category_id],
		references: [category.id]
	}),
	unitsToGroups: many(unitsToGroups)
}));

export const unitsToGroups = mysqlTable(
	'units_to_groups',
	{
		unit_id: int()
			.references(() => unit.id)
			.notNull(),
		group_id: int()
			.references(() => group.id)
			.notNull()
	},
	(t) => [primaryKey({ columns: [t.unit_id, t.group_id] })]
);

export const unitsToGroupsRelations = relations(unitsToGroups, ({ one }) => ({
	unit: one(unit, {
		fields: [unitsToGroups.unit_id],
		references: [unit.id]
	}),
	group: one(group, {
		fields: [unitsToGroups.group_id],
		references: [group.id]
	})
}));

export const productOrder = mysqlTable('product_order', {
	id: int().primaryKey().autoincrement(),
	created_at: datetime({ mode: 'string' }),
	price: decimal({ precision: 18, scale: 2 }).$type<number>(),
	total: decimal({ precision: 18, scale: 2 }).$type<number>(),
	qty: int().default(1).notNull(),
	qty_adjustment: int().default(1).notNull(),
	inventory_id: int().references(() => inventory.id),
	discount: varchar({ length: 50 }),
	product_id: int()
		.references(() => product.id)
		.notNull(),
	unit_id: int().references(() => unit.id),
	charge_id: int()
		.references(() => charge.id, { onDelete: 'cascade' })
		.notNull()
});

export const productOrderRelations = relations(productOrder, ({ one }) => ({
	product: one(product, {
		references: [product.id],
		fields: [productOrder.product_id]
	}),
	unit: one(unit, {
		references: [unit.id],
		fields: [productOrder.unit_id]
	}),
	charge: one(charge, {
		references: [charge.id],
		fields: [productOrder.charge_id]
	})
}));
