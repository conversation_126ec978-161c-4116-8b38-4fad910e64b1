import { db } from '$lib/server/db';
import { patient, progressNote, uploads, visit } from '$lib/server/schemas';
import { and, eq, isNull } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load = (async ({ url }) => {
	const patient_id = url.searchParams.get('patient_id') ?? '';
	const get_patient = await db.query.patient.findFirst({
		where: eq(patient.id, +patient_id),
		with: {
			provice: true,
			district: true,
			commune: true,
			village: true
		}
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'patient'), eq(uploads.related_id, +patient_id))
	});
	const count_opd = await db.$count(
		visit,
		and(eq(visit.patient_id, +patient_id), eq(visit.checkin_type, 'OPD'))
	);
	const count_ipd = await db.$count(
		progressNote,
		and(eq(progressNote.patient_id, +patient_id), isNull(progressNote.date_checkout))
	);
	const get_clinic_info = await db.query.clinicinfo.findFirst();
	const get_logo = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_id, get_clinic_info?.id ?? 0),
			eq(uploads.related_type, 'clinicinfo'),
			eq(uploads.mimeType, 'logo0')
		)
	});
	return {
		get_patient: {
			...get_patient,
			uploads: get_upload
		},
		get_clinic_info: {
			...get_clinic_info,
			logo0: get_logo?.filename
		},
		count_opd,
		count_ipd
	};
}) satisfies PageServerLoad;
