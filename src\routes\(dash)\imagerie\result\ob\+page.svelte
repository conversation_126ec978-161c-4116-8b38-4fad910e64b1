<script lang="ts">
	import Athtml from '$lib/coms/Athtml.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { addDays } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData, ActionData } from './$types';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}

	let { data, form }: Props = $props();
	let { get_imagerie_request, get_result_forms } = $derived(data);
	let loading = $state(false);
	let loading_form = $state(false);
	let loading_opt = $state(false);
	let image_id: number | null = $state(null);
	let filename = $state('');
	let form_id: number | null = $state(null);
	let form_id_for_edit: number | null = $state(null);
	let option_id: number | null = $state(null);
	let delivery = $state('');
	let find_form = $derived(get_result_forms.find((e) => e.id === form_id_for_edit));
</script>

<DeleteModal action="?/delete_picture" id={image_id!}>
	<input type="hidden" name="imagerie_request_id" value={get_imagerie_request?.id} />
	<input type="hidden" name="file_name" value={filename ?? ''} />
</DeleteModal>

<br />
<Form
	data_sveltekit_keepfocus={true}
	data_sveltekit_noscroll={true}
	enctype="multipart/form-data"
	action="?/update_img_result"
	method="post"
	bind:loading
>
	<div class="card bg-light">
		<div class="card-body">
			<div class="row">
				<div class="col-12">
					<button
						type="button"
						class="form-control text-start"
						data-bs-toggle="modal"
						data-bs-target="#imagerieform"
					>
						OB Form
					</button>
				</div>
			</div>
			<br />
			<input type="hidden" name="id" value={get_imagerie_request?.id || ''} />

			<div class="row">
				{#each get_result_forms as item (item.id)}
					{@const result = get_imagerie_request.resultImagerie.find(
						(e) => e.result_form_id === item.id
					)}
					<input type="hidden" value={item.id} name="result_form_id" />
					<div class="col-3 pt-2">
						<label class="text-bg-success pt-1 mb-1 px-2" for={`id${item.id.toString()}`}
							>{item.name}</label
						>
						{#if item.type === 'textarea'}
							<div class="input-group">
								<textarea id={`id${item.id.toString()}`} name="result_form" class="form-control"
									>{result?.result ?? ''}</textarea
								>
								{#if item.options?.length > 0}
									<span class="input-group-text">
										<Athtml html={item.options[0]?.name ?? ''} />
									</span>
								{/if}
							</div>
						{/if}

						{#if item.type === 'number'}
							<div class="input-group">
								<input
									value={result?.result ?? ''}
									id={`id${item.id.toString()}`}
									name="result_form"
									type="number"
									step="any"
									class="form-control"
								/>
								{#if item.options?.length > 0}
									<span class="input-group-text">
										<Athtml html={item.options[0]?.name ?? ''} /></span
									>
								{/if}
							</div>
						{/if}
						{#if item.type === 'text'}
							<div class="input-group">
								<input
									value={result?.result ?? ''}
									id={`id${item.id.toString()}`}
									name="result_form"
									type="text"
									class="form-control"
								/>
								{#if item.options?.length > 0}
									<span class="input-group-text">
										<Athtml html={item.options[0]?.name ?? ''} /></span
									>
								{/if}
							</div>
						{/if}
						{#if item.type === 'datetime-local'}
							<div class="input-group">
								{#if item.name == 'Period'}
									<input
										onchange={(e) => (delivery = e?.currentTarget?.value ?? '')}
										value={result?.result ?? ''}
										id={`id${item.id.toString()}`}
										name="result_form"
										type="date"
										class="form-control"
									/>
								{:else}
									<input
										value={result?.result ?? ''}
										id={`id${item.id.toString()}`}
										name="result_form"
										type="date"
										class="form-control"
									/>
								{/if}
								{#if item.name === 'Period'}
									<span class="input-group-text">
										<Athtml html={item.options[0]?.name ?? ''} /> &nbsp;
										{#if result?.result && !delivery}
											<DDMMYYYYFormat
												style="date"
												date={addDays(new Date(result?.result), 280).toString()}
											/>
										{/if}
										{#if result?.result && delivery}
											<DDMMYYYYFormat
												style="date"
												date={addDays(new Date(delivery), 280).toString()}
											/>
										{/if}
										{#if delivery && !result?.result}
											<DDMMYYYYFormat
												style="date"
												date={addDays(new Date(delivery), 280).toString()}
											/>
										{/if}
									</span>
								{:else}
									<span class="input-group-text">
										<Athtml html={item.options[0]?.name ?? ''} />
									</span>
								{/if}
							</div>
						{/if}

						{#if item.type === 'option'}
							<select
								value={result?.result ?? ''}
								class="form-control"
								name="result_form"
								id={`id${item.id.toString()}`}
							>
								<option value=""></option>
								{#each item?.options || [] as iitem}
									<option value={iitem.name}>
										{iitem?.name ?? ''}
									</option>
								{/each}
							</select>
						{/if}
					</div>
				{/each}
			</div>

			<div class="col-12">
				<div class=" pb-3">
					<label for="exampleInputFile">{locale.T('picture')}</label>
					<CropImage submit={true} name="file" />
				</div>
			</div>
			<div class="row">
				{#each get_imagerie_request?.uploads || [] as item}
					<div class="p-2 col-3">
						<img class="rounded img-thumbnail" src={item?.filename} alt="" />
						<button
							data-bs-toggle="modal"
							data-bs-target="#delete_modal"
							type="button"
							onclick={() => {
								image_id = item.id;
								filename = item.filename || '';
							}}
							class="btn btn-danger w-100">{locale.T('delete_')}</button
						>
					</div>
				{/each}
			</div>
		</div>

		<div class="card-footer text-end">
			<SubmitButton {loading} />
		</div>
	</div>
</Form>

<!-- Modal -->
<div
	class="modal fade"
	id="imagerieform"
	data-bs-backdrop="static"
	data-bs-keyboard="false"
	tabindex="-1"
	aria-labelledby="imagerieformLabel"
	aria-hidden="true"
>
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h1 class="modal-title fs-5" id="imagerieformLabel">{locale.T('form_doc')}</h1>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<Form
					class={form_id_for_edit
						? 'alert alert-success py-2 mx-2 mb-0'
						: 'alert alert-secondary py-2 mx-2 mb-0'}
					action="?/create_form"
					method="post"
					bind:loading={loading_form}
					fnSuccess={() => (form_id_for_edit = null)}
				>
					<div class="row g-1">
						<div class="col-8">
							<input
								value={find_form?.name ?? ''}
								autocomplete="off"
								class:border-danger={form?.message}
								placeholder={locale.T('name')}
								type="text"
								name="name"
								class="form-control"
							/>
						</div>
						<input type="hidden" name="id" value={form_id_for_edit} />
						<div class="col-3">
							<select value={find_form?.type ?? ''} name="type" class="form-control" id="">
								<option value="text"> {locale.T('text')}</option>
								<option value="textarea"> {locale.T('textarea')}</option>
								<option value="number"> {locale.T('number')}</option>
								<option value="datetime-local"> {locale.T('dates')}</option>
								<option value="option"> {locale.T('option')}</option>
							</select>
						</div>
						<div class="col-1">
							<SubmitButton loading={loading_form} icon_only={true} />
						</div>
					</div>
				</Form>
				<hr class="mx-2" />
				<div style="max-height: 500px;" class="card-body overflow-auto">
					<div class="p-2 mt-1">
						{#each get_result_forms as item, index}
							<!-- svelte-ignore a11y_no_static_element_interactions -->

							<div class="position-relative">
								<span
									class="position-absolute ms-3 top-0 start-0 translate-middle badge rounded-pill bg-primary"
								>
									{index + 1}
								</span>

								<div class="row g-0 text-bg-secondary py-1 px-2 justify-content-between">
									<div class="col-6 text-light">
										{#if item.type === 'text'}
											<i class="fa-solid fa-text-width"></i>
										{/if}
										{#if item.type === 'textarea'}
											<i class="fa-solid fa-text-height"></i>
										{/if}

										{#if item.type === 'number'}
											<i class="fa-solid fa-arrow-down-1-9"></i>
										{/if}
										{#if item.type === 'datetime-local'}
											<i class="fa-solid fa-calendar-days"></i>
										{/if}
										{#if item.type === 'option'}
											<i class="fa-regular fa-hand-pointer"></i>
										{/if}
										{item.name}
									</div>
									<div class="col-1">
										{#if get_result_forms[0].id !== item.id}
											<Form showToast={false} action="?/up" method="post">
												<input type="hidden" value={item.id} name="id" />
												<input type="hidden" value={item.index} name="index" />
												<button
													aria-label="up"
													type="submit"
													class="btn btn-link text-light m-0 p-0"
													><i class="fa-solid fa-arrow-up"></i></button
												>
											</Form>
										{/if}
									</div>
									<div class="col-1">
										{#if get_result_forms[get_result_forms.length - 1].id !== item.id}
											<Form showToast={false} action="?/down" method="post">
												<input type="hidden" value={item.id} name="id" />
												<input type="hidden" value={item.index} name="index" />
												<button
													aria-label="up"
													type="submit"
													class="btn btn-link text-light m-0 p-0"
													><i class="fa-solid fa-arrow-down"></i></button
												>
											</Form>
										{/if}
									</div>
									<div class="col-2">
										{#if item.type === 'option'}
											<button
												onclick={() => (form_id = form_id === item.id ? null : item.id)}
												type="button"
												class="btn btn-link text-light m-0 p-0"
												><i class="fa-solid fa-plus"></i> {locale.T('option')}</button
											>
										{:else}
											<button
												onclick={() => (form_id = form_id === item.id ? null : item.id)}
												type="button"
												class="btn btn-link text-light m-0 p-0"
												><i class="fa-solid fa-plus"></i> {locale.T('unit')}</button
											>
										{/if}
									</div>
									<div class="col-1 text-end">
										<button
											onclick={() =>
												(form_id_for_edit = form_id_for_edit === item.id ? null : item.id)}
											aria-label="edit_form"
											class="btn btn-link p-0 m-0 text-light text-end"
										>
											<i class="fa-solid fa-pen-to-square"></i>
										</button>
									</div>
									<div class="col-1 text-end">
										<Form action="?/delete_form" method="post" bind:loading={loading_form}>
											<input type="hidden" value={item.id} name="id" />
											<button
												aria-label="delete_form"
												type="button"
												onclick={(e) =>
													confirm(locale.T('confirm_delete')) &&
													e.currentTarget.form?.requestSubmit()}
												class="btn btn-link m-0 col p-0 text-light text-decoration-none"
												><i class="fa-solid fa-trash-can"></i></button
											>
										</Form>
									</div>
									{#if form_id === item.id}
										<Form action="?/create_option" method="post" bind:loading={loading_opt}>
											<input type="hidden" value={item.id} name="result_form_id" />
											<div class="row g-1">
												<div class="input-group input-group-sm">
													<input
														autocomplete="off"
														class:border-danger={form?.optErr}
														placeholder={locale.T('name')}
														type="text"
														name="name"
														class="form-control"
													/>
													<SubmitButton loading={loading_opt} icon_only={true} />
												</div>
											</div>
										</Form>
									{/if}
								</div>
								<table class="table table-sm table-bordered table-secondary">
									<tbody class="">
										{#each item.options || [] as opt (opt.id)}
											<tr>
												<td style="width: 100%;">
													<div class="row py-1 px-1 g-0">
														<div class="col-10">
															{#if option_id === opt.id}
																<Form
																	action="?/update_option"
																	method="post"
																	bind:loading={loading_opt}
																	fnSuccess={() => (option_id = null)}
																>
																	<input type="hidden" value={option_id} name="id" />
																	<div class="row g-1">
																		<div class="input-group input-group-sm">
																			<input
																				autocomplete="off"
																				class:border-danger={form?.optErr}
																				placeholder={locale.T('name')}
																				type="text"
																				name="name"
																				value={opt.name}
																				class="form-control"
																			/>
																			<SubmitButton loading={loading_opt} icon_only={true} />
																		</div>
																	</div>
																</Form>
															{:else}
																{opt.name}
															{/if}
														</div>
														<div class="col-1 text-end">
															<button
																aria-label="edit_option"
																onclick={() => {
																	option_id = opt.id === option_id ? null : opt.id;
																}}
																class="btn btn-link p-0 m-0 text-secondary text-decoration-none"
															>
																<i class="fa-solid fa-pen-to-square"></i>
															</button>
														</div>
														<div class="col-1 text-end">
															<Form
																action="?/delete_option"
																method="post"
																bind:loading={loading_form}
															>
																<input type="hidden" value={opt.id} name="id" />
																<button
																	aria-label="delete_option"
																	type="button"
																	onclick={(e) =>
																		confirm(locale.T('confirm_delete')) &&
																		e.currentTarget.form?.requestSubmit()}
																	class="btn btn-link m-0 col p-0 text-secondary text-decoration-none"
																	><i class="fa-solid fa-trash-can"></i></button
																>
															</Form>
														</div>
													</div>
												</td>
											</tr>
										{/each}
									</tbody>
								</table>
							</div>
						{/each}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
