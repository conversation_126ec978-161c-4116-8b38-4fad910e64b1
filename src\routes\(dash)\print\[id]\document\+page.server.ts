import { db } from '$lib/server/db';
import type { Actions, PageServerLoad } from './$types';
import { document, fields, progressNote, uploads, words } from '$lib/server/schemas';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { and, eq } from 'drizzle-orm';
import { fail } from '@sveltejs/kit';
import { fileHandle } from '$lib/server/upload';
import logError from '$lib/server/utils/logError';
export const load = (async ({ params, url }) => {
	const { id: progress_note_id } = params;
	const title = url.searchParams.get('title') ?? '';
	const get_document = await db.query.document.findFirst({
		where: and(eq(document.progress_note_id, +progress_note_id), eq(document.title, title)),
		with: {
			fields: true
		}
	});
	const get_documents = await db.query.document.findMany({
		where: eq(document.progress_note_id, +progress_note_id),
		with: {
			fields: true
		}
	});

	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +progress_note_id),
		with: {
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			department: true
		}
	});
	const get_words = await db.query.words.findMany({
		where: eq(words.category, 'patient')
	});
	const get_clinich_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'clinicinfo'), eq(uploads.mimeType, 'logo0'))
	});
	const get_upload_doc = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'document'), eq(uploads.related_id, get_document?.id || 0))
	});
	const get_document_setting = await db.query.documentSetting.findFirst({});

	return {
		get_document: {
			...get_document,
			uploads: get_upload_doc
		},
		get_document_setting: get_document_setting,
		get_progress_note,
		get_words,
		get_clinich_info,
		get_upload,
		get_documents
	};
}) satisfies PageServerLoad;
