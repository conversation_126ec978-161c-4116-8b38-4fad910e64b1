<script lang="ts">
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import type { PageServerData } from './$types';
	import Words from '$lib/coms-cu/Words.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_words } = $derived(data);
	let cheif_complaint = $state(data.get_visit?.subjective?.cheif_complaint ?? '');
	let history_of_present_illness = $state(
		data.get_visit?.subjective?.history_of_present_illness ?? ''
	);
	let current_medication = $state(data.get_visit?.subjective?.current_medication ?? '');
	let past_medical_history = $state(data.get_visit?.subjective?.past_medical_history ?? '');
	let allesgy_medicine = $state(data.get_visit?.subjective?.allesgy_medicine ?? '');
	let surgical_history = $state(data.get_visit?.subjective?.surgical_history ?? '');
	let family_and_social_history = $state(
		data.get_visit?.subjective?.family_and_social_history ?? ''
	);
	let pre_diagnosis = $state(data.get_visit?.subjective?.pre_diagnosis ?? '');
	let loading = $state(false);
</script>

<Words
	name="Cheif complaint"
	bind:value={cheif_complaint}
	words={get_words.filter((e) => e.type === 'cheif_complaint')}
	modal_name="cheif_complaint"
	category="subjective"
/>
<Words
	name="History of Present illness"
	bind:value={history_of_present_illness}
	words={get_words.filter((e) => e.type === 'history_of_present_illness')}
	modal_name="history_of_present_illness"
	category="subjective"
/>
<Words
	name="Current Medication"
	bind:value={current_medication}
	words={get_words.filter((e) => e.type === 'current_medication')}
	modal_name="current_medication"
	category="subjective"
/>
<Words
	name="Past medical history"
	bind:value={past_medical_history}
	words={get_words.filter((e) => e.type === 'past_medical_history')}
	modal_name="past_medical_history"
	category="subjective"
/>
<Words
	name="Allergy medicine"
	bind:value={allesgy_medicine}
	words={get_words.filter((e) => e.type === 'allesgy_medicine')}
	modal_name="allesgy_medicine"
	category="subjective"
/>
<Words
	name="Surgical history"
	bind:value={surgical_history}
	words={get_words.filter((e) => e.type === 'surgical_history')}
	modal_name="surgical_history"
	category="subjective"
/>
<Words
	name="Family and social history"
	bind:value={family_and_social_history}
	words={get_words.filter((e) => e.type === 'family_and_social_history')}
	modal_name="family_and_social_history"
	category="subjective"
/>
<Words
	name="pre Diagnosis"
	bind:value={pre_diagnosis}
	words={get_words.filter((e) => e.type === 'pre_diagnosis')}
	modal_name="pre_diagnosis"
	category="subjective"
/>

<div class="card bg-light">
	<div class="card-header fs-5">
		<span>#Subjective</span>
	</div>
	<Form
		data_sveltekit_keepfocus={true}
		data_sveltekit_noscroll={true}
		reset={false}
		bind:loading
		action="?/create_subjective"
		method="post"
	>
		<div class="card-body">
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#cheif_complaint"
						type="button"
						class="btn btn-outline-primary btn-sm">Cheif complaint</button
					>
				</div>
				<!-- <label for="cheif_coplaint" class="col-sm-3 col-form-label">Cheif complaint</label> -->
				<div class="col-sm-9">
					<textarea
						bind:value={cheif_complaint}
						rows="4"
						class="form-control"
						name="cheif_complaint"
					>
					</textarea>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#history_of_present_illness"
						type="button"
						class="btn btn-outline-primary btn-sm">History of Present illness</button
					>
				</div>
				<!-- <label for="cheif_coplaint" class="col-sm-3 col-form-label">Cheif complaint</label> -->
				<div class="col-sm-9">
					<textarea
						bind:value={history_of_present_illness}
						rows="4"
						class="form-control"
						name="history_of_present_illness"
						id="history_of_present_illness"
					>
					</textarea>
				</div>
			</div>
			<span class="btn btn-sm btn-info">Past Medicine History</span>
			<hr />
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#current_medication"
						type="button"
						class="btn btn-outline-primary btn-sm">Current Medication</button
					>
				</div>
				<div class="col-sm-9">
					<div class="input-group">
						<input
							bind:value={current_medication}
							id="current_medication"
							name="current_medication"
							type="text"
							class="form-control"
						/>
					</div>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#past_medical_history"
						type="button"
						class="btn btn-outline-primary btn-sm">Past medical history</button
					>
				</div>
				<div class="col-sm-9">
					<div class="input-group">
						<input
							bind:value={past_medical_history}
							id="past_medical_history"
							name="past_medical_history"
							type="text"
							class="form-control"
						/>
					</div>
				</div>
			</div>
			<div class="row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#allesgy_medicine"
						type="button"
						class="btn btn-outline-primary btn-sm">Allergy medicine</button
					>
				</div>

				<div class="col-sm-9">
					<div class="input-group">
						<input
							bind:value={allesgy_medicine}
							id="allesgy_medicine_"
							name="allesgy_medicine"
							type="text"
							class="form-control"
						/>
					</div>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#surgical_history"
						type="button"
						class="btn btn-outline-primary btn-sm">Surgical history</button
					>
				</div>

				<div class="col-sm-9">
					<div class="input-group">
						<input
							bind:value={surgical_history}
							id="surgical_history"
							name="surgical_history"
							type="text"
							class="form-control"
						/>
					</div>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#family_and_social_history"
						type="button"
						class="btn btn-outline-primary btn-sm">Family and social history</button
					>
				</div>

				<div class="col-sm-9">
					<div class="input-group">
						<input
							bind:value={family_and_social_history}
							id="family_and_social_history"
							name="family_and_social_history"
							type="text"
							class="form-control"
						/>
					</div>
				</div>
			</div>
			<div class=" row pb-2">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#pre_diagnosis"
						type="button"
						class="btn btn-success btn-sm">Pre-Diagnosis</button
					>
				</div>

				<div class="col-sm-9">
					<div class="input-group">
						<input
							bind:value={pre_diagnosis}
							id="pre_diagnosis"
							name="pre_diagnosis"
							type="text"
							class="form-control"
						/>
					</div>
				</div>
			</div>
		</div>
		<div class="card-footer">
			<div class="d-grid gap-2 d-md-flex justify-content-md-end">
				<SubmitButton {loading} />
			</div>
		</div>
	</Form>
</div>
