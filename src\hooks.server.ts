import * as auth from '$lib/server/auth';
import { type <PERSON>le } from '@sveltejs/kit';
// import { redis } from '$lib/server/db/redis';
import { sequence } from '@sveltejs/kit/hooks';
const handleAuth: Handle = async ({ event, resolve }) => {
	const sessionToken = event.cookies.get(auth.sessionCookieName);
	if (!sessionToken) {
		event.locals.user = null;
		event.locals.session = null;
		event.locals.roles = null;
		event.locals.departments = null;
		return resolve(event);
	}

	const { session, user, roles, departments } = await auth.validateSessionToken(sessionToken);
	if (session) {
		auth.setSessionTokenCookie(event, sessionToken, session.expires_at);
	} else {
		auth.deleteSessionTokenCookie(event);
	}

	event.locals.user = user;
	event.locals.session = session;
	event.locals.roles = roles;
	event.locals.departments = departments;

	return resolve(event);
};
export const handle = sequence(handleAuth);
