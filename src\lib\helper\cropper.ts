export type Point = { x: number; y: number };
export type Rectangle = { x: number; y: number; width: number; height: number };

export class ShapeCropper {
	private canvas: HTMLCanvasElement;
	private ctx: CanvasRenderingContext2D;
	private image: HTMLImageElement | null = null;
	private cropRect: Rectangle | null = null;
	private isDrawing: boolean = false;
	private isMoving: boolean = false;
	private startPoint: Point = { x: 0, y: 0 };
	private dragOffset: Point = { x: 0, y: 0 };
	private aspectRatio: number | null = null;
	private isFixedAspect: boolean = false;

	constructor(canvas: HTMLCanvasElement) {
		this.canvas = canvas;
		const ctx = this.canvas.getContext('2d');
		if (!ctx) throw new Error('Could not get canvas context');
		this.ctx = ctx;

		this.setupEventListeners();
	}

	public setAspectRatio(ratio: number | null, fixed: boolean = true): void {
		this.aspectRatio = ratio;
		this.isFixedAspect = fixed;
		if (this.cropRect && ratio) {
			this.applyAspectRatio();
		}
	}

	private applyAspectRatio(): void {
		if (!this.cropRect || !this.aspectRatio) return;

		if (this.isFixedAspect) {
			// Maintain fixed aspect ratio
			this.cropRect.height = this.cropRect.width / this.aspectRatio;
		} else {
			// Snap to nearest aspect ratio while allowing dynamic resizing
			const currentRatio = this.cropRect.width / this.cropRect.height;
			if (Math.abs(currentRatio - this.aspectRatio) < 0.1) {
				this.cropRect.height = this.cropRect.width / this.aspectRatio;
			}
		}
	}

	public loadImage(imageFile: File): Promise<void> {
		return new Promise((resolve, reject) => {
			this.image = new Image();
			this.image.onload = () => {
				this.initializeCanvas();
				resolve();
			};
			this.image.onerror = () => {
				reject(new Error('Failed to load image'));
			};

			const reader = new FileReader();
			reader.onload = (e) => {
				if (e.target?.result) {
					this.image!.src = e.target.result as string;
				}
			};
			reader.readAsDataURL(imageFile);
		});
	}

	private initializeCanvas(): void {
		if (!this.image) return;

		// Scale canvas to fit container while maintaining aspect ratio
		const container = this.canvas.parentElement;
		if (container) {
			const containerWidth = container.clientWidth;
			const scale = containerWidth / this.image.width;

			this.canvas.width = this.image.width;
			this.canvas.height = this.image.height;
			this.canvas.style.width = `${containerWidth}px`;
			this.canvas.style.height = `${this.image.height * scale}px`;
		} else {
			this.canvas.width = this.image.width;
			this.canvas.height = this.image.height;
		}

		this.redrawCanvas();
	}

	private setupEventListeners(): void {
		this.canvas.addEventListener('mousedown', this.handleMouseDown);
		this.canvas.addEventListener('mousemove', this.handleMouseMove);
		this.canvas.addEventListener('mouseup', this.handleMouseUp);
		this.canvas.addEventListener('mouseleave', this.handleMouseLeave);

		// Touch events with proper typing
		this.canvas.addEventListener('touchstart', this.handleTouchStart as EventListener, {
			passive: false
		});
		this.canvas.addEventListener('touchmove', this.handleTouchMove as EventListener, {
			passive: false
		});
		this.canvas.addEventListener('touchend', this.handleTouchEnd as EventListener);
		this.canvas.addEventListener('touchcancel', this.handleTouchEnd as EventListener);
	}

	private getPositionFromEvent(e: MouseEvent | Touch): Point {
		const rect = this.canvas.getBoundingClientRect();
		const scaleX = this.canvas.width / rect.width;
		const scaleY = this.canvas.height / rect.height;

		return {
			x: (e.clientX - rect.left) * scaleX,
			y: (e.clientY - rect.top) * scaleY
		};
	}

	private isPointInRect(point: Point, rect: Rectangle): boolean {
		return (
			point.x >= rect.x &&
			point.x <= rect.x + rect.width &&
			point.y >= rect.y &&
			point.y <= rect.y + rect.height
		);
	}

	private handleMouseDown = (e: MouseEvent): void => {
		e.preventDefault();
		this.handleStart(this.getPositionFromEvent(e));
	};

	private handleTouchStart = (e: TouchEvent): void => {
		e.preventDefault();
		if (e.touches.length === 1) {
			this.handleStart(this.getPositionFromEvent(e.touches[0]));
		}
	};

	private handleStart(pos: Point): void {
		if (!this.image) return;

		if (this.cropRect && this.isPointInRect(pos, this.cropRect)) {
			this.isMoving = true;
			this.dragOffset = {
				x: pos.x - this.cropRect.x,
				y: pos.y - this.cropRect.y
			};
		} else {
			this.isDrawing = true;
			this.startPoint = pos;
			this.cropRect = { x: pos.x, y: pos.y, width: 0, height: 0 };
		}
	}

	private handleMouseMove = (e: MouseEvent): void => {
		e.preventDefault();
		this.handleMove(this.getPositionFromEvent(e));
	};

	private handleTouchMove = (e: TouchEvent): void => {
		e.preventDefault();
		if (e.touches.length === 1) {
			this.handleMove(this.getPositionFromEvent(e.touches[0]));
		}
	};

	private handleMove(pos: Point): void {
		if (!this.image) return;

		this.redrawCanvas();

		if (this.isDrawing && this.cropRect) {
			this.cropRect.width = pos.x - this.startPoint.x;
			this.cropRect.height = pos.y - this.startPoint.y;

			if (this.aspectRatio) {
				this.applyAspectRatio();
			}
		} else if (this.isMoving && this.cropRect) {
			this.cropRect.x = pos.x - this.dragOffset.x;
			this.cropRect.y = pos.y - this.dragOffset.y;
		}

		this.drawCropRect();
	}

	private handleMouseUp = (): void => {
		this.handleEnd();
	};

	private handleTouchEnd = (): void => {
		this.handleEnd();
	};

	private handleEnd(): void {
		this.isDrawing = false;
		this.isMoving = false;
	}

	private handleMouseLeave = (): void => {
		this.handleEnd();
	};

	private redrawCanvas(): void {
		if (!this.image) return;

		this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
		this.ctx.drawImage(this.image, 0, 0, this.canvas.width, this.canvas.height);
	}

	private drawCropRect(): void {
		if (!this.cropRect) return;

		this.ctx.save();

		// Draw overlay outside crop area
		this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
		this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

		// Clear the crop area
		this.ctx.globalCompositeOperation = 'destination-out';
		this.ctx.fillRect(this.cropRect.x, this.cropRect.y, this.cropRect.width, this.cropRect.height);

		// Draw crop rectangle border
		this.ctx.globalCompositeOperation = 'source-over';
		this.ctx.strokeStyle = '#ffffff';
		this.ctx.lineWidth = 2;
		this.ctx.setLineDash([5, 5]);
		this.ctx.strokeRect(
			this.cropRect.x,
			this.cropRect.y,
			this.cropRect.width,
			this.cropRect.height
		);

		this.ctx.restore();
	}

	public cropImage(): HTMLCanvasElement {
		if (!this.cropRect || !this.image) {
			throw new Error('No image or rectangle selected for cropping');
		}

		const outputCanvas = document.createElement('canvas');
		outputCanvas.width = Math.abs(this.cropRect.width);
		outputCanvas.height = Math.abs(this.cropRect.height);
		const outputCtx = outputCanvas.getContext('2d');
		if (!outputCtx) throw new Error('Could not create output canvas context');

		const srcX = this.cropRect.width < 0 ? this.cropRect.x + this.cropRect.width : this.cropRect.x;
		const srcY =
			this.cropRect.height < 0 ? this.cropRect.y + this.cropRect.height : this.cropRect.y;
		const srcWidth = Math.abs(this.cropRect.width);
		const srcHeight = Math.abs(this.cropRect.height);

		outputCtx.drawImage(
			this.image,
			srcX,
			srcY,
			srcWidth,
			srcHeight,
			0,
			0,
			outputCanvas.width,
			outputCanvas.height
		);

		return outputCanvas;
	}

	public reset(): void {
		this.cropRect = null;
		this.redrawCanvas();
	}

	public destroy(): void {
		// Mouse events
		this.canvas.removeEventListener('mousedown', this.handleMouseDown);
		this.canvas.removeEventListener('mousemove', this.handleMouseMove);
		this.canvas.removeEventListener('mouseup', this.handleMouseUp);
		this.canvas.removeEventListener('mouseleave', this.handleMouseLeave);

		// Touch events
		this.canvas.removeEventListener('touchstart', this.handleTouchStart);
		this.canvas.removeEventListener('touchmove', this.handleTouchMove);
		this.canvas.removeEventListener('touchend', this.handleTouchEnd);
		this.canvas.removeEventListener('touchcancel', this.handleTouchEnd);
	}
}
