import { relations } from 'drizzle-orm';
import { float, mysqlTable, text, int, datetime, varchar } from 'drizzle-orm/mysql-core';
import { progressNote, visit } from './visit';
import { staff } from './staff';
import { activeDepartment } from './departmentBed';

export const vitalSign = mysqlTable('vital_sign', {
	id: int().primaryKey().autoincrement(),
	dbp: float(),
	sbp: float(),
	pulse: float(),
	t: float(),
	sp02: float(),
	height: float(),
	weight: float(),
	rr: float(),
	bmi: float(),
	visit_id: int().references(() => visit.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	// form IPD
	datetime: datetime({ mode: 'string' }),
	stool: varchar({ length: 255 }),
	urine: varchar({ length: 255 }),
	note: text(),
	piv: int(),
	drink: int(),
	nasogastric_tube_in: int(),
	nasogastric_tube_out: int(),
	fluid_out: int(),
	vomiting: int(),
	by: int().references(() => staff.id),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	active_department_id: int().references(() => activeDepartment.id, {
		onDelete: 'cascade'
	})
});
export const vitalSignRelations = relations(vitalSign, ({ one }) => ({
	by: one(staff, {
		fields: [vitalSign.by],
		references: [staff.id]
	}),
	progressNote: one(progressNote, {
		fields: [vitalSign.progress_note_id],
		references: [progressNote.id]
	}),
	visit: one(visit, {
		fields: [vitalSign.visit_id],
		references: [visit.id]
	}),
	activeDepartment: one(activeDepartment, {
		fields: [vitalSign.active_department_id],
		references: [activeDepartment.id]
	})
}));
