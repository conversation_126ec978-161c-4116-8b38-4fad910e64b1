<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	let loading = $state(false);
	interface Props {
		action?: string;
		slug?: string;
		id?: number | null | string;
		delete_modal?: string;
		children?: import('svelte').Snippet;
	}

	let {
		action = '',
		slug = '',
		id = $bindable(null),
		delete_modal = 'delete_modal',
		children
	}: Props = $props();
	function clear() {
		id = null;
		slug = '';
	}
</script>

<div class="modal fade" id={delete_modal} data-bs-backdrop="static">
	<Form
		{action}
		class="modal-dialog modal-md"
		fnSuccess={() => document.getElementById('close_delete_modal')?.click()}
		method="POST"
		bind:loading
	>
		<input value={id} type="hidden" name="id" />
		<input value={slug} type="hidden" name="slug" />
		<div class="modal-content rounded-3 shadow">
			<div class="modal-header py-2 justify-content-center text-bg-danger">
				<span class="fs-3">
					<i class="fa-regular fa-trash-can"></i>
					{locale.T('delete_')}
				</span>
			</div>
			<div class="modal-body">
				{@render children?.()}
				<ul class="list-group">
					<li class="list-group-item">
						<i class="fa-regular fa-circle-question"></i>
						{locale.T('verify')}
						{locale.T('id')} <span class="text-danger">{id}</span>
						{locale.T('will_delete')}
					</li>
					<li class="list-group-item">
						<i class="fa-solid fa-triangle-exclamation"></i>
						{locale.T('confirm_delete')}
					</li>
				</ul>
			</div>
			<div class="modal-footer flex-nowrap p-0">
				<button
					onclick={clear}
					id="close_delete_modal"
					type="button"
					class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
					data-bs-dismiss="modal">{locale.T('no')}</button
				>
				<button
					data-bs-dismiss="modal"
					disabled={loading}
					type="submit"
					class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
				>
					<strong>{locale.T('yes')}</strong>
				</button>
			</div>
		</div>
	</Form>
</div>
