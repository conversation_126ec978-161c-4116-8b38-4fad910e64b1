const foo = [
	{
		id: 1,
		name: 'apple',
		type: 'fruit'
	},
	{
		id: 2,
		name: 'orange',
		type: 'fruit'
	},
	{
		id: 3,
		name: 'car',
		type: 'vehicle'
	},
	{
		id: 3,
		name: 'motorbike',
		type: 'vehicle'
	}
];

// const baa = [
// 	{
// 		type: 'fruit',
// 		items: [
// 			{
// 				id: 1,
// 				name: 'apple'
// 			},
// 			{
// 				id: 2,
// 				name: 'orange'
// 			}
// 		]
// 	},
// 	{
// 		type: 'vehicle',
// 		items: [
// 			{
// 				id: 3,
// 				name: 'car'
// 			},
// 			{
// 				id: 3,
// 				name: 'motorbike'
// 			}
// 		]
// 	}
// ];

const map = new Map();

foo.forEach((item) => {
	if (!map.has(item.type)) {
		map.set(item.type, []);
	}
	map.get(item.type).push({ id: item.id, name: item.name });
});

const grouped = Array.from(map, ([type, items]) => ({ type, items }));

console.log(grouped);
