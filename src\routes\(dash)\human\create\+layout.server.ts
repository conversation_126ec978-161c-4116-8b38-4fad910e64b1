import type { LayoutServerLoad } from './$types';
import { db } from '$lib/server/db';
import { commune, district, uploads, product, staff, village } from '$lib/server/schemas';
import { and, eq } from 'drizzle-orm';
import { permision } from '$lib/server/auth/permision';
export const load: LayoutServerLoad = async ({ parent, url, locals }) => {
	await parent();
	const staff_id = url.searchParams.get('staff_id') ?? '';
	permision.go({
		locals,
		staff_id: +staff_id,
		redirect_: '/staff'
	});

	const get_staff = await db.query.staff.findFirst({
		where: eq(staff.id, +staff_id),
		with: {
			user: true,
			commune: true,
			district: true,
			title: true,
			village: true,
			provice: true,
			staffToRole: {
				with: {
					role: true
				}
			},
			staffToDemartment: {
				with: {
					department: true
				}
			}
		}
	});

	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staff'), eq(uploads.related_id, +staff_id))
	});
	const get_upload_sign = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staffSign'), eq(uploads.related_id, +staff_id))
	});
	return {
		get_staff: {
			...get_staff,
			uploads: get_upload,
			sign: get_upload_sign
		}
	};
};
