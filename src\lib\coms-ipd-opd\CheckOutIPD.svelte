<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import DD<PERSON>YYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { calculateDifference } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	import type { LayoutServerData } from '../../routes/(dash)/ipd/[progress_note_id]/$types';
	type Data = Pick<LayoutServerData, 'get_progress_note'>;
	let loading = $state(false);
	interface Props {
		data: Data;
		class?: string;
		children?: import('svelte').Snippet;
	}

	let { data, class: className = 'btn btn-primary', children }: Props = $props();
	let { get_progress_note } = $derived(data);
	let getDayStay = $derived(calculateDifference(get_progress_note?.date_checkup, new Date()));
	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
	let active_department = $derived(
		get_progress_note?.activeDepartment.find((e) => e.active === true)
	);
	let active_bed = $derived(active_department?.activeBed.find((e) => e.active === true));
</script>

<button
	type="button"
	data-bs-toggle="modal"
	data-bs-target={'#'.concat(id?.toString() ?? '')}
	class={className}
>
	{#if get_progress_note?.date_checkout}
		{locale.T('checkout')}
	{:else}
		{locale.T('discharge_and_go_home')}
	{/if}
</button>

<div class="modal fade" tabindex="-1" role="dialog" {id} data-bs-backdrop="static">
	<div class="modal-dialog" role="document">
		<div class="modal-content rounded-3 shadow">
			{#if get_progress_note?.date_checkout === null}
				<Form
					action="/ipd/checkout/?/check_out"
					bind:loading
					fnSuccess={() => document.getElementById('close_confirm_submit')?.click()}
					method="post"
				>
					<input value={get_progress_note?.id ?? ''} type="hidden" name="progress_note_id" />
					<input
						value={get_progress_note?.date_checkout ?? ''}
						type="hidden"
						name="date_checkout"
					/>
					<input value={get_progress_note?.billing?.status} type="hidden" name="status" />
					<input value={get_progress_note?.inclund_pay} type="hidden" name="inclund_pay" />
					<input value={active_department?.id} type="hidden" name="active_department_id" />
					<input value={active_bed?.id} type="hidden" name="active_bed_id" />
					<div class="modal-header py-2 justify-content-center text-bg-warning">
						<span class="fs-3">
							{locale.T('discharge_and_go_home')}
						</span>
					</div>
					<div class="modal-body">
						{#if get_progress_note?.service.length && get_progress_note.date_checkout === null}
							<ol class="list-group list-group-numbered">
								{#each get_progress_note?.service || [] as item}
									<li class="list-group-item">
										{item.product?.products}
										{#if item.is_paid_ipd}
											<span class="float-end btn btn-sm btn-warning rounded py-0"
												>{locale.T('paid')}</span
											>
										{/if}
									</li>
								{/each}
							</ol>
							<ul class="list-group pt-2">
								{#if get_progress_note.presrciption.length}
									<li class="list-group-item">
										<input
											name="prescription"
											class="form-check-input me-1"
											type="checkbox"
											value="prescription"
											id="prescription"
										/>
										<label class="form-check-label stretched-link" for="prescription"
											>គិតប្រាក់សរុប រួមទាំងថ្លៃថ្នាំលេបនៅផ្ទះ</label
										>
									</li>
								{/if}
								<li class="list-group-item">
									<input
										class="form-check-input me-1"
										type="checkbox"
										value="treatment"
										id="treatment"
										name="treatment"
									/>
									<label class="form-check-label stretched-link" for="treatment"
										>គិតប្រាក់សរុប រួមទាំងថ្លៃព្យាបាល</label
									>
								</li>
							</ul>
						{:else}
							<input value="true" type="hidden" name="prescription" />
							<input value="true" type="hidden" name="treatment" />
						{/if}
						<div class="alert alert-primary py-1 mt-2 mb-2">
							<span>{locale.T('date')}</span>
							<DDMMYYYYFormat date={get_progress_note?.date_checkup} /> -
							<DDMMYYYYFormat date={new Date().toJSON()} />
						</div>
						<!-- <div class="alert alert-danger py-1 mt-0">
						{#if getDayStay?.days}
							<span>{locale.T('day_stay')}</span>
							{getDayStay?.days}
							<span>{locale.T('day')}</span>
						{/if}
						{#if getDayStay?.hours}
							{getDayStay?.hours}
							<span>{locale.T('hour')}</span>
						{/if}
					</div> -->
					</div>

					<div class="modal-footer flex-nowrap p-0">
						<button
							id="close_confirm_submit"
							type="button"
							class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
							data-bs-dismiss="modal">{locale.T('no')}</button
						>

						<button
							disabled={loading}
							type="submit"
							class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
						>
							<strong>{locale.T('yes')}</strong>
						</button>
					</div>
				</Form>
			{:else}
				<Form
					action="/ipd/checkout/?/dis_check_out"
					bind:loading
					fnSuccess={() => document.getElementById('close_confirm_submit')?.click()}
					method="post"
				>
					<input value={get_progress_note?.id ?? ''} type="hidden" name="progress_note_id" />
					<input
						value={get_progress_note?.date_checkout ?? ''}
						type="hidden"
						name="date_checkout"
					/>
					<input value={get_progress_note?.billing?.status} type="hidden" name="status" />
					<input value={get_progress_note?.inclund_pay} type="hidden" name="inclund_pay" />
					<input value={active_department?.id} type="hidden" name="active_department_id" />
					<input value={active_bed?.id} type="hidden" name="active_bed_id" />
					<div class="modal-header py-2 justify-content-center text-bg-warning">
						<span class="fs-3">
							{locale.T('send_back_to_checking')}
						</span>
					</div>
					<div class="modal-body">
						<ul class="list-group">
							<li class="list-group-item">
								<i class="fa-solid fa-triangle-exclamation"></i>
								{locale.T('after_done_of_pay_you_can_not_edit_infomations')}
							</li>
						</ul>
					</div>
					<div class="modal-footer flex-nowrap p-0">
						<button
							id="close_confirm_submit"
							type="button"
							class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
							data-bs-dismiss="modal">{locale.T('no')}</button
						>
						<button
							disabled={loading}
							type="submit"
							class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
						>
							<strong>{locale.T('yes')}</strong>
						</button>
					</div>
				</Form>
			{/if}
		</div>
	</div>
</div>
