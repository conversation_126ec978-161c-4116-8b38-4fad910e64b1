<script lang="ts">
	import Barcode from '$lib/coms/Barcode.svelte';
	import { khmerDate } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from '../../routes/(dash)/patient/histrory/$types';
	interface Props {
		patient_info: PageServerData['get_patient'];
		is_show?: boolean;
		children?: import('svelte').Snippet;
	}
	let { patient_info, is_show, children }: Props = $props();
	let show = $state(false);
	let uid = $derived(`barcode`.concat(patient_info?.id?.toString() ?? ''));
	function printDiv(id: string) {
		setTimeout(() => {
			const element = document.getElementById(id) as HTMLElement;
			const body = document.body;
			// Store current children of the body in an array
			const originalChildren: Node[] = [...body.children];
			// Clone the element to be printed
			const elementClone = element.cloneNode(true) as HTMLElement;
			// Remove all current children from the body
			while (body.firstChild) {
				body.firstChild.remove();
			}
			// Add the cloned element to be printed
			body.append(elementClone);
			// Call window.print and revert the state back after printing
			window.print();
			// Revert the state back
			while (body.firstChild) {
				(body.firstChild as HTMLElement).remove();
			}

			for (const child of originalChildren) body.append(child);
			show = false;
		}, 100);
	}
</script>

<button
	aria-label="print barcode"
	class="btn btn-success btn-sm"
	onclick={() => {
		show = true;
		printDiv(uid);
	}}
>
	{#if children}
		{@render children?.()}
	{:else}
		<i class="fa-solid fa-barcode"></i>
	{/if}
</button>
{#if show || is_show}
	<div id={uid} style="width: 50mm;height: 25mm;zoom:130%" class="card bg-light p-2 mt-2 ms-2">
		<div class="text-center" style="font-size: 8px;">
			{locale.T('patient_name')} : {patient_info.name_khmer}
			{`(${patient_info.name_latin})`}
		</div>
		<div class="text-center" style="font-size: 7px;">
			{locale.T('gender')}
			{patient_info?.gender?.toLowerCase() === 'male' ? locale.T('male') : locale.T('female')}
			DOB {khmerDate(patient_info?.dob, 'date')}
		</div>
		<Barcode text={patient_info?.id?.toString() ?? ''} />
		<div class="text-center pt-1" style="font-size: 7px;">
			{locale.T('id')}
			{patient_info.id}
		</div>
	</div>
{/if}

<style>
	@media print {
		@page {
			size: A4;
			margin: 0mm;
		}
	}
</style>
