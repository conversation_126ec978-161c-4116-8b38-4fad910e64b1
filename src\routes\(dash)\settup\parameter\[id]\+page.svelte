<script lang="ts">
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import UpdateParameter from '$lib/coms-cu/UpdateParameter.svelte';
	import { store } from '$lib/store/store.svelte';
	import type { ActionData, PageServerData } from './$types';
	import Athtml from '$lib/coms/Athtml.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}

	let { form, data }: Props = $props();
	let parameter_id: number = $state(0);
	let { get_parameters, get_product_labo, get_para_units } = $derived(data);
	let find_parameter = $derived(get_parameters.filter((e) => e.id === parameter_id));
</script>

<DeleteModal action="?/delete_parameter" id={find_parameter[0]?.id} />
<UpdateParameter
	data={{
		get_para_units: get_para_units,
		get_parameters: find_parameter
	}}
	{form}
	bind:parameter_id
/>

<div class="row">
	<div class="col-sm-6">
		<a href="/settup/parameter/group" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/parameter/group" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-vial nav-icon"></i>
					{locale.T('group')}
					{locale.T('parameter')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-vials"></i>
					{locale.T('parameter')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header fs-4">
		<span>{locale.T('id')}#{get_product_labo?.id ?? ''} </span>
	</div>
	<div>
		<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0">
			<table class="table table-bordered table-hover text-nowrap table-light">
				<thead class="table-active table-light sticky-top">
					<tr>
						<th class="text-center" style="width: 5%;">{locale.T('n')}</th>
						<th>{locale.T('parameter')}</th>
						<th>{locale.T('description')}</th>
						<th class="text-center">{locale.T('unit')}</th>
						<th class="text-center">{locale.T('gender')}</th>
						<th class="text-center">{locale.T('range_normal')}</th>
						<th></th>
					</tr>
				</thead>
				<tbody>
					{#each get_parameters || [] as item, index}
						<tr>
							<td class="text-center">{index + 1}</td>
							<td>{item?.parameter ?? ''}</td>
							<td>
								<Athtml html={item.description ?? ''} />
							</td>
							<td class="text-center"><Athtml html={item?.paraUnit?.unit ?? ''} /> </td>
							<td class="text-center">{item?.gender ?? ''}</td>
							<td class="text-center">
								<div>
									<span>
										{item?.mini === 0 ? '' : item.mini}
									</span>
									<span>
										{item.sign ?? ''}
									</span>
									<span>
										{item?.maxi === 0 ? '' : item.maxi}
									</span>
								</div>
							</td>
							<td>
								<button
									aria-label="updateparamater"
									onclick={() => {
										parameter_id = 0;
										parameter_id = item.id;
									}}
									type="button"
									class="btn btn-primary btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#update_parameter"
									><i class="fa-solid fa-file-pen"></i>
								</button>
								<button
									aria-label="deletemodal"
									onclick={() => {
										parameter_id = 0;
										parameter_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	</div>
</div>
