import { db } from '$lib/server/db';
import { and, desc, eq, or } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { billing } from '$lib/server/schemas';
import { betweenHelper, pagination } from '$lib/server/utils';

export const load: PageServerLoad = async ({ parent, url }) => {
	await parent();
	const get_billings = await db.query.billing.findMany({
		where: and(
			betweenHelper(url, billing.created_at),
			eq(billing.status, 'paying'),
			or(eq(billing.billing_type, 'IPD'), eq(billing.billing_type, 'CHECKING'))
		),
		with: {
			serviceType: true,
			paymentService: true,
			charge: {
				with: {
					productOrder: {
						with: {
							product: true
						}
					}
				}
			},
			patient: true,
			progressNote: {
				with: {
					presrciption: true,
					patient: true,
					staff: true,
					department: true
				}
			},
			visit: {
				with: {
					presrciption: true,
					patient: true,
					staff: true,
					department: true
				}
			}
		},

		orderBy: desc(billing.created_at),
		...pagination(url)
	});
	const count = await db.$count(
		billing,
		and(
			betweenHelper(url, billing.created_at),
			eq(billing.status, 'paying'),
			or(eq(billing.billing_type, 'IPD'), eq(billing.billing_type, 'CHECKING'))
		)
	);
	return {
		get_billings,
		items: count
	};
};
