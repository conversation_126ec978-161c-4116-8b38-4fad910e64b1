<script lang="ts">
	import { enhance } from '$app/forms';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from './Form.svelte';
	interface Props {
		action?: string;
		slug?: string;
		id?: number | undefined;
	}

	let { action = '', slug = '', id = undefined }: Props = $props();
	let loading = $state(false);
</script>

<div class="modal fade" id="confirme_modal" data-bs-backdrop="static">
	<Form
		method="post"
		bind:loading
		fnSuccess={() => document.getElementById('close_confirme_modal')?.click()}
		{action}
		class="modal-dialog modal-dialog-centered modal-sm"
	>
		<input value={id} type="hidden" name="id" />
		<input value={slug} type="hidden" name="slug" />
		<div class="modal-content">
			<div class="modal-header">
				<h3 class="modal-title">{locale.T('confirm_yes')}</h3>
			</div>
			<div class="modal-footer justify-content-between">
				<button type="submit" class="btn btn-danger">
					{#if loading}
						<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
					{:else}
						<i class="fa-solid fa-square-check"></i>
					{/if}
					{locale.T('yes')}</button
				>
				<button
					id="close_confirme_modal"
					type="button"
					class="btn btn-secondary"
					data-bs-dismiss="modal"
					><i class="fa-solid fa-rectangle-xmark"></i> {locale.T('no')}</button
				>
			</div>
		</div>
	</Form>
</div>
