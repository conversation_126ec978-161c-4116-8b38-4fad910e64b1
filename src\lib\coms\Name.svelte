<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { Staff } from '$lib/type';
	interface Props {
		staff?: Staff | null;
		class?: string;
	}
	let { staff, class: className = '' }: Props = $props();
</script>

{#if locale.L === 'en'}
	<span class={className}>
		{staff?.name_latin || staff?.name_khmer}
	</span>
{/if}
{#if locale.L === 'km'}
	<span class={className}>
		{staff?.name_khmer}
	</span>
{/if}
