<script lang="ts">
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import type { PageServerData } from './$types';
	import Diagnosis from '$lib/coms-ipd-opd/Diagnosis.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let isEdit = $state(false);
	let diagnosis_id = $state(0);
	let { get_diagnosis, get_diagnosisTypes } = $derived(data);
	let diagnosis_ = $state(data.get_accessment?.diagnosis_or_problem ?? '');
	let differential = $state(data.get_accessment?.differential_diagnosis ?? '');
	let loading = $state(false);
	let assessment_process = $state(data.get_accessment?.assessment_process ?? '');
</script>

<Diagnosis
	bind:diagnosis_id
	bind:isEdit
	bind:value={diagnosis_}
	modal_name="DiagnosisOrProblem"
	name="Diagnosis or Problem"
	diagnosis={get_diagnosis}
	diagnosis_type={get_diagnosisTypes}
/>
<Diagnosis
	bind:diagnosis_id
	bind:isEdit
	bind:value={differential}
	modal_name="DifferentialDiagnosis"
	name="Differential Diagnosis"
	diagnosis={get_diagnosis}
	diagnosis_type={get_diagnosisTypes}
/>

<div class="card bg-light">
	<div class="card-header fs-5">
		<span># Assessment</span>
	</div>
	<Form reset={false} bind:loading action="?/create_accessment" method="post">
		<div class="card-body">
			<div class=" row pb-3">
				<!-- <label for="familly_history" class="col-sm-3 col-form-label">Diagnosis or Problem :</label> -->
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#DiagnosisOrProblem"
						type="button"
						class="btn btn-outline-primary btn-sm">Diagnosis or Problem</button
					>
				</div>
				<div class="col-sm-9">
					<div class="input-group">
						<div class="input-group">
							<textarea
								bind:value={diagnosis_}
								id="diagnosis"
								name="diagnosis_or_problem"
								rows="5"
								class="form-control"
							></textarea>
						</div>
					</div>
				</div>
			</div>
			<div class=" row pb-3">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#DifferentialDiagnosis"
						type="button"
						class="btn btn-outline-primary btn-sm">Differential Diagnosis</button
					>
				</div>

				<div class="col-sm-9">
					<div class="input-group">
						<textarea
							bind:value={differential}
							id="diagnosis_differential"
							name="diagnosis_differential"
							rows="5"
							class="form-control"
						></textarea>
					</div>
				</div>
			</div>
			<div class=" row">
				<label for="diagnosis_differential" class="col-sm-3 col-form-label"
					>Assessment Progress</label
				>
				<div class="col-sm-9">
					<textarea
						bind:value={assessment_process}
						rows="4"
						class="form-control"
						name="assessment_process"
					>
					</textarea>
				</div>
			</div>
		</div>
		<div class="card-footer text-end">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
