<script lang="ts">
	import type { PageServerData, ActionData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { YYYYMMDD_Format } from '$lib/helper';
	import Form from '$lib/coms-form/Form.svelte';
	import Address from '$lib/coms-cu/Address.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}
	let { data, form }: Props = $props();
	let { get_staff, get_provinces, get_districts, get_conmunies, get_vilages ,get_currency} = $derived(data);
	let loading = $state(false);
</script>

<div class="card bg-light">
	<Form
		reset={false}
		action="?/create_staff"
		method="post"
		enctype="multipart/form-data"
		bind:loading
		class="card-body"
	>
		{#if get_staff?.id}
			<input type="hidden" name="staff_id" value={get_staff?.id} />
		{/if}
		{#if get_staff?.uploads?.id}
			<input type="hidden" name="old_image" value={get_staff?.uploads?.id} />
		{/if}

		<div class="row pb-3">
			<div class="col-md-6">
				<input value={get_staff?.id} type="hidden" name="patient_id" />
				<input value={get_staff?.uploads?.filename ?? ''} type="hidden" name="old_image" />
				<div class=" ">
					<label for="name_khmer">{locale.T('name_khmer')}</label>
					<input
						value={get_staff?.name_khmer ?? ''}
						required
						minlength="1"
						name="name_khmer"
						type="text"
						class="form-control"
						id="name_khmer"
					/>
				</div>
			</div>
			<div class="col-md-6">
				<label for="name_latin">{locale.T('name_latin')}</label>
				<input
					required
					value={get_staff?.name_latin ?? ''}
					name="name_latin"
					type="text"
					class="form-control"
					id="name_latin"
				/>
			</div>
		</div>
		<div class="row mb-3">
			<div class="col-md-6">
				<label for="telephone">{locale.T('contact')}</label>
				<input
					value={get_staff?.telephone ?? ''}
					name="telephone"
					type="text"
					class="form-control"
					id="telephone"
				/>
			</div>
			<div class="col-md-6">
				<label for="base_salary">{locale.T('base_salary')}</label>
				<CurrencyInput
					name="base_salary"
					amount={get_staff?.base_salary}
					symbol={get_currency?.currency}
				/>
				
			</div>
		</div>
		<div class="mb-3">
			<label for="exampleInputFile">{locale.T('picture')}</label>
			<CropImage
				name="file"
				default_image={get_staff?.uploads?.filename ?? ''}
				related_id={get_staff?.id}
				related_type_="staff"
			/>
		</div>

		<div class="mb-3">
			<label for="signature">{locale.T('signature')}</label>
			<CropImage
				name="file"
				default_image={get_staff?.sign?.filename ?? ''}
				related_id={get_staff?.id}
				related_type_="staffSign"
			/>
		</div>

		<div class="row mb-3">
			<div class="col-md-12">
				<label for="specialist">{locale.T('specific')}</label>
				<textarea
					rows="3"
					class="form-control"
					value={get_staff?.specialist}
					name="specialist"
					id="specialist"
				></textarea>
			</div>
		</div>
		<div class="row pb-3">
			<div class="col-md-3">
				<div class=" ">
					<label for="gender">{locale.T('gender')}</label>
					<select
						required
						value={get_staff?.gender ?? ''}
						name="gender"
						class="form-control"
						id="gender"
					>
						<option value="Other">{locale.T('none')}</option>
						<option value="Male">{locale.T('male')}</option>
						<option value="Female">{locale.T('female')}</option>
					</select>
				</div>
			</div>
			<div class="col-md-3">
				<div class=" ">
					<label for="blood_group">{locale.T('blood_group')}</label>
					<select
						value={get_staff?.blood_group ?? ''}
						name="blood_group"
						class="form-control"
						id="blood_group"
					>
						<option value="">Other</option>

						<option value="A-">A-</option>
						<option value="A+">A+</option>

						<option value="AB+">AB+</option>
						<option value="AB-">AB-</option>

						<option value="B-">B-</option>
						<option value="B+">B+</option>

						<option value="O-">O-</option>
						<option value="O+">O+</option>
					</select>
				</div>
			</div>
			<div class="col-md-6">
				<div class=" ">
					<label for="dob">{locale.T('dob')}</label>
					<input value={get_staff?.dob} name="dob" type="date" class="form-control" id="dob" />
				</div>
			</div>
		</div>
		<div class="row pb-3">
			<div class="col-md-3">
				<div class=" ">
					<label for="datetime_start">{locale.T('start_working')}</label>
					<input
						value={YYYYMMDD_Format.datetime(get_staff?.datetime_start)}
						name="datetime_start"
						type="datetime-local"
						class="form-control"
						id="datetime_start"
					/>
				</div>
			</div>
			<div class="col-md-3">
				<div class=" ">
					<label for="datetime_stop">{locale.T('stop_working')}</label>
					<input
						value={YYYYMMDD_Format.datetime(get_staff?.datetime_stop)}
						name="datetime_stop"
						type="datetime-local"
						class="form-control"
						id="datetime_stop"
					/>
				</div>
			</div>
			<div class="col-md-6">
				<div class=" ">
					<label for="id_staff">{locale.T('id')} {locale.T('staff')} </label>
					<input
						value={get_staff?.id_staff}
						name="id_staff"
						type="text"
						class="form-control"
						id="id_staff"
					/>
				</div>
			</div>
		</div>
		<Address
			data={{ get_conmunies, get_districts, get_provinces, get_vilages }}
			defaultValue={{
				province_id: get_staff?.provice?.id ?? null,
				district_id: get_staff?.district?.id ?? null,
				commune_id: get_staff?.commune?.id ?? null,
				village_id: get_staff?.village?.id ?? null
			}}
		/>
		<div class="float-end">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
<br />
