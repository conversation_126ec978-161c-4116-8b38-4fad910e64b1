<script lang="ts">
	import InputDocument from '$lib/coms-form/InputDocument.svelte';
	import type { TDocumentSetting } from '$lib/type';
	interface Prop {
		title_khm: string;
		title_eng: string;
		logo: string;
		get_document_setting?: TDocumentSetting;
		n: string;
	}
	let { get_document_setting, logo, title_eng, title_khm, n }: Prop = $props();
</script>

<div
	style="font-size: {get_document_setting?.header_size}px;color: {get_document_setting?.header_color};"
	class="row"
>
	<h3 class="kh_font_muol_light text-center pb-1">ព្រះរាជាណាចក្រកម្ពុជា</h3>
	<h3 class="kh_font_muol_light text-center">ជាតិ សាសនា ព្រះមហាក្សត្រ</h3>
	<h1 class="text-center tacteng">3</h1>
</div>
<div class="row">
	<div class="col-10">
		<div class="row">
			<div class="col-auto">
				<img class="mb-2" style="height: {get_document_setting?.logo_size}px" src={logo} alt="" />
			</div>
			<div class="col">
				<h4
					style="font-size: {get_document_setting?.clinic_title_kh_size}px;color: {get_document_setting?.clinic_title_kh_color};"
					class="kh_font_muol_light mb-2"
				>
					{title_khm}
				</h4>
				<h5
					style="font-size: {get_document_setting?.clinic_title_en_size}px;color: {get_document_setting?.clinic_title_en_color};"
					class="en_font_times_new_roman mb-2"
				>
					{title_eng}
				</h5>
				<div style="color: {get_document_setting?.text_body_color}" class="fs-5">
					លេខ <InputDocument
						style="color: {get_document_setting?.text_input_color}"
						value={n}
						name="n"
						width="130px"
						type="text"
					/>
				</div>
			</div>
		</div>
	</div>
</div>
<br />

