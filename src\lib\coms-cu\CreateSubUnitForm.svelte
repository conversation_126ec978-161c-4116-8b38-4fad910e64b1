<script lang="ts">
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	interface Unit {
		id: number;
		unit: string | null;
		vaccine_dose: string | null;
		product_group_type_id: number | null;
	}
	interface Props {
		units: Unit[];
		unit_id: number;
		main_unit_id: number | null | undefined;
		qty?: number;
		price?: number;
	}

	let { units, unit_id = $bindable(), main_unit_id, qty = 0, price = 0 }: Props = $props();
	let find_unit = $derived(units.find((e) => e.id === unit_id));
	let find_main_unit = $derived(units.find((e) => e.id === main_unit_id));
</script>

<div class="row">
	<div class="col-4">
		<div class=" pb-3">
			<label for="unit_id_per_unit">{locale.T('unit')} / {find_unit?.unit ?? ''}</label>
			<div class="input-group">
				<SelectParam
					bind:value={unit_id}
					name="unit_id_per_unit"
					items={units.map((e) => ({ id: e.id, name: e.unit }))}
				/>
			</div>
		</div>
	</div>
	<div class="col-4">
		<div class=" pb-3">
			<label for="price_per_unit">{locale.T('price')} / {find_unit?.unit ?? ''} </label>
			<input
				value={price}
				name="price_per_unit"
				type="text"
				class="form-control"
				id="price_per_unit"
			/>
		</div>
	</div>
	<div class="col-4">
		<div class=" pb-3">
			<label for="qty_per_unit"
				>{locale.T('qty')} / {find_unit?.unit ?? ''} / {find_main_unit?.unit}
			</label>
			<div class="input-group">
				<input
					class="form-control"
					value={qty}
					name="qty_per_unit"
					type="number"
					id="qty_per_unit"
				/>
			</div>
		</div>
	</div>
</div>
